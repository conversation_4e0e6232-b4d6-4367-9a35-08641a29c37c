using System;
using System.Collections;
using System.Collections.Generic;

namespace Music.ACM
{
    public enum ConsistentType
    {
        AllTrace    = 0,
        Children    = 1,
        Parent      = 2,
    }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public class ConsistentValueAttribute : Attribute
    {
        public ConsistentValueAttribute(ConsistentType type = ConsistentType.AllTrace)
        {
            this.Type = type;
        }

        public ConsistentType Type { get; private set; }
    }
}
