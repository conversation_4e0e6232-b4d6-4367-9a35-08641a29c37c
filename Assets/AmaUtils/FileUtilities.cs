using UnityEngine;
using System.IO;

namespace Amanotes.Utils
{
    /// <summary>
    /// Helper class to deal with every task related to files and folder
    /// </summary>
    public class FileUtilities
    {
        /// <summary>
        /// Return a path to a writable folder on a supported platform
        /// </summary>
        /// <param name="relativeFilePath">A relative path to the file, from the out most writable folder</param>
        /// <returns></returns>
        public static string GetWritablePath(string relativeFilePath)
        {
            string result = "";
#if UNITY_EDITOR
            result = Application.dataPath.Replace("Assets", "DownloadedData") + "/" + relativeFilePath;
#elif UNITY_ANDROID
		    result = Application.persistentDataPath + "/" + relativeFilePath;
#elif UNITY_IPHONE
		    result = Application.persistentDataPath + "/" + relativeFilePath;
#elif UNITY_WP8 || NETFX_CORE
		    result = Application.persistentDataPath + "/" + relativeFilePath;
#endif
            return result;
        }

        /// <summary>
        /// Read a file at specified path
        /// </summary>
        /// <param name="filePath">Path to the file</param>
        /// <param name="isAbsolutePath">Is this path an absolute one?</param>
        /// <returns>Data of the file, in byte[] format</returns>
        public static byte[] LoadFile(string filePath, bool isAbsolutePath = false)
        {
            if (filePath == null || filePath.Length == 0)
            {
                return null;
            }

            string absolutePath = filePath;
            if (!isAbsolutePath) { absolutePath = GetWritablePath(filePath); }

            if (System.IO.File.Exists(absolutePath))
            {
                return System.IO.File.ReadAllBytes(absolutePath);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Check if a file is existed or not
        /// </summary>
        public static bool IsFileExist(string filePath, bool isAbsolutePath = false)
        {
            if (filePath == null || filePath.Length == 0)
            {
                return false;
            }

            string absolutePath = filePath;
            if (!isAbsolutePath) { absolutePath = GetWritablePath(filePath); }

            return (System.IO.File.Exists(absolutePath));
        }



        /// <summary>
        /// Save a byte array to storage at specified path and return the absolute path of the saved file
        /// </summary>
        /// <param name="bytes">Data to write</param>
        /// <param name="filePath">Where to save file</param>
        /// <param name="isAbsolutePath">Is this path an absolute one or relative</param>
        /// <returns>Absolute path of the file</returns>
        public static string SaveFile(byte[] bytes, string filePath, bool isAbsolutePath = false, bool isSaveResource = false)
        {
            if (filePath == null || filePath.Length == 0)
            {
                return null;
            }
            //		if (isSaveResource) {
            //			SaveFileToResource (bytes, filePath);
            //		}
            //path to the file, in absolute format
            string path = filePath;
            if (!isAbsolutePath)
            {
                path = GetWritablePath(filePath);
            }
            //create a directory tree if not existed
            string folderName = Path.GetDirectoryName(path);
            //Debug.Log("Folder name: " + folderName);
            if (!Directory.Exists(folderName))
            {
                Directory.CreateDirectory(folderName);
            }

            //write file to storage
            File.WriteAllBytes(path, bytes);
#if UNITY_IPHONE
		    UnityEngine.iOS.Device.SetNoBackupFlag(path);
#endif
            return path;
        }


        public static string SaveFileOtherThread(byte[] bytes, string filePath)
        {
            if (filePath == null || filePath.Length == 0)
            {
                return null;
            }
            //		if (isSaveResource) {
            //			SaveFileToResource (bytes, filePath);
            //		}
            //path to the file, in absolute format
            string path = filePath;

            //create a directory tree if not existed
            string folderName = Path.GetDirectoryName(path);
            //Debug.Log("Folder name: " + folderName);
            if (!Directory.Exists(folderName))
            {
                Directory.CreateDirectory(folderName);
            }

            //write file to storage
            File.WriteAllBytes(path, bytes);
            return path;
        }


        /// <summary>
        /// Delete a file from storage using default setting
        /// </summary>
        /// <param name="filePath">The path to the file</param>
        /// <param name="isAbsolutePath">Is this file path an absolute path or relative one?</param>
        public static void DeleteFile(string filePath, bool isAbsolutePath = false)
        {
            if (filePath == null || filePath.Length == 0)
                return;

            if (isAbsolutePath)
            {
                if (System.IO.File.Exists(filePath))
                {
                    //Debug.Log("Delete file : " + absoluteFilePath);
                    System.IO.File.Delete(filePath);
                }
            }
            else
            {
                string file = GetWritablePath(filePath);
                DeleteFile(file);
            }
        }

    }
}