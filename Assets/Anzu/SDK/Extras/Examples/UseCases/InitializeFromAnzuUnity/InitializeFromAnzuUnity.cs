using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using anzu;

namespace Anzu.Examples
{
    public class InitializeFromAnzuUnity : AnzuUnity
    {
        public AppSettings AppSettings;

        private void OnEnable()
        {
            // We override AnzuCore to make sure we get the relevant creatives for Anzu examples
            if (AnzuCore.State != SDKState.Disabled)
            {
                AnzuCore.Uninitialize();
            }

            AnzuCore.Initialize(AppSettings);
        }

        private void OnDisable()
        {
            AnzuCore.Uninitialize();
        }
    }
}
