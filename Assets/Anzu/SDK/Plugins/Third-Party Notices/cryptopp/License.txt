Compilation Copyright (c) 1995-2019 by <PERSON>.  All rights reserved.
This copyright applies only to this software distribution package
as a compilation, and does not imply a copyright on any particular
file in the package.

All individual files in this compilation are placed in the public domain by
<PERSON> and other contributors.

I would like to thank the following authors for placing their works into
the public domain:

<PERSON> - 3way.cpp
<PERSON> - cast.cpp, seal.cpp
<PERSON> - cast.cpp
<PERSON> des.cpp
<PERSON> - md2.cpp, md4.cpp
<PERSON> - md5.cpp
<PERSON> - rc6.cpp
<PERSON> - rijn<PERSON>l.cpp
<PERSON> - ri<PERSON>.cpp, skipjack.cpp, square.cpp
<PERSON> - safer.cpp
<PERSON> - twofish.cpp
<PERSON> - came<PERSON>.cpp, shacal2.cpp, ttmac.cpp, whrlpool.cpp, ripemd.cpp
<PERSON><PERSON> - sha3.cpp
<PERSON>, <PERSON>, <PERSON>-<PERSON> and <PERSON> - blake2.cpp, blake2b_simd.cpp, blake2s_simd.cpp
Aaram <PERSON> - <PERSON>ria.cpp, aria_simd.cpp
<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> - sm4.cpp sm4_simd.cpp
<PERSON>, <PERSON> - <PERSON>.cpp, chacha_simd.cpp, chacha_avx.cpp
<PERSON> - ed25519, x25519, donna_32.cpp, donna_64.cpp, donna_sse.cpp

The Crypto++ <PERSON> uses portions of Andy Polyakov's CRYPTOGAMS for Poly1305
scalar multiplication, aes_armv4.S, sha1_armv4.S and sha256_armv4.S. CRYPTOGAMS
is dual licensed with a permissive BSD-style license. The CRYPTOGAMS license is
reproduced below.

The Crypto++ Library uses portions of Jack Lloyd's Botan for ChaCha SSE2 and
AVX. Botan placed the code in public domain for Crypto++ to use.

The Crypto++ Library (as a compilation) is currently licensed under the Boost
Software License 1.0 (http://www.boost.org/users/license.html).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

CRYPTOGAMS License

Copyright (c) 2006-2017, CRYPTOGAMS by <<EMAIL>>
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

* Redistributions of source code must retain copyright notices,
  this list of conditions and the following disclaimer.
* Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the following
  disclaimer in the documentation and/or other materials
  provided with the distribution.
* Neither the name of the CRYPTOGAMS nor the names of its copyright
  holder and contributors may be used to endorse or promote products
  derived from this software without specific prior written permission.
