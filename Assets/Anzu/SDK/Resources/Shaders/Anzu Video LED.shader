// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Anzu/Video LED Playback Shader"
{
    Properties {
        _ShouldFlipX ("Should Flip X", Int) = 0
        _ShouldFlipY ("Should Flip Y", Int) = 0
        _ShouldSwitchRB ("Should switch R/B", Int) = 0
        _HorizonalLeds ("Horizontal Resolution", Int) = 80.0
        _VerticalLeds ("Vertical Resolution", Int) = 60.0
        _Color ("Glow Color", Color) = (1,1,1,1)
        _Glow ("Glow Intensity", Range(0, 3)) = 1
        _LuminanceSteps ("Luma Steps", Int) = 12
        _Brightness ("Brightness", Float) = 0
        _Contrast ("Contrast", Float) = 1
        _MainTex ("Base (RGB)", 2D) = "black" { }
    }   
 
    SubShader {

        Tags { "RenderType" = "Opaque" "AnzuShaderType"="AnzuVideoLEDShader"}

        Pass {
            Fog { Mode off }
 
            CGPROGRAM
 
            #pragma vertex vert
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #include "UnityCG.cginc"
            #pragma target 3.0
 
            struct v2f
            {
                float4 pos      : POSITION;
                float2 uv       : TEXCOORD0;
                //float4 scr_pos : TEXCOORD1;
            };
 
            uniform int _ShouldFlipX;
            uniform int _ShouldFlipY;
	        uniform int _ShouldSwitchRB;
            uniform sampler2D _MainTex;
            uniform float4 _Color;
            uniform float _Glow;
            uniform float _HorizonalLeds;
            uniform float _VerticalLeds;
            uniform int _LuminanceSteps;
            uniform float _Brightness;
            uniform float _Contrast;

            
            v2f vert(appdata_img v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = MultiplyUV(UNITY_MATRIX_TEXTURE0, v.texcoord);
                //o.scr_pos = ComputeScreenPos(o.pos);
                return o;
            }
            
            float2 CRTCurveUV( float2 uv )
            {
                uv = uv * 2.0 - 1.0;
                float2 offset = abs( uv.yx ) / float2( 6.0, 4.0 );
                uv = uv + uv * offset * offset;
                uv = uv * 0.5 + 0.5;
                return uv;
            }
            
            float4 applyLuminanceStepping(float4 color)
            {                
                float sum = color.r + color.g + color.b;
                float luminance = sum/3.0;
                float3 ratios = float3(color.r/luminance, color.g/luminance, color.b/luminance);

                float luminanceStep = 1.0/float(_LuminanceSteps);
                float luminanceBin = ceil(luminance/luminanceStep);
                float luminanceFactor = luminanceStep * luminanceBin;

                //use ratios * luminanceFactor as our new color so that original color hue is maintained
                return float4(ratios * luminanceFactor,1.0); 
            }

            
            #define KERNEL_SIZE 9
            float2 texCoords[KERNEL_SIZE]; //stores texture lookup offsets from a base case

            half4 frag(v2f i): COLOR
            {
                if( _ShouldFlipX == 1 ) {
                    i.uv.x = 1.0 - i.uv.x;
                }
                if( _ShouldFlipY == 1 ) {
                    i.uv.y = 1.0 - i.uv.y;
                }
                
                float4 outcolor;
                
                float halfx = 0.5 / _HorizonalLeds;     
                float halfy = 0.5 / _VerticalLeds;      
                float thirdx = 1.0 / _HorizonalLeds / 3.0;
                float thirdy = 1.0 / _VerticalLeds / 3.0;
                
                float2 r;
                
                //find the reference pixel in a _HorizonalLeds x _VerticalLeds pixelated world
                r.x = min((floor(i.uv.x * _HorizonalLeds)),_HorizonalLeds-1)/_HorizonalLeds + halfx;
                r.y = min((floor(i.uv.y * _VerticalLeds)),_VerticalLeds-1)/_VerticalLeds + halfy;
                //outcolor = tex2D(_MainTex, r.uv); //this alone creates a mosaic effect

                //get reference point for averaging color
                texCoords[0] = r + float2( -thirdx, -thirdy );
                texCoords[1] = r + float2( 0, -thirdy );
                texCoords[2] = r + float2( thirdx, -thirdy );
                texCoords[3] = r + float2( -thirdx, 0 );
                texCoords[4] = r;
                texCoords[5] = r + float2( thirdx, 0 );
                texCoords[6] = r + float2( -thirdx, thirdy );
                texCoords[7] = r + float2( 0, thirdy );
                texCoords[8] = r + float2( thirdx, thirdy );
                
                //calculate average color
                outcolor = tex2D(_MainTex, texCoords[0]) +
                           tex2D(_MainTex, texCoords[1]) +
                           tex2D(_MainTex, texCoords[2]) +
                           tex2D(_MainTex, texCoords[3]) +
                           tex2D(_MainTex, texCoords[4]) +
                           tex2D(_MainTex, texCoords[5]) +
                           tex2D(_MainTex, texCoords[6]) +
                           tex2D(_MainTex, texCoords[7]) +
                           tex2D(_MainTex, texCoords[8]);
                //this alone creates a mosaic effect with average color
                outcolor /= float(KERNEL_SIZE);
                
                //palletize the color
                outcolor = applyLuminanceStepping(outcolor);
                
                //multiply with the distance from reference point
                float maxdist = max(0.5 / _HorizonalLeds, 0.5 / _VerticalLeds);
                float d = distance( i.uv, r );
                outcolor *= (1.0 - (d / maxdist)) * _Glow;

                //outcolor *= _Color;
                
                //apply contrast
                outcolor.rgb = ((outcolor.rgb - 0.5f) * max(_Contrast, 0)) + 0.5f;
                
                //apply brightness
                outcolor.rgb += _Brightness;


                if( _ShouldSwitchRB == 1 ) {
                    return float4(outcolor.b, outcolor.g, outcolor.r, 1.0f);	
                }   
                return float4(outcolor.r, outcolor.g, outcolor.b, 1.0f);	
            }
 
            ENDCG
        }
    }
    FallBack "Diffuse"
}