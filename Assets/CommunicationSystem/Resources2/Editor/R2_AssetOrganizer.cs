using UnityEngine;
using UnityEditor;

using System.IO;
using System.Collections;

public class R2_AssetOrganizer {

    [MenuItem("Assets/Resources2/Create Material")]
    static void CreateMaterial()
    {
        string shaderName = "Custom/MobileCharacter";

        if(Selection.activeObject == null)
        {
            Debug.LogWarning("Should select textures for materials to be create");
            return;
        }

        if (Selection.activeObject == null)
        {
            Debug.LogWarning("Should select objects to distribute into folders");
            return;
        }

        var shader = Shader.Find(shaderName);

        foreach (var guid in Selection.assetGUIDs)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid).Replace("\\", "/");
            var path = getPathWithoutExtension(assetPath);
	        //var name = path.Substring(path.LastIndexOf("/"));

            var m = new Material(shader);
            m.mainTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
            AssetDatabase.CreateAsset(m, path + ".mat");
        }

        EditorUtility.UnloadUnusedAssetsImmediate();
    }
    
    [MenuItem("Assets/Resources2/Distribute to Folder")]
    static void DistributeToFolder()
    {
        if (Selection.activeObject == null)
        {
            Debug.LogWarning("Should select objects to distribute into folders");
            return;
        }

        foreach (var guid in Selection.assetGUIDs)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid).Replace("\\", "/");
            var path = getPathWithoutExtension(assetPath);
            var name = assetPath.Substring(assetPath.LastIndexOf("/"));

            if (!Directory.Exists(path)) Directory.CreateDirectory(path);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            AssetDatabase.MoveAsset(assetPath, path + name);
        }
    }

    [MenuItem("Assets/Resources2/Add asset suffix")]
    static void AddAssetSuffix()
    {
        if (Selection.activeObject == null)
        {
            Debug.LogWarning("Should select objects to distribute into folders");
            return;
        }

        foreach (var guid in Selection.assetGUIDs)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var extension = assetPath.Substring(assetPath.LastIndexOf(".") + 1);
            var obj = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            var objT = obj.GetType();
            
            string suffix = null;

            if (string.IsNullOrEmpty(extension)) continue;
            
            if (objT == typeof(Material))
            {
                suffix = "-mat";
            } else if (objT == typeof(Texture2D))
            {
                suffix = "-tex";
            } else if (objT == typeof(GameObject))
            {
                if (!assetPath.EndsWith(".prefab"))
                {
                    suffix = "-" + extension.ToLower();
                }
            } else if (objT == typeof(Animation))
            {
                suffix = "-anim";
            }

            if (!string.IsNullOrEmpty(suffix))
            {
                var newName = assetPath.Replace("." + extension, suffix + "." + extension.ToLower());
                File.Move(assetPath, newName);
                File.Move(assetPath + ".meta", newName + ".meta");
            }
        }

        AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
    }


    static string getPathWithoutExtension(string path)
    {
        path = path.Replace("\\", "/");
        var dotIndex = path.IndexOf(".");
        return dotIndex != -1 ? path.Substring(0, dotIndex) : path;
    }
}
