using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using WWW = vietlabs.WebRequest.WWW;
public enum R2_ProcessStatus
{
    Idle,
    Validate,   // check validator wether this can start ?
    Start,

    Download,   // self progress
    Dependency, // downloading dependencies

    Wait4Retry, // error but can retry
    Error,      // fatal error

	Cancel,     		// user cancelled
    Complete,   		// sucessfully loaded
}

//public class R3_Statistic
//{
//    public int retry;
//    public int maxRetry;
//    public int retryTimeout;

//    public float stLoadTime;
//    public float edLoadTime;
//}


public class R2_Process : IEnumerator
{
	//internal string id;      

    internal string error;
    internal object loadedData;

    internal R2_ProcessStatus _status;

    internal ulong _wSelfLoaded;
    internal ulong _wSelfTotal;
    internal ulong _wLoaded;
    internal ulong _wTotal;

	internal Action _onStart;
	
    internal Action<float> _onProgress;
    internal Action<string> _onError;
    internal Action<object> _onComplete;

    internal virtual void OnChildrenStatus(R2_Process d) {}

    internal void CalculateProgress()
    {
        _wLoaded = _wSelfLoaded;
        _wTotal = _wSelfTotal;

        foreach (var item in _children)
        {
            var d = item.Key;
            _wLoaded += d.wLoaded;
            _wTotal += d.wTotal;
        }
    }

    internal virtual void BubbleDependencyStatus()
	{
        foreach (var item in _parents)
        {
            item.OnChildrenStatus(this);
        }
    }
    
    internal virtual void TriggerOnStart()
    {
        if (_status >= R2_ProcessStatus.Start)
        {
            Debug.LogWarning("TriggerOnStart() - Something wrong ! Invalid status <" + _status + ">");
            return;
        }

        status = R2_ProcessStatus.Start;
        if (_onStart != null) _onStart();
        BubbleDependencyStatus();
    }

    internal float _lastProgressTime = 0;

    public virtual void TriggerOnProgress(bool calculate, float minDelay = 0)
    {
        var realTime = Time.realtimeSinceStartup;
        
        if (minDelay > 0 && _lastProgressTime > 0 && (realTime - _lastProgressTime < minDelay))
        {
            //triggering OnProgress too frequent : block !
            return;
        }

        if (calculate) CalculateProgress();
        _lastProgressTime = realTime;
        if (_onProgress != null) _onProgress(progress);

        BubbleDependencyStatus();
    }

    internal virtual void TriggerOnError(string message)
    {
        if (!isProgress)
        {
            Debug.LogWarning("TriggerOnError() - Something wrong ! Invalid status <" + _status + "> : " + message);
            return;
        }

        error = message;
        status = R2_ProcessStatus.Error;
        if (_onError != null) _onError(message);
        BubbleDependencyStatus();
    }         
	
    internal virtual void TriggerOnComplete(object loaded)
    {
        if (!isProgress)
        {
            Debug.LogWarning("TriggerOnComplete() - Something wrong ! Invalid status <" + _status + ">");
            return;
        }

        this.loadedData = loaded;
        status = R2_ProcessStatus.Complete;
        if (_onComplete != null) _onComplete(loaded);
        BubbleDependencyStatus();
    }

    // ------------------------------------------------

    internal bool _locked; // easy circular dependency check
    internal HashSet<R2_Process> _parents = new HashSet<R2_Process>();
    internal Dictionary<R2_Process, bool> _children = new Dictionary<R2_Process, bool>(); // Process -> Important

    internal bool HasCircularDependency
    {
        get
        {
            if (_locked)
            {
                return true;
            }

            _locked = true;
            {
                foreach (var item in _parents)
                {
                    if (item.HasCircularDependency) return true;
                }
            }
            _locked = false;

            return false;
        }
    }

    public void AddDependency(R2_Process d, bool important)
    {
        if (d == this)
        {
            Debug.LogWarning("Something wrong ! Can not add self as dependency");
            return;
        }

        if (_children.ContainsKey(d))
        {
            Debug.LogWarning(string.Format("Already added \n{0}\n as a dependency of \n{1}", d, this));
            return;
        }

        // Simple A -> B -> A dependency check
        if (d._children.ContainsKey(this))
        {
            Debug.LogWarning(string.Format("Already added \n{0}\n as a dependency of \n{1}", d, this));
            return;
        }

        var oLoaded = _wLoaded;
        var oTotal = _wTotal;
        _children.Add(d, important);
        d._parents.Add(this);

        // NOTE : check progress IMMEDIATELY to detect circular dependency!
        if (HasCircularDependency)
        {
            Debug.LogWarning("Circular dependency found between R3_Processes \n" + this + "\n" + d);

            // Undo adding dependencies !
            _wLoaded = oLoaded;
            _wTotal = oTotal;
            _children.Remove(d);
            d._parents.Remove(this);
        };
    }
    
    // ------------------------------------------------

    public float progress { get { return _wTotal == 0 ? 0 : (_wLoaded / (float)_wTotal); }}
    public ulong wLoaded { get { return _wLoaded; } }
    public ulong wTotal { get { return _wTotal; } }

    public R2_ProcessStatus status
    {
        get { return _status; }
        internal set
        {
            _status = value;

            foreach (var p in _parents)
            {
                p.OnChildrenStatus(this);
            }
        }
    }

    virtual public bool isProgress { get { return _status == R2_ProcessStatus.Download || _status == R2_ProcessStatus.Dependency; }}
    virtual public bool isComplete { get { return _status == R2_ProcessStatus.Complete; }}
	virtual public bool isError { get { return _status == R2_ProcessStatus.Error; }}
	
    virtual public bool isDone { get { return
        _status == R2_ProcessStatus.Error     ||
        _status == R2_ProcessStatus.Complete  ||
        _status == R2_ProcessStatus.Cancel;
     }}
                                                        
    // ------------------------------------------------

    object IEnumerator.Current { get { return null; } }
    void IEnumerator.Reset() { }
    bool IEnumerator.MoveNext() {
	    //Debug.Log("MoveNext ---> " + this + ":" + isDone);
        return !isDone;
    }

    // ------------------------------------------------
           
    //internal string[] ListernerIds
    //{
    //    get
    //    {
    //        var counter = 0;
    //        var result = new string[_parents.Count];
    //        foreach (var item in _parents)
    //        {
    //            result[counter++] = item.id;
    //        }
    //        return result;
    //    }
    //}

    //internal string[] DependencyIds
    //{
    //    get
    //    {
    //        var counter = 0;
    //        var result = new string[_children.Count];
    //        foreach (var item in _children)
    //        {
    //            result[counter++] = item.Key.id;
    //        }
    //        return result;
    //    }
    //}

    public override string ToString()
    {
        return string.Format("[{0} id={1} status={2} wLoaded={3} wTotal={4} listeners=[{5}] dependencies=[{6}] errorMessage={7} loadedData={8}", 
	        GetType(), "UnknownID", _status,
	        wLoaded, wTotal,
	        _parents.Count,
	        _children.Count,
            //string.Join(",", ListernerIds),
            //string.Join(",", DependencyIds),
	        error, loadedData
        );
    }   
}