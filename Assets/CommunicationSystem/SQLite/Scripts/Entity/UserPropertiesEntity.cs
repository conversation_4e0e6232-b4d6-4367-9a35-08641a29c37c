using System.Threading;

namespace AmaSQLite
{
    public class UserPropertiesEntity
    {
        private long id;
        public string key;
        public string value;

        public static long userPropertiesGlobalId;

        public UserPropertiesEntity() { }

        public UserPropertiesEntity(string key, string value)
        {
            id = Interlocked.Increment(ref userPropertiesGlobalId);
            this.key = key;
            this.value = value;
        }
    }
}