using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using UnityEngine;

namespace AmaSQLite
{
    public class MigratedUniqueSongEnd : MigratedEvent
    {
        public MigratedUniqueSongEnd()
        {
            eventName = "unique_song_end";
        }

        public override void Migrate()
        {
            /*
            List<Dictionary<string, object>> eventsParam = new List<Dictionary<string, object>>();
            List<string> songUnlockInDB = GetAllInDB();
            Dictionary<string, PlayerSongModel> songDatas = PlayerDataService.Instance.GetPlayerData().SongData;
            Dictionary<string, object> param;
            foreach (KeyValuePair<string, PlayerSongModel> item in songDatas)
            {
                PlayerSongModel song = item.Value;
                if (song.star >= 3 && !songUnlockInDB.Contains(song.acmId) && !songUnlockInDB.Contains(song.id.ToString()))
                {
                    param = new Dictionary<string, object>();
                    param.Add("song_id", song.id);
                    param.Add("song_acm_id", song.acmId);
                    eventsParam.Add(param);
                }
            }
            InsertEventIntoDB(eventsParam);
            */
        }
    }
}

