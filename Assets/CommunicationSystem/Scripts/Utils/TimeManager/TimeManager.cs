using Inwave;
using Newtonsoft.Json;
using PianoIdol;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;

public class TimeManager : SingletonMono<TimeManager> {
	private const string PREF_CHEAT_TIME = "isCheat_ForceUseSystemTime";

	private static bool? isCheat_ForceUseSystemTime;
	public static bool IsCheat_ForceUseSystemTime
	{
		get
		{
			if (!isCheat_ForceUseSystemTime.HasValue)
			{
				isCheat_ForceUseSystemTime = PlayerPrefs.GetInt(PREF_CHEAT_TIME, 0) == 1;
			}
			return isCheat_ForceUseSystemTime.Value;
		}
		set
		{
			isCheat_ForceUseSystemTime = value;
			PlayerPrefs.SetInt(PREF_CHEAT_TIME, value ? 1 : 0);
			PlayerPrefs.Save();
		}
	}

    private GetTimeAPI[] getTimeAPIs = new GetTimeAPI[2]
    {
        new WorldTimeAPI(),
        new WorldTimeServer()
    };

	private int currentAPIIndex = 0;
	private const float updateInterval = 60f;

	private float timeSinceGotUserTime;

	private DateTime gameTime = DateTime.Now;
	public DateTime GameTime
	{
		get
		{
			return GetGameTime() + ExtraTime;
		}
		private set
		{
			gameTime = value;
			timeSinceGotUserTime = Time.realtimeSinceStartup;
		}
    }

    public TimeSpan ExtraTime {
		get
		{
			string json = PlayerPrefs.GetString("extraTime", JsonConvert.SerializeObject(TimeSpan.Zero));
			TimeSpan timeSpan = JsonConvert.DeserializeObject<TimeSpan>(json);
			return timeSpan;
		}
		set
		{
			PlayerPrefs.SetString("extraTime", JsonConvert.SerializeObject(value));
			PlayerPrefs.Save();
		}
	}

	private float timeSinceGotUserUtcTime;
	private DateTime utcGameTime = DateTime.UtcNow;
	public DateTime UtcGameTime
    {
		get
        {
			return GetUtcGameTime();
        }
		private set
        {
			utcGameTime = value;
			timeSinceGotUserUtcTime = Time.realtimeSinceStartup;
		}
    }

    public EventHandler<DateTime> OnNewDay;
	private DateTime? lastDateCheck;

	public static float timeStartSubscriptionVIP = 0f;
	public static float timeEndSubscriptionVIP = 0f;

	#region Time in-app without paused time

	private static bool _pauseStatus;
	private static float _pausedTime;
	private static float _totalOutsideDuration;
	public static event Action saveDataOnPauseQuit;
	public void HandleOnGameLoaded() {
		if (_totalOutsideDuration > 0) return;
		_totalOutsideDuration = Time.realtimeSinceStartup;
	}

	#region Handle App Pause Quit

	public void HandleOnApplicationPause(bool pauseStatus) {
		if (PlayerPrefs.HasKey(DevInfo.IS_RESET_KEY)) {
			return;
		}
		
		if (_pauseStatus == pauseStatus)
			return;
		
		if (pauseStatus) {
			_pausedTime = Time.realtimeSinceStartup;
			saveDataOnPauseQuit?.Invoke();
		} else {
			_totalOutsideDuration += Time.realtimeSinceStartup - _pausedTime;
		}
		_pauseStatus = pauseStatus;
	}

	public void HandleOnApplicationFocus(bool hasFocus) {
		if (PlayerPrefs.HasKey(DevInfo.IS_RESET_KEY)) {
			return;
		}
		
		if (_pauseStatus != hasFocus)
			return;
		
		if (hasFocus) {
			_totalOutsideDuration += Time.realtimeSinceStartup - _pausedTime;
		} else {
			_pausedTime = Time.realtimeSinceStartup;
			saveDataOnPauseQuit?.Invoke();
		}
		_pauseStatus = !hasFocus;
	}

	public static void HandleOnUserExit() {
		saveDataOnPauseQuit?.Invoke();
	}

	#endregion
	
	public static float TimeIngameIgnorePause {
		get {
			if (_pauseStatus) {
				return _pausedTime - _totalOutsideDuration;
			}
			return Time.realtimeSinceStartup - _totalOutsideDuration;
		}
	}

	#endregion
	private DateTime GetGameTime()
	{
		float secsSinceLastGet = Time.realtimeSinceStartup - timeSinceGotUserTime;
		DateTime result = gameTime;
		result = result.AddSeconds(secsSinceLastGet);
		return result;
	}

	private DateTime GetUtcGameTime()
	{
		float secsSinceLastGet = Time.realtimeSinceStartup - timeSinceGotUserUtcTime;
		DateTime result = utcGameTime;
		result = result.AddSeconds(secsSinceLastGet);
		return result;
	}

	private void Awake()
	{
		StartCoroutine(RequestTimeService());
		StartCoroutine(CheckDatePass());
	}

    private IEnumerator RequestTimeService()
	{
		WaitForSeconds updateInterval = new WaitForSeconds(TimeManager.updateInterval);
		while (true)
		{
			if (IsCheat_ForceUseSystemTime)
			{
				HandleCheatLocalTimeResponse();
			}
			else
			{
				GetTimeAPI getTimeAPI = getTimeAPIs[currentAPIIndex];
				string api = getTimeAPI.GetAPI();
				UnityWebRequest request = UnityWebRequest.Get(api);
				request.certificateHandler = new BypassCertificate();
				request.downloadHandler = new DownloadHandlerBuffer();
				yield return request.SendWebRequest();
				if (request.isHttpError || request.isNetworkError)
				{
					string msg = "TimeManager request error: " + request.error;
					Debug.LogError(msg);

					//Firebase.Crashlytics.Crashlytics.Log(msg);
					SwitchAPI();
				}
				else
				{
					HandleLocalTimeResponse(getTimeAPI, request.downloadHandler.text);
				}
			}
			yield return updateInterval;
		}
	}

	private void SwitchAPI()
    {
		if (currentAPIIndex < getTimeAPIs.Length - 1)
        {
			currentAPIIndex++;
        }
		else
        {
			currentAPIIndex = 0;
		}
    }

	private void HandleLocalTimeResponse(GetTimeAPI getTimeAPI, string response)
	{
		GameTime = getTimeAPI.HandleResponse(response);
		if (!lastDateCheck.HasValue)
			lastDateCheck = GameTime.Date;
		DateTimeOffset dateTimeOffset = DateTime.SpecifyKind(GameTime, DateTimeKind.Local);
		UtcGameTime = GameTime.Subtract(dateTimeOffset.Offset);
	}

	private void HandleCheatLocalTimeResponse()
	{
		GameTime = DateTime.Now;
		if (!lastDateCheck.HasValue)
			lastDateCheck = GameTime.Date;
	}

	private IEnumerator CheckDatePass()
	{
		while (true)
		{
			if (lastDateCheck.HasValue && GameTime.Date > lastDateCheck)
			{
				lastDateCheck = GameTime.Date;
				OnNewDay?.Invoke(this, GameTime);
			}
			yield return new WaitForSecondsRealtime(1f);
		}
	}

	public DateTime GetEndOfDay()
	{
		DateTime endOfDay = new DateTime(GameTime.Year, GameTime.Month, GameTime.Day).AddDays(1).Subtract(new TimeSpan(0, 0, 0, 0, 1));
		return endOfDay;
	}

	class BypassCertificate : CertificateHandler
	{
		protected override bool ValidateCertificate(byte[] certificateData)
		{
			return true;
		}
	}
}
