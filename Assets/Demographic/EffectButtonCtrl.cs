using UnityEngine;
using DG.Tweening;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class EffectButtonCtrl : MonoBehaviour, IPointerDownHandler { //, IPointerClickHandler, IPointerExitHandler {
    [System.Serializable]
    public class ScaleOption {
        public Vector3 fixedValue;
        public bool    useFixedValue;
    }

    [SerializeField] Transform objBtnScale;
    [SerializeField] Graphic   graphicColor;
    [SerializeField] float     duration = 0.2f;

    public ScaleOption scaleOption;

    //public 
    // Use this for initialization
    Vector3                currentScale;
    [SerializeField] bool  useConstantAlpha = false;
    [SerializeField] float currentA         = 1f;

    public Vector3 scaleAdd = new Vector3(-0.1f, -0.1f, 0);

    private void Awake() {
        Init();
    }

    private bool _isTweenButton;

    public void OnMyPress() {
        if (_isTweenButton) {
            return;
        }

        _isTweenButton = true;

        Vector3 nextScale = currentScale + scaleAdd;
        //objBtnScale.DOKill();
        objBtnScale.DOScale(nextScale, duration).PlayForward();
        OnMyEnter();

        DOVirtual.DelayedCall(duration, OnMyReturn);
    }

    public void OnMyReturn() {
        //objBtnScale.DOKill();
        objBtnScale.DOScale(currentScale, duration).OnComplete(() => {
            _isTweenButton = false;
            //
        }).PlayForward();
        OnMyExit();
    }

    public void OnMyEnter() {
        if (graphicColor == null)
            return;

        //graphicColor.DOKill();
        graphicColor.DOFade(currentA / 2, duration).PlayForward();
    }

    public void OnMyExit() {
        if (graphicColor == null)
            return;

        //graphicColor.DOKill();
        graphicColor.DOFade(currentA, duration).PlayForward();
    }

    public void OnPointerDown(PointerEventData eventData) {
        OnMyPress();
    }

    // public void OnPointerClick(PointerEventData eventData) {
    //     // Debug.LogWarning("OnPointerClick");
    //     // OnMyReturn();
    // }

    // public void OnPointerExit(PointerEventData eventData) {
    //     //Debug.LogWarning("OnPointerExit");
    //     //OnMyReturn();
    // }

    public void Reset() {
#if UNITY_EDITOR
        Init();
#endif
    }

    private void Init() {
        if (objBtnScale == null) {
            objBtnScale = transform;
        }

        if (scaleOption != null && scaleOption.useFixedValue) {
            currentScale = scaleOption.fixedValue;
        } else {
            currentScale = objBtnScale.localScale;
        }

        if (graphicColor == null)
            graphicColor = transform.GetComponent<Graphic>();
        if (!useConstantAlpha && graphicColor != null) {
            currentA = graphicColor.color.a;
        }
    }

    public void UpdateCurrentAlpha(float alpha) {
        currentA = alpha;
    }
}