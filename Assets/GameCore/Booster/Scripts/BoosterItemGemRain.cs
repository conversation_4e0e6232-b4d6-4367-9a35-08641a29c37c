using System;

namespace TileHop.Cores.Boosters {
    [Serializable]
    public class BoosterItemGemRain : BoosterItem {
        public BoosterItemGemRain() {
            type = BoosterType.GemRain;
            amount = BoosterStorage.GetItemAmount(type);
            starToUnlock = RemoteConfigBase.instance.Booster_GemRain_StarUnlock;
            songStartToUnlock = RemoteConfigBase.instance.Booster_GemRain_SongStartUnlock;
        }

        public override void UpdateState(int song_start, int star) {
            isLocked = song_start < songStartToUnlock || star < starToUnlock;
            base.UpdateState(song_start, star);
        }

        public override void ApplyOnboardResult() {
            AddAmount("unlock_popup", RemoteConfigBase.instance.Booster_GemRain_Onboard_Gift);
            base.ApplyOnboardResult();
        }

        public override int GetPriceExchange() {
            return RemoteConfigBase.instance.Booster_GemRain_PriceExchange;
        }
    }
}