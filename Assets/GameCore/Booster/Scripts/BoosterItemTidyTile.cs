namespace TileHop.Cores.Boosters {
    public class BoosterItemTidyTile : BoosterItem {
        public BoosterItemTidyTile() {
            type = BoosterType.TileTidy;
            amount = BoosterStorage.GetItemAmount(type);
            starToUnlock = RemoteConfigBase.instance.Booster_TidyTile_StarUnlock;
            songStartToUnlock = RemoteConfigBase.instance.Booster_TidyTile_SongStartUnlock;
        }

        public override void UpdateState(int song_start, int star) {
            isLocked = song_start < songStartToUnlock || star < starToUnlock;
            base.UpdateState(song_start, star);
        }

        public override void ApplyOnboardResult() {
            AddAmount("unlock_popup", RemoteConfigBase.instance.Booster_TidyTile_Onboard_Gift);
            base.ApplyOnboardResult();
        }

        public override int GetPriceExchange() {
            return RemoteConfigBase.instance.Booster_TidyTile_PriceExchange;
        }
    }
}