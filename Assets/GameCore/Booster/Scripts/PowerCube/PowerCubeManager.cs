using System;
using System.Collections.Generic;
using DG.Tweening;
using Sirenix.OdinInspector;
using TileHop.Cores.Boosters;
using UnityEngine;

public class PowerCubeManager : FastSingleton<PowerCubeManager>, FeatureService {
    public static bool isEnable = false;
    public static bool isActive = false; // active when feature is available

    public const    string KEY_FEATURE             = "Power Cube";
    private const   string KEY_FEATURE_STREAK      = "powercube_streak";
    private const   string KEY_FEATURE_ONBOARD     = "powercube_streak_onboard";
    private const   string KEY_FEATURE_ACTIVE_TIME = "powercube_active_time";
    public readonly int    maxStreak               = 3;

    private                              float             _timeActivePerStreak;
    [ShowInInspector, ReadOnly] private  DateTime          _limitTime;
    [ShowInInspector, ReadOnly] internal bool              _isLocked = true;
    [ShowInInspector, ReadOnly] private  int               _currentStreak;
    [ShowInInspector, ReadOnly] private  List<BoosterType> _reward1;
    [ShowInInspector, <PERSON>Only] private  List<BoosterType> _reward2;
    [ShowInInspector, ReadOnly] private  List<BoosterType> _reward3;

    private Tween _tweenCountDown;
    public static event Action OnChangeState;
    public int streak => _currentStreak;
    private bool _inOnboarding;

    public void Init() {
        isEnable = RemoteConfigBase.instance.PowerCube_IsEnable;

        if (!isEnable) {
            return;
        }

        _timeActivePerStreak = RemoteConfigBase.instance.PowerCube_ActiveTime_Minute;
        if (_timeActivePerStreak <= 0) {
            _timeActivePerStreak = 15;
        }

        UpdateStatus();
        if (_isLocked) {
            return;
        }

        _currentStreak = PlayerPrefs.GetInt(KEY_FEATURE_STREAK, 0);
        if (_currentStreak != 0) {
            if (PlayerPrefs.HasKey(KEY_FEATURE_ACTIVE_TIME)) {
                string savedDate = PlayerPrefs.GetString(KEY_FEATURE_ACTIVE_TIME);
                if (long.TryParse(savedDate, out long ticks)) {
                    _limitTime = new DateTime(ticks);
                    if (DateTime.Now > _limitTime) {
                        var totalMinutes = (DateTime.Now - _limitTime).TotalMinutes;
                        int lostStar = (int) (totalMinutes / _timeActivePerStreak) + 1;
                        int beforeStreak = _currentStreak;

                        _currentStreak -= lostStar;
                        _limitTime += lostStar * TimeSpan.FromMinutes(_timeActivePerStreak);
                        if (_currentStreak <= 0) {
                            ResetStreak(); //reset when too far from the time active
                        } else {
                            var remain = _limitTime - DateTime.Now;
                            _tweenCountDown = DOVirtual.DelayedCall((float) remain.TotalSeconds, ReduceStreak);
                            OnChangeState?.Invoke();
                        }

                        PowerCubeTracking.StreakLose("by_time_up", beforeStreak, _currentStreak);
                    } else {
                        var remain = _limitTime - DateTime.Now;
                        _tweenCountDown = DOVirtual.DelayedCall((float) remain.TotalSeconds, ReduceStreak);
                        OnChangeState?.Invoke();
                    }
                } else {
                    ResetStreak(); //reset when wrong data before
                }
            } else {
                ResetStreak(); //reset when no data before
            }
        }
    }

    public static void SetActive(bool active) {
        isActive = isEnable && active;
    }

    public static int GetCurrentStreak() {
        if (!isEnable)
            return 0;
        if (!isInstanced)
            return 0;
        if (instance._isLocked)
            return 0;

        return instance._currentStreak;
    }

    public static void GameComplete(bool isWin) {
        if (!isEnable)
            return;
        if (!isInstanced)
            return;

        if (instance._isLocked) {
            instance.UpdateStatus();
            if (!instance._isLocked) {
                instance.ResetStreak(); //reset when first unlock
            }
        }

        if (instance._isLocked)
            return;

        instance.UpdateStreak(isWin);
    }

    private void UpdateStreak(bool isWin) {
        if (isWin) {
            _currentStreak++;
            if (_currentStreak > maxStreak) {
                _currentStreak = maxStreak;
            }

            PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
            ResetTimeActive(); //when increase streak
            OnChangeState?.Invoke();
            PowerCubeTracking.StreakKeep(_currentStreak);
        } else {
            PowerCubeTracking.StreakLose("by_song_fail", _currentStreak, 0);
            ResetStreak(); //reset when lose
        }

        Logger.EditorLog(KEY_FEATURE, $"Update streak : {_currentStreak}");
    }

    private void ResetTimeActive() {
        _limitTime = DateTime.Now + TimeSpan.FromMinutes(_timeActivePerStreak);
        PlayerPrefs.SetString(KEY_FEATURE_ACTIVE_TIME, _limitTime.Ticks.ToString());
        _tweenCountDown?.Kill();
        _tweenCountDown = DOVirtual.DelayedCall(_timeActivePerStreak * 60, ReduceStreak);
    }

    private void ReduceStreak() {
        _currentStreak -= 1;
        if (_currentStreak < 0) {
            _currentStreak = 0;
            PlayerPrefs.DeleteKey(KEY_FEATURE_ACTIVE_TIME);
        } else {
            ResetTimeActive(); //when decrease streak
        }

        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
        OnChangeState?.Invoke();
    }

    private void ResetStreak() {
        _currentStreak = 0;
        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
        PlayerPrefs.DeleteKey(KEY_FEATURE_ACTIVE_TIME);
        OnChangeState?.Invoke();
    }

    private void UpdateStatus() {
        if (!_isLocked)
            return;

        int song_start = UserProperties.GetPropertyInt(UserProperties.song_start);
        int star = GameController.instance
            ? GameController.instance.GetTotalStar()
            : Configuration.instance.GetCurrentStars();
        _isLocked = song_start < RemoteConfigBase.instance.PowerCube_Unlock_SongStart ||
                    star < RemoteConfigBase.instance.PowerCube_Unlock_Star;

    }

    public List<BoosterType> GetRewardStreak() {
        return GetRewardStreak(_currentStreak);
    }

    public List<BoosterType> GetRewardStreak(int streak) {
        if (!isEnable)
            return null;

        switch (streak) {
            case 0:
                return null;

            case 1:
                return _reward1 ??= ParseReward(RemoteConfigBase.instance.PowerCube_Streak1_Reward);

            case 2:
                return _reward2 ??= ParseReward(RemoteConfigBase.instance.PowerCube_Streak2_Reward);

            default:
                return _reward3 ??= ParseReward(RemoteConfigBase.instance.PowerCube_Streak3_Reward);
        }
    }

    private List<BoosterType> ParseReward(string strReward) {
        List<BoosterType> result = new List<BoosterType>();
        if (string.IsNullOrEmpty(strReward))
            return result;

        var dataReward = strReward.Split(';');
        foreach (var data in dataReward) {
            if (string.IsNullOrEmpty(data))
                continue;

            if (Enum.TryParse(data, out BoosterType booster)) {
                result.Add(booster);
            }
        }

        return result;
    }

    public TimeSpan GetActiveTime() {
        return _limitTime - DateTime.Now;
    }

    public static BoosterType GetOnboardingFirstSign() {
        if (!isInstanced) {
            return BoosterType.None;
        }

        var reward = instance.GetRewardStreak();
        if (reward != null) {
            foreach (var boosterType in reward) {
                var state = BoosterStorage.GetOnboardState(boosterType);
                if (state == BoosterStorage.OnboardState.None) {
                    return boosterType;
                }
            }
        }

        return BoosterType.None;
    }

    public static bool NeedOnboard() {
        if (!isInstanced) {
            return false;
        }

        if (!isEnable)
            return false;
        if (!isActive)
            return false;
        if (instance._isLocked)
            return false;

        return !PlayerPrefs.HasKey(KEY_FEATURE_ONBOARD);
    }

    public static void Onboard() {
        PlayerPrefs.SetInt(KEY_FEATURE_ONBOARD, 1);
        if (instance) {
            instance._inOnboarding = true;
        }
    }

    public static bool HasToast() {
        return instance && instance._inOnboarding;
    }

    public static void DoneShowToast() {
        PowerCubeTracking.StreakOnboarding(PowerCubeTracking.OnboardState.selection);
    }

    public static string GetListUsedItem() {
        if (!isInstanced) {
            return string.Empty;
        }

        var rewardStreak = instance.GetRewardStreak();
        if (rewardStreak.IsNullOrEmpty()) {
            return string.Empty;
        }
        else {
            List<string> nameItem = new List<string>();
            foreach (var boosterType in rewardStreak) {
                nameItem.Add(BoosterTracking.ConvertToName(boosterType));
            }

            return string.Join(";", nameItem);
        }
    }

    public static void SetupReward() {
        if (!isActive) {
            return;
        }

        List<BoosterType> reward = instance.GetRewardStreak();
        if (reward.IsNullOrEmpty()) {
            return;
        }

        foreach (var type in reward) {
            PowerCubeTracking.StreakReceive(type);
        }
        if (instance._inOnboarding) {
            instance._inOnboarding = false;
            PowerCubeTracking.StreakOnboarding(PowerCubeTracking.OnboardState.setup);
        }
    }
}