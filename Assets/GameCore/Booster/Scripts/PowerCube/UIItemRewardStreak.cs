using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.Cores.Boosters {
    public class UIItemRewardStreak : MonoBehaviour {
        [SerializeField] private int     streak;
        [SerializeField] private Image[] iconRewards;

        private void Start() {
            var reward = PowerCubeManager.instanceSafe.GetRewardStreak(streak);
            int numberReward = 0;

            if (reward != null)
                numberReward = reward.Count;
            if (reward != null && numberReward > 0) {
                for (int index = 0; index < numberReward; index++) {
                    BoosterType i = reward[index];
                    var config = BoosterManager.GetBoosterConfig(i);
                    iconRewards[index].sprite = config.icon;
                    iconRewards[index].gameObject.SetActive(true);
                }
            }

            for (int index = numberReward; index < iconRewards.Length; index++) {
                iconRewards[index].gameObject.SetActive(false);
            }
        }
    }
}