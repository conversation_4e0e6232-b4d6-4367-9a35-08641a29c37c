using System;
using DG.Tweening;
using UnityEngine;

public class CharacterManagerScript : MonoBehaviour, ICharacter {
    #region Fields

    //private
    private                CharacterAnimator _currentAnimator;
    [NonSerialized] public Transform         transformCached;

    private GameObject _character;
    public int RandomAnim => _currentAnimator != null ? _currentAnimator.randomAnim : 0;

    #endregion

    private void Awake() {
        transformCached = this.transform;
    }

    private CharacterData _characterData;
    private ICharacter    _characterImplementation;

    private Coroutine _loadAssetCoroutine;
    
    public void Init(int ballId, int layer = -1, Action<bool> onInitDone = null, int timeout = 0) {
        // if (_characterData != null && _characterData.id == ballId) {
        //     return;
        // }

        _characterData = BallManager.instance.GetCharacterData(ballId);
        if (_characterData == null) {
            Logger.LogError("[Init] Cannot get character data of " + ballId);
            onInitDone?.Invoke(false);
            return;
        }

        string characterName = _characterData.characterName;
        //Get from Resources
        GameObject loadPrefab = Resources.Load<GameObject>(ResourcesPath.Characters + characterName);
        if (loadPrefab != null) {
            InstantiateHuman(ballId, layer, loadPrefab);
            
            // reset anim shop idle sau khi load ball từ Resources
            if (UIController.ui != null && UIController.ui.CheckShopOpened()) {
                UIController.ui.shopScript.ResetAnim();
            }
            onInitDone?.Invoke(true);
            return;
        }

        bool isReturned = false;
        if (timeout > 0) {
            DOVirtual.DelayedCall(timeout, () => {
                if (isReturned) {
                    return;
                }

                isReturned = true;
                ClearCharacterData();
                onInitDone?.Invoke(false);

                StopDownload();
            });
        }

        //Get from asset bundle
        _loadAssetCoroutine = Configuration.instance.StartCoroutine(AssetBundleManager.LoadAsset<GameObject>(
            AssetBundleManager.pathCharacters, characterName, character => {
                try {
                    if (this == null || gameObject == null || isReturned) {
                        return;
                    }

                    isReturned = true;

                    if (_characterData != null && _characterData.id == ballId) {
                        if (character != null) {
                            InstantiateHuman(ballId, layer, character);
                            onInitDone?.Invoke(true);
                        } else {
                            ClearCharacterData();
                            onInitDone?.Invoke(false);
                        }
                    }
                } catch (Exception e) {
                    CustomException.Fire("[CharacterManagerScript][Init]", e.Message + " => " + e.StackTrace);
                }
            }));
    }

    public void StopDownload() {
        if (_loadAssetCoroutine != null) {
            StopCoroutine(_loadAssetCoroutine);
            AssetBundleManager.isLoadingAssetBundlesByHash = false;
        }
    }
    
    private void InstantiateHuman(int id, int layer, GameObject prefCharacter) {
        if (_characterData == null || _characterData.id != id) {
            return;
        }

        ClearOld();

        _character = Instantiate(prefCharacter, transform, false);
        if (layer >= 0) {
            _character.transform.SetLayerRecursivelyIgnoreTag(layer, "IgnorePreview");
        }

        _currentAnimator = _character.GetComponent<CharacterAnimator>();
        //show/hide model
        if (_currentAnimator != null) {
            _currentAnimator.SetBallID(id);
            _currentAnimator.SetActive(true);
        }

        // reset anim shop idle sau khi load ball từ assetbundle
        if (UIController.ui != null && UIController.ui.CheckShopOpened()) {
            UIController.ui.shopScript.ResetAnim();
        }
    }

    public void ClearCharacterData() {
        _characterData = null;
    }

    private void ClearOld() {
        if (_currentAnimator != null && GameController.instance.game != GameStatus.LIVE) {
            DestroyImmediate(_currentAnimator.gameObject);
            _currentAnimator = null;
        }

        if (transform.childCount > 0) {
            transform.ClearChild();
        }
    }

    public void ShowVfx() {
    }

    #region Animation

    public void SetAnimationCharacterJump(float jumpTime, bool isJumpRight, int randomAnim = 0) {
        if (_currentAnimator != null) {
            _currentAnimator.SetJump(jumpTime, isJumpRight, randomAnim);
        }
    }

    public void SetAnimationCharacterLastJump(float jumpTime) {
        if (_currentAnimator != null) {
            _currentAnimator.SetLastJump(jumpTime);
        }
    }

    public float PrepareFinished() {
        return _currentAnimator != null ? _currentAnimator.SetStateVictory() : 0;
    }

    #endregion

    public CharacterAnimator GetCurrentAnimator() {
        return _currentAnimator;
    }

    public void EnableBall() {
        if (_currentAnimator != null) {
            _currentAnimator.Reset();
        }
    }

    public float PrepareStart() {
        //_currentAnimator.SetStateFly();

        // float timeStart = 0.5f;
        // transform.DORotate(Vector3.zero, timeStart);
        // return timeStart;

        return 0;
    }

    public void SetActive(bool isShow) {
        gameObject.SetActive(isShow);
    }

    public void Rotate(int angle, float timeAnimation) {
        if (_currentAnimator != null) {
            Vector3 newRotate = new Vector3(0, angle, 0);
            _currentAnimator.transform.DOLocalRotate(newRotate, timeAnimation, RotateMode.FastBeyond360);
        }
    }

    public void Rotate(float angle) {
        if (_currentAnimator != null) {
            Vector3 newRotate = new Vector3(0, angle, 0);
            _currentAnimator.transform.Rotate(newRotate);
        }
    }

    public void SetRotation(Vector3 direction) {
        transformCached.localRotation = Quaternion.Euler(direction);
    }

    public void StopRotate() {
        if (_currentAnimator != null) {
            _currentAnimator.transform.DOKill();
        }
    }

    public void SetStateStart() {
        if (_currentAnimator != null) {
            _currentAnimator.SetStateStart();
        }
    }

    public void SetStateSlide() {
        if (_currentAnimator != null) {
            _currentAnimator.SetStateSlide();
        }
    }

    public void SetStateDance() {
        if (_currentAnimator != null) {
            _currentAnimator.SetStateDance();
        }
    }

    public Animator GetAnimator() {
        if (_currentAnimator != null) {
            return _currentAnimator.GetAnimator();
        }

        return null;
    }

    public Renderer[] GetRenderer() {
        if (_character != null) {
            return _character.GetComponentsInChildren<Renderer>();
        }

        return null;
    }
    public void SetActiveTrail(bool isActive) {
        if (_currentAnimator != null) {
            _currentAnimator.SetActiveTrail(isActive);
        }
    }

    public void SetChildRotation(Vector3 direction) {
        if (transformCached.childCount > 0) {
            transformCached.GetChild(0).localRotation = Quaternion.Euler(direction);
        }
    }

    public void PlayRandomAnimFromShop() {
        if (_currentAnimator != null) {
            _currentAnimator.PlayRandomAnimFromShop(true);
        }
    }
}