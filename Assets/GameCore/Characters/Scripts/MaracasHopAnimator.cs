using UnityEngine;

public class MaracasHopAnimator : CharacterAnimator {
    [SerializeField] private bool enableChangeToVictoryInAdvance;

    private int _currentNoteId;

    [Space(10)]
    [SerializeField] private float timeShortJump = 0.733f;

    [SerializeField] private float timeNormalJump = 0.733f;
    [SerializeField] private float timeLongJumpLr = 1.067f;
    [SerializeField] private float timeLongJumpRl = 1f;
    [SerializeField] private float timeLastJump   = 1.0f;
    [SerializeField] private float timeFinished   = 1.0f;

    private void Start() {
        Ball.OnJump += UpdateNoteId;
    }

    private void OnDestroy() {
        Ball.OnJump -= UpdateNoteId;
    }

    private void UpdateNoteId(int noteId) {
        _currentNoteId = noteId;
    }

    public override void SetJump(float jumpTime, bool isJumpRight, int random) {
        if (Ball.b.isPausePlayer) {
            return;
        }

        if (enableChangeToVictoryInAdvance && _currentNoteId >= NotesManager.instance.noteCount - 2) {
            return;
        }

        JumpType jumpType = GetJumpType(jumpTime);
        switch (jumpType) {
            case JumpType.ShortJump:
                animator.SetTrigger(ShortJump);
                animator.speed = timeShortJump / jumpTime;
                break;

            case JumpType.NormalJump:
                animator.SetTrigger(NormalJump);
                animator.speed = timeNormalJump / jumpTime;
                break;

            case JumpType.LongJump:
                int range = random == 0 ? Random.Range(1, 3) : random; // 1 2
                switch (range) {
                    case 1:
                        animator.SetTrigger(LongJumpLR);
                        animator.speed = timeLongJumpLr / jumpTime;
                        break;

                    case 2:
                        animator.SetTrigger(LongJumpRL);
                        animator.speed = timeLongJumpRl / jumpTime;
                        break;
                }

                break;
        }
    }

    public override void SetLastJump(float jumpTime) {
        if (enableChangeToVictoryInAdvance) {
            PlayFinishedAnimationSequence();
        } else {
            animator.SetTrigger(LastJump);
            animator.speed = timeLastJump / jumpTime;
        }
    }

    public override void SetStateStart() {
        animator.SetTrigger(StartAnim);
        animator.speed = 1;
    }

    public override float SetStateVictory() {
        if (!enableChangeToVictoryInAdvance) {
            PlayFinishedAnimationSequence();
        }

        return 2 * timeFinished;
    }

    protected void PlayFinishedAnimationSequence() {
        animator.SetTrigger(Finished);
        animator.speed = 1;
    }

    public override void RunAnimation(int hash) {
        animator.SetTrigger(hash);
        animator.speed = 1;
    }

    public override void SetActiveTrail(bool isActive) { }

    public override void SetStateFly() { }

    public override void SetBallID(int ballId) { }
}