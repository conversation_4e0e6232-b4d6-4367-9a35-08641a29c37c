using UnityEngine;

public class TrailNotRotateWithBall : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameSubscriber {
    [SerializeField] private Transform[] trails;
    private bool _isActived = false;
    
    private void Start() {
        if (GameController.instance != null) {
            GameController.instance.addSubcriber(this);
        }
    }

    private void OnDestroy() {
        if (GameController.instance != null) {
            GameController.instance.gameSubcribers.Remove(this);
        }

        if (!_isActived) return;
        foreach (var trail in trails) {
            Destroy(trail.gameObject);
        }
    }

    public void gameStart() {
        if (gameObject.activeInHierarchy && GameItems.instance && GameItems.instance.ballTrailer) {
            foreach (var trail in trails) {
                trail.parent = GameItems.instance.ballTrailer.transform;
            }

            _isActived = true;
        }
    }

    public void gameContinue() {
        
    }

    public void gameOver() {
        
    }
}
