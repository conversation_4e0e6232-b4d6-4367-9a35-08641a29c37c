public class BallCollectionHeader : BaseCollectionHeader {
    protected override void UpdateCollectionCount() {
        if (!BallManager.isInstanced) {
            countText.text = "--/--";
            return;
        }
        (int currentCount, int maxCount) = BallManager.instance.CountCurrentOpenAndMaxNumberSkins();
        countText.text = Util.BuildString("/", currentCount, maxCount);
    }

    protected override void RegisterChangeCount() {
        Configuration.OnUnlockNewBall += UpdateCollectionCount;
    }

    protected override void UnregisterChangeCount() {
        Configuration.OnUnlockNewBall -= UpdateCollectionCount;
    }
}
