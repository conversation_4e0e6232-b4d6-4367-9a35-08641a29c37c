namespace Inwave.CommunicationSystem {
    public enum ActionSystemType {
        /// <summary>
        /// Navigate to a specific path
        /// </summary>
        Navigate,

        /// <summary>
        /// Play the song with that ACM ID
        /// </summary>
        PlaySong,

        /// <summary>
        /// Play the song with that ACM ID and unlock it
        /// </summary>
        PlaySongAndUnlock,

        /// <summary>
        /// Open PI in current platform store
        /// </summary>
        OpenStore,

        /// <summary>
        /// Open an URL in device’s default browser
        /// </summary>
        OpenUrl,

        /// <summary>
        /// Open Vip subscription/benefit popup
        /// </summary>
        OpenVipSub,

        /// <summary>
        /// Unlock next (locked) song and go to action phase of that song
        /// </summary>
        RewardNextSong
    }
}