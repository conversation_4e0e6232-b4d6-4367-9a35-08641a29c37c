using System;
using Inwave;
using UnityEngine;
using Music.ACM;
using Utils = Inwave.Utils;

namespace TileHop.EconomySystem {
    public class SpecialOfferManager : FastSingleton<SpecialOfferManager> {
        [Serializable]
        private class SpecialOfferUserData {
            public bool   isBought;
            public bool   isActive;
            public string remainTime;
            public bool   isExpired;
            public bool   isNeedRemind; // variable to check auto show when user kill shop before show auto popup
            public bool   isShowLastChance;
            public string remainTimeRevive;
            public int    countAutoShowByGem;
            public int    countAutoShowVer2;
            public bool   isClose;
        }

        private const string SPECIAL_OFFER_DATA = "Economy_SpecialOffer_UserData";

        [SerializeField, ReadOnly] private bool                 _isInit;
        [SerializeField, ReadOnly] private SpecialOfferConfig   _config;
        [SerializeField, ReadOnly] private SpecialOfferUserData _userData;
        [SerializeField, ReadOnly] private bool                 _isActive;
        [SerializeField, ReadOnly] private bool                 _isFreeRevive;

        /// <summary>
        /// [Ver2] Use to ensure auto-popup only shows once per session 
        /// </summary>
        private static bool _isAutoShowed = false;

        private static UISpecialOffer _instancePopup;
        
        #if UNITY_EDITOR
        [SerializeField, ReadOnly] private string remainTimeText;
        #endif

        public int version { get; private set; }
        private float _timeTick;
        public TimeSpan RemainTime { get; private set; }

        public static event Action<bool> OnChangeStatus;

        #region Unity Methods

        protected override void Awake() {
            base.Awake();
            if (instance == this) {
                DontDestroyOnLoad(this.gameObject);
            }
        }

        private void Update() {
            if (version == 2 || version == 3) {
                return;
            }
            
            if (_isFreeRevive) {
                _timeTick += Time.deltaTime;
                if (_timeTick >= 1f) {
                    _timeTick -= 1f;
                    RemainTime = RemainTime.Subtract(Configuration.OneSecond);
                    if (RemainTime.TotalSeconds < 1f) {
                        ExpiredFreeRevive();
                    }
#if UNITY_EDITOR
                    remainTimeText = RemainTime.ToString();
#endif
                }
            } else if (_isActive) {
                _timeTick += Time.deltaTime;
                if (_timeTick >= 1f) {
                    _timeTick -= 1f;
                    RemainTime = RemainTime.Subtract(Configuration.OneSecond);
                    if (!_userData.isShowLastChance) {
                        CheckShowLastChance();
                    }

                    if (RemainTime.TotalSeconds < 1f) {
                        ExpiredSpecialOffer();
                    }
#if UNITY_EDITOR
                    remainTimeText = RemainTime.ToString();
#endif
                }
            }
        }

        private void OnDisable() {
            if (_userData != null) {
                SaveUserData();
            }
        }

        private void OnApplicationPause(bool pauseStatus) {
            if (pauseStatus) {
                if (_userData != null) {
                    SaveUserData();
                }
            }
        }

        #endregion

        public static bool IsFreeRevive() {
            if (RemoteConfigBase.instance.Economy_SpecialOffer_Version == 2 || RemoteConfigBase.instance.Economy_SpecialOffer_Version == 3) {
                return false;
            }
            
            if (!isInstanced || instance._userData == null)
                return false;

            return instance._userData.isBought && !instance._userData.isClose;
        }

        private void CheckShowLastChance() {
            if (_config.condition.autoshow_expired <= 0)
                return;
            if (RemainTime.TotalMinutes > _config.condition.autoshow_expired)
                return;
            if (!Util.IsHomeScene())
                return;

            _userData.isShowLastChance = true;
            ShowPopup(UISpecialOffer.ShowType.last_chance);
        }

        public SpecialOfferConfig GetConfig() {
            if (!_isInit) {
                Init();
            }

            return _config;
        }

        public void InitSettings() {
            if (!_isInit) {
                Init();
            }

            if (_config == null || _userData.isClose) {
                //Logger.LogWarning("Special Offer => Config || Offer is null!!!");
                Destroy(this.gameObject);
            }
        }

        private void Init() {
            if (_isInit)
                return;

            _isInit = true;
            version = RemoteConfigBase.instance.Economy_SpecialOffer_Version;

            string dataConfig = RemoteConfigBase.instance.Economy_SpecialOffer;
            if (!string.IsNullOrEmpty(dataConfig)) {
                try {
                    _config = JsonUtility.FromJson<SpecialOfferConfig>(dataConfig);
                } catch (Exception e) {
                    CustomException.Fire("[Get Special Offer Config]", e.Message + " => " + dataConfig);
                }
            }

            if (_config == null)
                return;

			if (version == 3) {
				SpecialOfferV3Controller autoShow = gameObject.AddComponent<SpecialOfferV3Controller>();
                autoShow.InitConfig(Utils.IsAndroid() ? _config.noAds.trigger_android : _config.noAds.trigger_ios, _config.noAds.variant);
			}

            //get current user data
            LoadUserData();

            _isActive = _userData.isActive;
            _isFreeRevive = _userData.isBought && !_userData.isClose;
            if (_isActive && !_isFreeRevive) {
                RemainTime = TimeSpan.Parse(_userData.remainTime);
            } else if (_isFreeRevive) {
                RemainTime = TimeSpan.Parse(_userData.remainTimeRevive);
            }

            OnChangeStatus?.Invoke(_isActive);

            // NonSubPendingPurchaseManager.onProcessTransactions += HandleUnprocessedPurchase;
        }

        public bool HandleUnprocessedPurchase() {
            string productId = IapBase.GetProductID(IAPDefinitionId.special_offer);

            if (string.IsNullOrEmpty(productId)) {
                return false;
            }
            
            // check primary id when user pending purchase in one version of pack
            if (NonSubPendingPurchaseManager.ContainsUnprocessed(productId)) {
                NonSubPendingPurchaseManager.RemoveUnprocessedPurchase(productId);
                ProcessCompletedPendingPurchase();
                return true;
            }
            return false;
        }

        private void ProcessCompletedPendingPurchase() {
            var gemBefore = Configuration.instance.GetDiamonds();
            var amount = _config.benefit.gem;
            
            //reward
            Configuration.UpdateDiamond(amount, CurrencyEarnSource.Special_Offer.ToString(), CurrencyEarnSource.special_offer.ToString());
            OnBoughtSpecialPack();
            
            // close opened popup
            if (_instancePopup != null) {
                _instancePopup.Close();
            }
            
            //tracking
            SpecialOfferTracking.SpecialOfferPopupSuccess(
                EconomyIAPTracker.TRACK_LOCATION.pending_purchase.ToString(), 
                gemBefore,
                Configuration.instance.GetDiamonds());
        }

        private void LoadUserData() {
            if (PlayerPrefs.HasKey(SPECIAL_OFFER_DATA)) {
                string jsonData = PlayerPrefs.GetString(SPECIAL_OFFER_DATA);
                //("Special Offer", $"Load data: {jsonData}");
                try {
                    _userData = JsonUtility.FromJson<SpecialOfferUserData>(jsonData);
                } catch (Exception e) {
                    CustomException.Fire("[Get Special Offer UserData]", e.Message + " => " + jsonData);
                    _userData = new SpecialOfferUserData();
                }
            } else {
                _userData = new SpecialOfferUserData();
            }
        }

        private void SaveUserData() {
            if (_userData.isBought) {
                _userData.remainTimeRevive = RemainTime.ToString();
            } else {
                _userData.remainTime = RemainTime.ToString();
            }

            string jsonData = JsonUtility.ToJson(_userData);
            //Logger.EditorLog("Special Offer", $"Save data: {jsonData}");
            PlayerPrefs.SetString(SPECIAL_OFFER_DATA, jsonData);
        }

        public bool IsActive() {
            return _isActive;
        }

        public bool TryActive() {
            if (!_isInit) {
                Init();
            }

            if (_config == null) {
                return false; //disable feature
            }

            if (_isActive) {
                return true;
            }

            if (_userData.isBought) {
                return false; //already bought this pack
            }

            int userCurrentDay = UserProperties.GetDayDiff();
            if (_config.condition.daydiff > userCurrentDay) {
                return false;
            }

            bool isVip = SubscriptionController.IsSubscriptionVip();
            if (isVip) {
                if (!_config.condition.activesub) {
                    return false;
                }
                // if (!CanEarnDiamond()) {
                //     return false;
                // }
            }

            if (version == 1) {
                if (_userData.isExpired) {
                    return false; // already expired
                }
            
                if (Configuration.instance.CanShowStarterPack()) {
                    return false;
                }
            } else if(version == 2 || version == 3) {
                int sessionId = UserProperties.GetSessionCount();
                if (sessionId < _config.condition.session) {
                    return false;
                }
            }

            if (!_userData.isActive) {
                ActiveSpecialOffer();
            }

            return true;
        }

        private void ActiveSpecialOffer() {
            //Logger.EditorLog("Special Offer", "Active feature!");
            _userData.isActive = true;
            _isActive = true;
            RemainTime = TimeSpan.FromMinutes(_config.condition.expiredtime);
            _userData.remainTime = RemainTime.ToString();
            SpecialOfferTracking.SpecialOfferEnable();
            SaveUserData();

            OnChangeStatus?.Invoke(_isActive);
            if (HomeManager.instance) {
                HomeManager.instance.CheckShowSpecialOffer();
            }
        }

        private void ExpiredSpecialOffer() {
            _userData.isExpired = true;
            _userData.remainTime = string.Empty;
            DeActiveSpecialOffer();
        }

        private void ExpiredFreeRevive() {
            _userData.isClose = true;
            _isFreeRevive = false;
            SaveUserData();

            string packageId = Utils.IsAndroid() ? _config.idpack_android : _config.idpack_ios;
            DateTime now = DateTime.Now;
            TimeSpan t = now - new DateTime(1970, 1, 1);
            int secondsSinceEpoch = (int) t.TotalSeconds;
            SpecialOfferTracking.ReviveDurationEnd(packageId, secondsSinceEpoch);
        }

        private void DeActiveSpecialOffer() {
            //Logger.EditorLog("Special Offer", "DeActive feature!");
            _userData.isActive = false;
            _isActive = false;
            OnChangeStatus?.Invoke(_isActive);
            SaveUserData();
            SpecialOfferTracking.SpecialOfferDisable();
        }

        public void OnBoughtSpecialPack() {
            // get free revive time
            if (_config.benefit.unlimitedRevive > 0) {
                UnlimitedReviveManager.IncreaseUnlimitedReviveTime(_config.benefit.unlimitedRevive);
                _isFreeRevive = true;
                RemainTime = TimeSpan.FromMinutes(_config.benefit.unlimitedRevive);
                _userData.remainTimeRevive = RemainTime.ToString();
                string packageId = Utils.IsAndroid() ? _config.idpack_android : _config.idpack_ios;
                DateTime now = DateTime.Now;
                TimeSpan t = now - new DateTime(1970, 1, 1);
                int secondsSinceEpoch = (int) t.TotalSeconds;
                SpecialOfferTracking.ReviveDurationStart(packageId, secondsSinceEpoch);
            }
            
            // set no-FSAds
            if (_config.benefit.noFsAds) {
                Configuration.AddNoFSAds();
            }
            
            _userData.isBought = true;
            DeActiveSpecialOffer();
        }

        public bool CanShowPopup(UISpecialOffer.ShowType showType, string location) {
            if (!IsActive()) {
                return false;
            }

            if (_isAutoShowed && version == 2) {
                return false;
            }

            switch (showType) {
                case UISpecialOffer.ShowType.home:
                    return true;

                case UISpecialOffer.ShowType.last_chance:
                    return true;

                case UISpecialOffer.ShowType.auto:
                    if (!_userData.isNeedRemind && version == 1) {
                        return false;
                    }

                    // int currentGem = Configuration.instance.GetDiamonds();
                    // if (currentGem >= _config.condition.wallet) {
                    //     return false;
                    // }
                    if(version != 3) {
                    if (_config.condition.location.IndexOf(location, StringComparison.OrdinalIgnoreCase) < 0) {
                        return false; // chỉ bật popup này khi ở những location cho phép
                    }
					}

                    bool isReachCapping = version == 1 && _userData.countAutoShowByGem >= _config.condition.autoshow_needgems_capping;
                    if (isReachCapping) {
                        return false; //run out of capping auto show when not enough gems
                    }

                    if (version == 2 || version == 3) {
                        if (_userData.countAutoShowVer2 >= _config.condition.maxAutoShowOnResult) {
                            return false;
                        }
                    } else {
                        if (_userData.countAutoShowByGem >= _config.condition.autoshow_needgems_capping) {
                            return false;
                        }
                    }

                    break;
            }

            return true;
        }

        public static void ShowPopupAtResultScreen() {
            bool canAutoShowSpecialOffer = isInstanced && instance.version == 2 &&
                                           instance.CanShowPopup(UISpecialOffer.ShowType.auto,
                                               LOCATION_NAME.result.ToString());
        
            // auto show if Special Offer is activated and can show automaticaly
            if (canAutoShowSpecialOffer) {
                instance.ShowPopup(UISpecialOffer.ShowType.auto);
            }
        }

        public GameObject ShowPopup(UISpecialOffer.ShowType type) {
            switch (type) {
                case UISpecialOffer.ShowType.auto:
                    //record to limited autoshow popup by capping
                    if (version == 2 || version == 3) {
                        _userData.countAutoShowVer2++;
                    } else {
                        _userData.countAutoShowByGem++;
                    }

                    SaveUserData();
                    _isAutoShowed = true;
                    SetRemindSpecialOffer(false);
                    break;

                case UISpecialOffer.ShowType.home:
                    SpecialOfferTracking.SpecialOfferEntryClick(type.ToString(), Configuration.instance.GetDiamonds());
                    break;

                case UISpecialOffer.ShowType.last_chance:
                    break;
            }

            if (_instancePopup == null) {
                GameObject popup = Util.ShowPopUp(Util.BuildString("V", PopupName.EconomySpecialOffer, version));
            
                if (popup != null && popup.TryGetComponent(out _instancePopup)) {
                    _instancePopup.Show(type, type.ToString());
                }
            } else {
                _instancePopup.gameObject.SetActive(true);
                _instancePopup.Show(type, type.ToString());
            }
            
            return _instancePopup == null? null : _instancePopup.gameObject;
        }

        public void SetRemindSpecialOffer(bool active) {
            if (!_isActive)
                return;

            _userData.isNeedRemind = active;
        }

        public bool CreateButtonAtHome(Transform btnSpecialOffer, bool fromUP) {
            var prefabName = fromUP? ResourcesPath.SpecialOffer_ButtonUP : ResourcesPath.SpecialOffer_Button;
            if (version == 2) {
                prefabName = Util.BuildString(string.Empty, prefabName, "_V2");
            }
            else if (version == 3) {
				prefabName = Util.BuildString(string.Empty, prefabName, "_V3");
			}
            
            var prefab = Resources.Load<GameObject>(prefabName);
            if (prefab == null) {
                return false;
            }

            Instantiate(prefab, btnSpecialOffer);
            return true;
        }

        public static bool IsEnable() {
            string dataConfig = RemoteConfigBase.instance.Economy_SpecialOffer;
            if (string.IsNullOrEmpty(dataConfig)) {
                return false;
            }

            return true;
        }
    }
}