using System;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.EconomySystem {
    public class ItemStarterPack : MonoBehaviour {
        [SerializeField] private Text   txtPrice;
        [SerializeField] private Text   txtOldPrice;
        [SerializeField] private Button btnBuy;
        [SerializeField] private Text   txtEndTime;
        [SerializeField] protected Text   txtValueDiamond;

        private const IAPDefinitionId ID = IAPDefinitionId.starter_pack;

        protected EconomyIAPRemoteData economyIAPRemoteData;

        private TimeSpan _remainTime;
        private string   _strRemainTime;
        private string   _strTime;
        private float    _countTime;

        private void OnEnable() {
            economyIAPRemoteData = Configuration.instance.GetEconomyRemoteConfig();
            if (economyIAPRemoteData == null) {
                gameObject.SetActive(false);
                return;
            }
            
            ShowValue();
            ShowOldPrice();
            InitTimer();
        }

        protected virtual void Start() {
            btnBuy.onClick.AddListener(OnBtnBuyClicked);
            Configuration.OnBoughtStarterPack += ConfigurationOnOnBoughtStarterPack;
        }

        private void OnDestroy() {
            Configuration.OnBoughtStarterPack -= ConfigurationOnOnBoughtStarterPack;
        }
        
        protected virtual void Update() {
            _countTime += Time.deltaTime;
            if (_countTime > 1) {
                _countTime -= 1;
                _remainTime = _remainTime.Subtract(Configuration.OneSecond);
                UpdateRemainTime();
                if (_remainTime.TotalSeconds < 1) {
                    //end event
                    this.gameObject.SetActive(false);
                    EconomyIAPTracker.Track_StarterPackDisable();
                }
            }
        }

        protected virtual void ShowValue() {
            txtValueDiamond.text = economyIAPRemoteData.StarterPack_Diamond > 0
                ? $"{economyIAPRemoteData.StarterPack_Diamond} {LocalizationManager.instance.GetLocalizedValue("DIAMONDS")}"
                : string.Empty;
        }

        protected virtual void ShowOldPrice(){
            if (Application.isEditor) {
                return;
            }
            
            float currentPrice = IapBase.GetPriceValue(ID);
            txtPrice.text = IapBase.GetPriceString(ID);
            
            if (txtOldPrice != null) {
                float oldPrice = Mathf.RoundToInt(currentPrice / (1 - EconomyOfferStarterPack.PERCENT_SALE / 100f));
                if (oldPrice > 10000) {
                    oldPrice = Mathf.RoundToInt(oldPrice / 1000) * 1000;
                } else if (oldPrice > 1000) {
                    oldPrice = Mathf.RoundToInt(oldPrice / 100) * 100;
                } else if (oldPrice > 100) {
                    oldPrice = Mathf.RoundToInt(oldPrice / 10) * 10;
                }

                txtOldPrice.text = Util.PriceToString(oldPrice, IapBase.GetPackagePriceCode(ID));
            }
        }
        
        protected virtual void InitTimer() {
            _remainTime = Configuration.instance.GetRemainTimeStarterPack();
            _strRemainTime = LocalizationManager.instance.GetLocalizedValue("ENDS_IN");
            UpdateRemainTime();
            _countTime = 0f;
        }

        private void UpdateRemainTime() {
            if (txtEndTime == null)
                return;
            _strTime = string.Empty;
            if (_remainTime.TotalHours >= 24) {
                _strTime = $"{_remainTime.Days}D {_remainTime.Hours}H";
            } else {
                _strTime = $"{_remainTime.Hours}H {_remainTime.Minutes}M";
                // strTime = remainTime.ToString(@"hh\:mm\:ss");
            }

            txtEndTime.text = $"{_strRemainTime} {_strTime}";
        }


        private void ConfigurationOnOnBoughtStarterPack() {
            this.gameObject.SetActive(false);
        }

        protected virtual void OnBtnBuyClicked() {
            SoundManager.PlayGameButton();
            var popup = EconomyOfferStarterPack.Show();
            popup.GetComponent<EconomyOfferStarterPack>().SetLocation(EconomyIAPTracker.TRACK_LOCATION.shop);
            EconomyIAPTracker.Track_StarterPackEntryClick(EconomyIAPTracker.TRACK_LOCATION.shop,
                Configuration.instance.GetDiamonds());
        }
    }
}