using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TileHop.LiveEvent.GalaxyQuest {
    public class PlayerAvatarItem : MonoBehaviour {
        #region Fields

        private static readonly Vector3 _squashSize  = new(1.2f, 0.9f, 1f);
        private static readonly Vector3 _stretchSize = new(0.8f, 1.5f, 1f);
        
        private static List<Sprite> _framePool;
        private static Sprite       _defaultAvatarSprite;
    
        [SerializeField] private SpriteRenderer frame;
        [SerializeField] private SpriteRenderer avatar;

        public  string         avatarPhotoUrl;
        public  int            frameIndex;
        private Transform      _cachedTransform;
        private ParticleSystem _shockVfx;

        #endregion

        #region Properties

        public Transform cachedTransform {
            get {
                if (_cachedTransform == null) {
                    _cachedTransform = transform;
                }
                return _cachedTransform;
            }
        }

        public static Sprite defaultAvatarSprite {
            get {
                if (_defaultAvatarSprite == null) {
                    _defaultAvatarSprite = Resources.Load<Sprite>("Sprites/default-avatar");
                }
                
                return _defaultAvatarSprite;
            }
        }

        #endregion
        
        #region Process Avatar

        public void SetFrame(bool isMainPlayer) {
            InitUserFrameAssets();
            if (_framePool.Count == 0) {
                return;
            }

            frameIndex = isMainPlayer ? 0 : GalaxyQuestHelper.GetBetterRandomInt(0, _framePool.Count);
            frame.sprite = _framePool[frameIndex];
        }

        public static Sprite GetFrameSprite(int index) {
            if (_framePool == null) {
                return null;
            }
            
            if (index < 0 || index >= _framePool.Count) {
                return null;
            }
            
            return _framePool[index];
        }
        
        public async void SetAvatar(string imageUrl) {
            try {
                avatar.sprite = defaultAvatarSprite;

                if (string.IsNullOrEmpty(imageUrl)) {
                    avatar.transform.localScale = Vector3.one;
                    avatarPhotoUrl = imageUrl;
                    return;
                }
                
                var avatarSprite = await DownloadManager.instanceSafe.DownloadImage(imageUrl);

                if (avatarSprite != null && avatar != null) {
                    avatar.sprite = avatarSprite;
                    Vector2 targetSize = defaultAvatarSprite.bounds.size;
                    Vector2 spriteSize = avatarSprite.bounds.size;
        
                    Vector3 scale = Vector3.one;
                    scale.x = targetSize.x / spriteSize.x;
                    scale.y = targetSize.y / spriteSize.y;

                    avatar.transform.localScale = scale;
                    avatarPhotoUrl = imageUrl;
                }
            } catch (Exception e) {
                Debug.Log(e);
            }
        }

        protected void InitUserFrameAssets() {
            if (_framePool != null) {
                return;
            }
            
            _framePool = new List<Sprite>();
            for (int i = 1; i < 5; i++) {
                var frameAsset = Resources.Load<Sprite>($"Sprites/frame{i}");
                if (frameAsset != null) {
                    _framePool.Add(frameAsset);
                }
            }
        }

        #endregion
        

        #region Display
        
        public void Show() {
            frame.SetAlpha(1f);
            avatar.SetAlpha(1f);
            cachedTransform.rotation = Quaternion.identity;
        }

        public void Hide() {
            frame.SetAlpha(0f);
            avatar.SetAlpha(0f);
        }

        public void Popout(float delay = 0f) {
            var trans = cachedTransform;
            var scale = trans.localScale;
            trans.localScale = Vector3.zero;
            Show();
            trans.DOScale(scale, 0.5f).SetEase(Ease.OutBack).SetDelay(delay);
            trans.localPosition += Vector3.up * 50f;
            trans.DOLocalMoveY(-50f, 1f).SetDelay(delay).SetRelative(true).SetEase(Ease.OutBack);
        }
        
        public void Appear(float duration) {
            frame.SetAlpha(0f);
            avatar.SetAlpha(0f);
            frame.DOFade(1f, duration);
            avatar.DOFade(1f, duration);
        }

        public void Disappear(float delay = 0f) {
            cachedTransform.DOLocalMoveY(-10f, 1).SetRelative(true).SetEase(Ease.Linear).SetDelay(delay);
            frame.DOFade(0f, 1f).SetEase(Ease.Linear).SetDelay(delay);
            avatar.DOFade(0f, 1f).SetEase(Ease.Linear).SetDelay(delay);
        }

        public void Shock(float duration, float delay = 0f) {
            if (_shockVfx == null) {
                var shockVfxPrefab = Resources.Load<ParticleSystem>("VFX/VFX_DropTile");
                if (shockVfxPrefab != null) {
                    _shockVfx = Instantiate(shockVfxPrefab, cachedTransform);
                }   
            }

            if (_shockVfx != null) {
                DOVirtual.DelayedCall(delay, _shockVfx.Play);
            }
            
            cachedTransform.DOShakePosition(duration, Vector3.one * Random.Range(10f, 20f), 50).SetDelay(delay);
        }

        
        /// <summary>
        /// The avatar of main user falls down from above to the current block
        /// </summary>
        /// <param name="target"></param>
        /// <returns>Total duration in seconds from the beginning to landing</returns>
        public float AppearOnRevive(Transform target) {
            var trans = cachedTransform;
            var angle = Random.Range(10f, 30f) * (Random.Range(0, 2) == 0? 1 : -1);
            trans.rotation = Quaternion.identity;
            trans.position = target.position;
            trans.localPosition += Vector3.up * 300;
            trans.localScale = _stretchSize;

            const float appearTime = 0.35f;
            const float fallTime = 0.5f;
            
            float delay = appearTime;
            trans.localScale = Vector3.zero;
            trans.DOScale(1f, appearTime);
            trans.DORotateQuaternion(Quaternion.Euler(0, 0, angle), appearTime + fallTime);
            trans.DOMoveY(0.5f, appearTime).SetRelative(true).SetEase(Ease.InOutSine);
            
            
            trans.DOMoveY(target.position.y, fallTime).SetEase(Ease.InCirc).SetDelay(delay);
            delay += fallTime;
            trans.DOMoveY(target.position.y + 0.2f, 0.2f).SetEase(Ease.InOutSine).SetDelay(delay);
            trans.DOMoveY(target.position.y, 0.2f).SetEase(Ease.InOutSine).SetDelay(delay + 0.25f);
            
            trans.DORotate(-angle * 0.75f * Vector3.forward, 0.2f).SetEase(Ease.Linear).SetDelay(delay);
            trans.DORotate(angle * 0.5f * Vector3.forward, 0.2f).SetEase(Ease.Linear).SetDelay(delay + 0.2f);
            trans.DORotate(Vector3.zero, 0.1f).SetEase(Ease.Linear).SetDelay(delay + 0.4f);
            
            trans.DOScale(_squashSize, 0.1f).SetDelay(delay);
            trans.DOScale(Vector3.one, 0.1f).SetDelay(delay + 0.1f);
            
            return appearTime + fallTime;
        }

        #endregion


        #region Jumping Animations

        public void JumpNext(Vector3 nextPoint, float jumpPower, float jumpDuration, Ease jumpingEase, float delay = 0, TweenCallback onComplete = null) {
            var trans = cachedTransform;
            var horizontalDistance = trans.position.x - nextPoint.x;
            
            // góc tiếp tuyến với quỹ đạo parabol
            var angle = Mathf.Clamp(Mathf.Atan2(horizontalDistance / 4, jumpPower) * Mathf.Rad2Deg, -20f, 20f);
            
            // phase 1: squash
            trans.DOScale(_squashSize, 0.2f).SetDelay(delay);
            delay += 0.2f;
            
            
            // phase 2: stretch
            trans.DOScale(_stretchSize, 0.2f).SetDelay(delay);
            trans.DOScale(Vector3.one, jumpDuration - 0.2f).SetDelay(delay + 0.2f).SetEase(Ease.OutBack);

            // phase 3: landing squash and stretch
            var landingScaleDelay = delay + jumpDuration;
            trans.DOScale(_squashSize, 0.15f).SetDelay(landingScaleDelay);
            trans.DOScale(Vector3.one, 0.15f).SetDelay(landingScaleDelay + 0.15f);

            trans.DORotate(angle * Vector3.forward, 0.2f).SetDelay(delay);
            
            // jump
            delay += 0.1f;
            trans.DOJump(nextPoint, jumpPower, 1, jumpDuration).SetEase(jumpingEase).SetDelay(delay).OnComplete(onComplete);
            
            var halfDuration = jumpDuration / 2;
            trans.DORotate(Vector3.zero, halfDuration - 0.1f).SetEase(Ease.Linear).SetDelay(delay + 0.1f);
            trans.DORotate(-angle * Vector3.forward, halfDuration).SetEase(Ease.Linear).SetDelay(delay + halfDuration);
            
            // landing rotate
            var delayLanding = delay + jumpDuration;
            var inertiaAngle = Mathf.Sign(angle) * Mathf.Max(Mathf.Abs(angle) * 0.75f, Random.Range(10, 21));
            trans.DORotate(inertiaAngle * Vector3.forward, 0.2f).SetEase(Ease.Linear).SetDelay(delayLanding);
            delayLanding += 0.2f;
            trans.DORotate(Vector3.zero, 0.1f).SetEase(Ease.Linear).SetDelay(delayLanding);
            trans.DOJump(nextPoint, 0.2f, 1, 0.1f).SetDelay(delayLanding);
        }

        public void JumpOut(Vector3 nextPoint, float jumpPower, float jumpDuration, bool isDisappear, Ease jumpingEase = Ease.InOutSine, float delay = 0) {
            var trans = cachedTransform;
            trans.DOJump(nextPoint, jumpPower, 1, jumpDuration).SetEase(jumpingEase).SetDelay(delay);

            var angle = Mathf.Sign(trans.position.x - nextPoint.x) * Random.Range(0, 45f);
            trans.DORotate(angle * Vector3.forward, jumpDuration - 0.2f).SetEase(Ease.InOutSine).SetDelay(delay + 0.2f);

            if (isDisappear) {
                delay += jumpDuration + 0.25f;
                Shock(duration: 0.5f, delay);
                Disappear(delay + 0.25f);
            }
        }

        #endregion
    }   
}
