using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TileHop.LiveEvent.BallSpinner {
    public class BallSpinnerFeedback {
        public static bool IsSendFromEditor = false;
        
        private const string FeedbackGoogleDocs =
            "https://docs.google.com/forms/d/e/1FAIpQLSe6SygO0GATrET-zo42ygqaStEUj8437sm1AqTIWq01AFRT2w/formResponse";

        private const string Field_UID       = "entry.487953755";
        private const string Field_STT       = "entry.15185684";
        private const string Field_TotalPool = "entry.793238261";
        private const string Field_Bet       = "entry.677580863";
        private const string Field_Result    = "entry.1548732229";
        private const string Field_WinCoin   = "entry.885409658";
        private const string Field_LoseCoin  = "entry.1903899198";
        private const string Field_Slot1     = "entry.401442068";
        private const string Field_Slot2     = "entry.1133944108";
        private const string Field_Slot3     = "entry.1190836558";
        private const string Field_Slot4     = "entry.1974944708";

        public static void SendWinDataAnalytic(int totalCoin, int amountBet, int reward,
                                                List<BallSpinnerData.RewardSpinnerData> winData) {
            if (Application.isEditor && !IsSendFromEditor) {
                Logger.EditorLog("Ball Spinner","Dont send data to gg sheet!");
                return;
            }
            int STT = BallSpinnerData.GetSTT();
            string UID = SystemInfo.deviceUniqueIdentifier;
            string slot1 = string.Empty;
            string slot2 = string.Empty;
            string slot3 = string.Empty;
            string slot4 = string.Empty;
            byte index = 0;
            if (winData.Count > index) {
                slot1 =
                    $"{winData[index].index}- {winData[index].idShape} - {winData[index].match} - {winData[index].reward}";
            }

            index = 1;
            if (winData.Count > index) {
                slot2 =
                    $"{winData[index].index}- {winData[index].idShape} - {winData[index].match} - {winData[index].reward}";
            }

            index = 2;
            if (winData.Count > index) {
                slot3 =
                    $"{winData[index].index}- {winData[index].idShape} - {winData[index].match} - {winData[index].reward}";
            }

            index = 3;
            if (winData.Count > index) {
                slot4 =
                    $"{winData[index].index}- {winData[index].idShape} - {winData[index].match} - {winData[index].reward}";
            }

            BallSpinnerData.SetSTT(STT + 1);
            Configuration.instance.StartCoroutine(BallSpinnerFeedback.IEObtainSheetData(UID, STT, totalCoin, amountBet,
                true, reward, 0, slot1, slot2, slot3, slot4));
        }

        public static void SendLoseDataAnalytic(int totalCoin, int amountBet) {
            if (Application.isEditor && !IsSendFromEditor) {
                Logger.EditorLog("Ball Spinner","Dont send data to gg sheet!");
                return;
            }
            int STT = BallSpinnerData.GetSTT();
            string UID = SystemInfo.deviceUniqueIdentifier;
            BallSpinnerData.SetSTT(STT + 1);
            Configuration.instance.StartCoroutine(
                BallSpinnerFeedback.IEObtainSheetData(UID, STT, totalCoin, amountBet, false, 0, amountBet));
        }
        public static IEnumerator IEObtainSheetData(string UID, int STT, int totalPool, int bet, bool iswin,
                                                    int wincoin = 0, int losecoin = 0, string winslot1 = "",
                                                    string winslot2 = "", string winslot3 = "", string winslot4 = "") {
            WWWForm form = new WWWForm();
            form.AddField(Field_UID, UID);
            form.AddField(Field_STT, STT);
            form.AddField(Field_TotalPool, totalPool);
            form.AddField(Field_Bet, bet);
            form.AddField(Field_Result, iswin ? "win" : "lose");
            if (iswin) {
                form.AddField(Field_WinCoin, wincoin);
                form.AddField(Field_Slot1, winslot1);
                form.AddField(Field_Slot2, winslot2);
                form.AddField(Field_Slot3, winslot3);
                form.AddField(Field_Slot4, winslot4);
            } else {
                form.AddField(Field_LoseCoin, losecoin);
            }

            byte[] rawData = form.data;
            WWW www = new WWW(FeedbackGoogleDocs, rawData);
            yield return www;
            Logger.EditorLog("Ball Spinner", "Send google Done " + www.error);
            www.Dispose();
        }
    }
}