using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.LiveEvent.BallSpinner {
    public class UITooltipOthers : UITooltip {
        [SerializeField] private Transform tooltipShop;
        [SerializeField] private Vector3   offsetShop;
        [SerializeField] private Transform tooltipMission;
        [SerializeField] private Vector3   offsetMission;

        private Button _btnShop;
        private Button _btnMission;

        protected override void OnDisable() {
            base.OnDisable();
            if (_btnShop) {
                _btnShop.onClick.RemoveListener(Hide);
            }

            if (_btnMission) {
                _btnMission.onClick.RemoveListener(Hide);
            }
        }

        public void Show(Button btnShop, Button btnMission) {
            _btnShop = btnShop;
            _btnMission = btnMission;
            highLights = new[] {_btnShop.transform, _btnMission.transform};
            if (_btnShop) {
                _btnShop.onClick.AddListener(Hide);
                tooltipShop.position = _btnShop.transform.position + offsetShop;
            }

            if (_btnMission) {
                _btnMission.onClick.AddListener(Hide);
                tooltipMission.position = _btnMission.transform.position + offsetMission;
            }

            this.gameObject.SetActive(true);
        }
    }
}