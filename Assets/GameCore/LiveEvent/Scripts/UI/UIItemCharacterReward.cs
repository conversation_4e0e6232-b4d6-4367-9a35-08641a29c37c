using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace TileHop.LiveEvent {
    public class UIItemCharacterReward : OptimizedCellView {
        // ~~~~~~~~~~~~~ public ~~~~~~~~~~~~~
        [SerializeField] private Text                    txtName;
        [SerializeField] protected CharactersPreviewScript charactersPreviewScript;

        //private RectTransform RectTransformProgressText => txtProgress.transform as RectTransform;

        // ~~~~~~~~~~~~~ private ~~~~~~~~~~~~~ 
        private int        _indexGroup;
        private int        _indexBall;
        private BallConfig _ballConfig;

        private int   _unlockCount       = 0;
        private float _currentFillAmount = 1f;

        private SkinQuestItem           _skinQuestItem;
        private SongGroupData           _groupData;
        private UIScrollGroupReward     _groupUI;
        public static event Action<int> OnClickItem;

        private void Awake() {
            if (TryGetComponent(out Button btn)) {
                btn.onClick.AddListener(OnBtnClicked);
            }
        }

        private void Start() {
            charactersPreviewScript.ResetMaterial();
        }

        private void OnEnable() {
            UIScrollGroupReward.OnSelectGroup += UIScrollGroupRewardOnOnSelectGroup;

            if (_groupUI != null) {
                UIScrollGroupRewardOnOnSelectGroup(0, _groupUI.IndexSelected);
            }
        }

        private void OnDisable() {
            UIScrollGroupReward.OnSelectGroup -= UIScrollGroupRewardOnOnSelectGroup;
        }

        private void UIScrollGroupRewardOnOnSelectGroup(int order, int idSelect) {
            if (_indexGroup == idSelect) {
                Play();
            } else {
                Pause();
            }
        }

        public virtual void SetData(int order, SkinQuestItem skinQuestItem, int indexGroup, UIScrollGroupReward parent) {
            this._groupUI = parent;
            this._indexGroup = indexGroup;
            this._skinQuestItem = skinQuestItem;
            this._groupData = _skinQuestItem._skinQuestData.Groups[indexGroup];
            this._unlockCount = _skinQuestItem._skinQuestProgress.groups[indexGroup].CountUnlockSong;
            ParseData(order);
            UpdateProgress(_skinQuestItem._skinQuestProgress.groups[indexGroup]);
        }

        private void UpdateProgress(SongGroupProgress progress) {
            this._currentFillAmount = progress.CountUnlockSong / (float)progress.CountTotalSong;
            //RectTransformProgressText.anchorMin = new Vector2(0, 1 - _currentFillAmount);
        }

        private void ParseData(int order) {
            switch (_groupData.GiftType) {
                case "Ball":
                    if (int.TryParse(_groupData.GiftValue, out int temp)) {
                        _indexBall = temp;
                        _ballConfig = BallManager.instance.GetBallConfig(_indexBall);
                        this.txtName.text = _ballConfig == null ? string.Empty : _ballConfig.name;
                        charactersPreviewScript.Init(_indexBall, OnCharacterPreviewClick);
                        UIScrollGroupRewardOnOnSelectGroup(order, _skinQuestItem.IdCurrentGroup);
                    } else {
                        Debug.LogError("error!!!");
                    }
                    break;
                default:
                    Debug.LogError("Chưa handle");
                    break;
            }
        }

        public void UpdateData(SongGroupProgress progress, Action<bool> onComplete) {
            float targetFillAmount = progress.CountUnlockSong / (float)progress.CountTotalSong;
            if (this._unlockCount != progress.CountUnlockSong) {
                this._unlockCount = progress.CountUnlockSong;
                // do
                DOTween.To(() => _currentFillAmount, x => _currentFillAmount = x, targetFillAmount, .6f).OnUpdate(
                    () => {
                        //imgBar.fillAmount = _currentFillAmount;
                        //RectTransformProgressText.anchorMin = new Vector2(0, 1 - _currentFillAmount);
                    }).OnComplete(() => {
                    this._currentFillAmount = targetFillAmount;
                    //imgBar.fillAmount = _currentFillAmount;
                    //RectTransformProgressText.anchorMin = new Vector2(0, 1 - _currentFillAmount);

                    if (!progress.GotReward && progress.CountUnlockSong >= progress.CountTotalSong) {
                        // Đạt phần thưởng của group
                        onComplete?.Invoke(true);
                    } else {
                        onComplete?.Invoke(false);
                    }
                });
            } else {
                onComplete?.Invoke(false);
                Debug.LogError($"Why count unlock song is not change {_unlockCount} {progress.CountUnlockSong} ???");
            }
        }

        private void OnCharacterPreviewClick(CharactersPreviewScript preview) {
            OnBtnClicked();
        }
        private void OnBtnClicked() {
            OnClickItem?.Invoke(_indexGroup);
        }

        public virtual void Play() {
            charactersPreviewScript.Play();
        }

        public virtual void Pause() {
            charactersPreviewScript.Pause();
        }

        public override void SetData(IData _data) {
        }
    }
}