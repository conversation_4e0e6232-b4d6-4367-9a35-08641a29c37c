using TileHop.UI;

namespace TileHop.MysteryBox {
    public class UIButtonMysteryBox : ButtonWithBadge {
        private void Start() {
            mainButton.onClick.AddListener(OnBtnMysteryBoxClicked);
        }

        protected  virtual  void OnEnable() {
            MysteryBox.OnChangeFreeGiftStatus += MysteryBoxOnOnChangeFreeGiftStatus;
            MysteryBoxOnOnChangeFreeGiftStatus(MysteryBox.instance.HaveFreeGift);
        }

        private void OnDisable() {
            MysteryBox.OnChangeFreeGiftStatus -= MysteryBoxOnOnChangeFreeGiftStatus;
        }

        private void OnBtnMysteryBoxClicked() {
            SoundManager.PlayGameButton();
            AnalyticHelper.Button_Click(BUTTON_NAME.MysteryBox);
            MysteryBox.instance.ShowMysteryBox(MysteryBoxLocation.BtnMysteryBox);
        }
        protected  virtual void MysteryBoxOnOnChangeFreeGiftStatus(bool haveFreeReward) {
            badgeHolder.ShowBadge(haveFreeReward);
        }
    }
}