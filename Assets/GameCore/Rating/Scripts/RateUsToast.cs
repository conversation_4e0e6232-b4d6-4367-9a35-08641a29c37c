using System;
using System.Collections;
using DG.Tweening;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class RateUsToast : MonoBehaviour {
    [SerializeField] private Image imgPopup;
    [SerializeField] private Image imgIcon;
    [SerializeField] private Text  txtThanks;
    [SerializeField] private float timeAnim   = 0.2f;
    [SerializeField] private float timeAppear = 1.5f;
    private IEnumerator Start() {
        var color = imgPopup.color;
        color.a = 0;
        imgPopup.color = color;
        color = imgIcon.color;
        color.a = 0;
        imgIcon.color = color;
        color = txtThanks.color;
        color.a = 0;
        txtThanks.color = color;

        imgPopup.DOFade(1f, timeAnim);
        imgIcon.DOFade(1f, timeAnim);
        txtThanks.DOFade(1f, timeAnim);
        yield return YieldPool.GetWaitForSeconds(timeAppear);
        imgPopup.DOFade(0f, timeAnim);
        imgIcon.DOFade(0f, timeAnim);
        txtThanks.DOFade(0f, timeAnim);
        yield return YieldPool.GetWaitForSeconds(timeAnim);
        Destroy(this.gameObject);
    }
}