using System.Collections.Generic;
using UnityEngine;
using Firebase.Auth;
using Firebase.Database;
using Facebook.Unity;
using Facebook.MiniJSON;
using System;
using System.Linq;
using System.Text;
using Inwave;
using System.Threading.Tasks;
using Firebase;
using Firebase.Extensions;

public struct FB_DB_KEY {
    // song keys
    public static string name       = "name";
    public static string path       = "path";
    public static string fullPath   = "fullPath";
    public static string uploadedBy = "uploadedBy";
    public static string ownerId    = "ownerId";
    public static string rankPoint  = "rankPoint";
    public static string shareCount = "shareCount";
    public static string likeCount  = "likeCount";

    //User attribute;
    public static string auId                 = "auId";
    public static string fbId                 = "fbId";
    public static string email                = "email";
    public static string photoUrl             = "photoUrl";
    public static string score                = "score";
    public static string scores               = "scores";
    public static string ranks                = "ranks";
    public static string gameData             = "gameData";
    public static string createdDate          = "createdDate";
    public static string lastDate             = "lastDate";
    public static string lang                 = "lang";
    public static string version              = "version";
    public static string likedList            = "likedList";
    public static string sharedList           = "sharedList";
    public static string ownerList            = "ownerList";
    public static string expired1DayVip       = "expired1DayVip";
    public static string favoriteList         = "favoriteList";
    public static string favoriteACMList      = "favoriteACMList";
    public static string friendList           = "friendList";
    public static string favoriteGenres       = "favoriteGenres";
    public static string interactedWithGenres = "interactedWithGenres";
    public static string recentlyPlayedSongs  = "recentlyPlayedSongs";
}

public class UserEntry : IData {
    public string auId;
    public string fbId;
    public string name;
    public string email;
    public string photoUrl;

    public Dictionary<string, int>  scores = new Dictionary<string, int>();
    public Dictionary<string, long> ranks  = new Dictionary<string, long>();

    public string gameData;
    public string createdDate;
    public string lastDate;
    public string lang;
    public string version;
    public string profileKey;
    public string friendList;

    public int interactedWithFavoriteGenres;

    public List<string> favoriteGenres      = new List<string>();
    public List<string> recentlyPlayedSongs = new List<string>();

    private static List<string> sharedList    = new List<string>();
    private static List<string> likedList     = new List<string>();
    private static List<string> _favoriteList = new List<string>(); //old use path, new use ACM ID v4
    private static List<string> _unlockSongs  = new List<string>();
    private static List<string> ownerList     = new List<string>();

    // dùng để check contains
    private static HashSet<string> _unlockedSongHashSet;

    private static List<FirebaseItem> ownerSongs = new List<FirebaseItem>();

    //public Dictionary<string, FirebaseItem> ownerList = new Dictionary<string, FirebaseItem>();
    public UserEntry() {
    }

    public UserEntry(DataSnapshot snapshot, string boardid, long rank) {
        this.auId = snapshot.Key;
        this.ranks[boardid] = rank;
        this.scores[boardid] = int.Parse(snapshot.Value.ToString());
        this.profileKey = "/" + CONFIG_STRING.DBKey_Users + "/" + auId;
    }

    public UserEntry(FirebaseUser user, string fbId) {
        this.auId = user.UserId;
        this.fbId = fbId;
        this.name = user.DisplayName;
        this.email = user.Email;
#if UNITY_EDITOR
        this.fbId = "100000098730984";
        this.auId = "gOTT8AKR7KgplX7UArquBylINOH3";
        this.name = "Trương Xuân Hưng";
        this.photoUrl = "https://graph.facebook.com/2289857351225376/picture?type=large";
#endif
        this.profileKey = "/" + CONFIG_STRING.DBKey_Users + "/" + auId;

        photoUrl = GetPhotoUrlOfCurrentUser(user);

        if (LeaderBoardUI.instance != null) {
            LeaderBoardUI.instance.SetProfilePhoto(photoUrl, user?.DisplayName);
            //LeaderBoardUI.instance.SetProfileNameOnTopBarHome(name);
        }
    }

    // Update from firebase snapshot
    public UserEntry(DataSnapshot snapshot) {
        Bind(snapshot);
    }

    public void SetFBID(string id) {
        fbId = id;
    }

    public void Bind(DataSnapshot snapshot) {
        if (snapshot.Value == null) {
            return;
        }

        auId = snapshot.Key;
        profileKey = "/" + CONFIG_STRING.DBKey_Users + "/" + auId;

        if (snapshot.Child(FB_DB_KEY.fbId).Value != null) {
            SetFBID(snapshot.Child(FB_DB_KEY.fbId).Value.ToString());
        }

        photoUrl = snapshot.Child(FB_DB_KEY.photoUrl).Value?.ToString();
        if (string.IsNullOrEmpty(photoUrl) && !string.IsNullOrEmpty(fbId)) {
            photoUrl = $"https://graph.facebook.com/{fbId}/picture?type=large"; // Deprecated by FacebookSDK 8.x
        }

        if (snapshot.Child(FB_DB_KEY.name).Value != null) {
            name = snapshot.Child(FB_DB_KEY.name).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.email).Value != null) {
            email = snapshot.Child(FB_DB_KEY.email).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.score).Value != null) {

            Dictionary<string, System.Object> list = (Dictionary<string, object>) snapshot.Child(FB_DB_KEY.score).Value;

            foreach (var obj in list) {
                try {
                    scores[obj.Key] = int.Parse(obj.Value.ToString());
                } catch {
                    //Debug.Log (e.Message);
                }
            }
        }

        if (snapshot.Child(FB_DB_KEY.gameData).Value != null) {
            gameData = snapshot.Child(FB_DB_KEY.gameData).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.lastDate).Value != null) {
            lastDate = snapshot.Child(FB_DB_KEY.lastDate).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.createdDate).Value != null) {
            createdDate = snapshot.Child(FB_DB_KEY.createdDate).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.lang).Value != null) {
            lang = snapshot.Child(FB_DB_KEY.lang).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.version).Value != null) {
            version = snapshot.Child(FB_DB_KEY.version).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.friendList).Value != null) {
            friendList = snapshot.Child(FB_DB_KEY.friendList).Value.ToString();
        }

        if (snapshot.Child(FB_DB_KEY.favoriteGenres).Value != null) {
            favoriteGenres = Util.StringToList(snapshot.Child(FB_DB_KEY.favoriteGenres).Value.ToString());
        }

        if (snapshot.Child(FB_DB_KEY.recentlyPlayedSongs).Value != null) {
            recentlyPlayedSongs = Util.StringToList(snapshot.Child(FB_DB_KEY.recentlyPlayedSongs).Value.ToString());
        }

        if (snapshot.Child(FB_DB_KEY.interactedWithGenres).Value != null) {
            interactedWithFavoriteGenres = int.Parse(snapshot.Child(FB_DB_KEY.interactedWithGenres).Value.ToString());
        }

        if (FirebaseSDK.instance.auth != null && FirebaseSDK.instance.auth.CurrentUser != null &&
            FirebaseSDK.instance.auth.CurrentUser.UserId == auId) {
            string favStr = PlayerPrefs.GetString(FB_DB_KEY.favoriteACMList, "");
            if (snapshot.Child(FB_DB_KEY.favoriteACMList).Value != null) {
                favStr += FileHelper.Split + snapshot.Child(FB_DB_KEY.favoriteACMList).Value;

            } else if (snapshot.Child(FB_DB_KEY.favoriteList).Value != null) { //remove this late
                favStr += FileHelper.Split + snapshot.Child(FB_DB_KEY.favoriteList).Value;
            }

            _favoriteList = Util.StringToList(favStr);

            string likeStr = PlayerPrefs.GetString(FB_DB_KEY.likedList, "");
            if (snapshot.Child(FB_DB_KEY.likedList).Value != null) {
                likeStr += FileHelper.Split + snapshot.Child(FB_DB_KEY.likedList).Value;
            }

            likedList = Util.StringToList(likeStr);

            string shareStr = PlayerPrefs.GetString(FB_DB_KEY.sharedList, "");
            if (snapshot.Child(FB_DB_KEY.sharedList).Value != null) {
                shareStr += FileHelper.Split + snapshot.Child(FB_DB_KEY.sharedList).Value;
            }

            sharedList = Util.StringToList(shareStr);

            string ownerStr = PlayerPrefs.GetString(FB_DB_KEY.ownerList, "");
            if (snapshot.Child(FB_DB_KEY.ownerList).Value != null) {
                ownerStr += FileHelper.Split + snapshot.Child(FB_DB_KEY.ownerList).Value;
            }

            ownerList = Util.StringToList(ownerStr);

            FirebaseItemListing.SearchByKeys(ownerList, (result) => {
                ownerSongs = result;
                SongManager.instance.MergeLocalSongList(ownerSongs);
            });
        }
    }

    private string GetPhotoUrlOfCurrentUser(FirebaseUser user) {
        string url = null;
        switch (FirebaseSDK.instance.authTypeLogin) {
            case FirebaseSDK.AuthType.Facebook:
                //photoUrl = "https://graph.facebook.com/" + fbId + "/picture?type=large";
                url = user.PhotoUrl?.ToString();
                if (url != null && !url.Contains("?type=")) {
                    url += "?type=large";
                }
                //Debug.Log("[UserEntry] photoUrl: " + photoUrl);

                break;

            case FirebaseSDK.AuthType.Google:
                url = user.PhotoUrl?.ToString();
                //Debug.Log("[UserEntry] photoUrl: " + photoUrl);

                break;

            case FirebaseSDK.AuthType.Apple:
                break;

        }

        return url;
    }

    public static List<string> GetLikeList() {
        if (likedList.Count > 0) {
            return likedList;
        } else {
            likedList = Util.StringToList(PlayerPrefs.GetString(FB_DB_KEY.likedList, ""));
            return likedList;
        }

    }

    public static List<string> GetFavoriteList() {
        if (_favoriteList.Count > 0) {
            return _favoriteList;

        } else {
            if (PlayerPrefs.HasKey(FB_DB_KEY.favoriteList)) { //Remove after all user converted
                ConvertFavoriteList(FB_DB_KEY.favoriteList, FB_DB_KEY.favoriteACMList);
                PlayerPrefs.DeleteKey(FB_DB_KEY.favoriteList);
            }

            _favoriteList = Util.StringToList(PlayerPrefs.GetString(FB_DB_KEY.favoriteACMList, ""));
            return _favoriteList;
        }
    }

    private static void ConvertFavoriteList(string keyPath, string keyAcmId) {
        List<string> listPath = Util.StringToList(PlayerPrefs.GetString(keyPath, ""));
        List<string> acmIdSongs = SongManager.instance.GetSongAcmIDsByPaths(listPath);
        string update = string.Join(FileHelper.Split, acmIdSongs);
        PlayerPrefs.SetString(keyAcmId, update);
    }

    public static List<string> GetShareList() {
        if (sharedList.Count > 0) {
            return sharedList;
        } else {
            sharedList = Util.StringToList(PlayerPrefs.GetString(FB_DB_KEY.sharedList, ""));
            return sharedList;
        }

    }

    public static List<string> GetOwnerList() {
        if (ownerList.Count > 0) {
            return ownerList;
        } else {
            ownerList = Util.StringToList(PlayerPrefs.GetString(FB_DB_KEY.ownerList, ""));
            return ownerList;
        }

    }

    public void SaveProfiles() {
        try {

            if (auId != null) {

                // if profileKey isn't valid -> can't update firebaseDatabase
                if (string.IsNullOrWhiteSpace(profileKey)) {
                    return;
                }

                Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();

                // User Name
                if (!string.IsNullOrEmpty(name)) {
                    childUpdates[profileKey + "/" + FB_DB_KEY.name] = name;
                }

                // Email
                if (!string.IsNullOrEmpty(email)) {
                    childUpdates[profileKey + "/" + FB_DB_KEY.email] = email;
                }

                // Facebook ID Link
                if (!string.IsNullOrEmpty(fbId)) {
                    childUpdates[profileKey + "/" + FB_DB_KEY.fbId] = fbId;
                    childUpdates["/" + CONFIG_STRING.DBKey_Links + "/" + fbId] = auId;
                }

                //Photo URL
                if (!string.IsNullOrEmpty(photoUrl)) { //Save link photo for acc Google + Apple
                    childUpdates[profileKey + "/" + FB_DB_KEY.photoUrl] = photoUrl;
                }

                // Scores
                if (scores != null) {
                    // if auId isn't valid -> can't update firebaseDatabase
                    if (!string.IsNullOrWhiteSpace(auId)) {
                        Dictionary<string, System.Object> child = new Dictionary<string, System.Object>();
                        foreach (KeyValuePair<string, int> pair in scores) {
                            if (!string.IsNullOrWhiteSpace(pair.Key)) {
                                child[pair.Key] = pair.Value;
                                childUpdates["/" + CONFIG_STRING.DBKey_LeaderBoards + "/" + pair.Key + "/" + auId] =
                                    pair.Value;
                            }
                        }

                        childUpdates[profileKey + "/" + FB_DB_KEY.score] = child;
                    }
                }

                UpdateAsyncFirebase(childUpdates); // save profiles
            }
        } catch (Exception ex) {
            Debug.Log("SaveProfiles Error: " + ex.ToString());
        }

    }

    public void Delete(string boardID) {
        if (auId == null) {
            return;
        }
        // FirebaseSDK.GetValueFromDB(profileKey, (result) => {
        //     result?.Reference?.RemoveValueAsync()?.ContinueWithOnMainThread(T => {
        //         Debug.Log("Deleted"); //
        //     });
        // });

        BoardManager.instance.DeleteData(boardID, auId);

        //DeleteSongRank(SongManager.instance.songs.Values.ToList(), 0);
    }

    private void DeleteSongRank(List<Song> list, int i) {
        FirebaseSDK.GetValueFromDB(
            "/" + CONFIG_STRING.DBKey_LeaderBoards + "/" + Util.SongToBoardId(list[i].path) + "/" + auId, (result) => {
                if (result != null && result.Value != null) {
                    result?.Reference?.RemoveValueAsync()?.ContinueWithOnMainThread(task => {
                        Debug.Log("Deleted3:" + Util.SongToBoardId(list[i].path) + "-" + result.Reference.Key + "-" +
                                  result.Value); //
                    });
                } else {
                    Debug.Log("Deleted3:" + Util.SongToBoardId(list[i].path) + "-" + result?.Reference.Key + "- null");
                }

                if (i < list.Count - 1) {
                    DeleteSongRank(list, i + 1);
                }
            });
    }

    public void SaveScores(string boardId, int score, int total) {
        if (string.IsNullOrEmpty(boardId)) {
            return;
        }

        if (string.IsNullOrEmpty(auId)) {
            return;
        }

        try {
            // Update attribute
            scores[boardId] = score;
            scores[CONFIG_STRING.DBKey_Total] = total;

            /* * Push to remote db */
            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();

            //Update leaderboard score
            childUpdates["/" + CONFIG_STRING.DBKey_LeaderBoards + "/" + boardId + "/" + auId] = score;
            childUpdates["/" + CONFIG_STRING.DBKey_LeaderBoards + "/" + CONFIG_STRING.DBKey_Total + "/" + auId] = total;

            //Update profile score
            childUpdates[profileKey + "/" + FB_DB_KEY.score + "/" + boardId] = score;
            childUpdates[profileKey + "/" + FB_DB_KEY.score + "/" + CONFIG_STRING.DBKey_Total] = total;

            UpdateAsyncFirebase(childUpdates); // update scores

            //Update new ranks
            SetMyRank(boardId);
            SetMyRank(CONFIG_STRING.DBKey_Total);
        } catch (Exception ex) {
            Debug.Log("SaveScores Error: " + ex.ToString());
        }

        SaveProfiles();
    }

    public void UpdateScoreIntoDB(string db, string boardID, int score, int total) {
        try {

            // if db songID auId isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(db) || string.IsNullOrWhiteSpace(boardID) ||
                string.IsNullOrWhiteSpace(auId)) {
                return;
            }

            /* * Push to remote db */
            Dictionary<string, System.Object> childUpdates = new();

            //save score for day
            childUpdates["/" + db + "/" + boardID + "/" + auId] = score;
            childUpdates["/" + db + "/" + CONFIG_STRING.DBKey_Total + "/" + auId] = total;

            UpdateAsyncFirebase(childUpdates, true); //allow update in editor to test

        } catch (Exception ex) {
            CustomException.Fire("[UpdateScoreIntoDB]", ex.Message);
        }
    }

    public static void SaveShare(Song song) {
        sharedList.Add(song.key);
        SubmitDB_Share();
    }

    public static void SaveOwner(Song song) {
        ownerList.Add(song.key);
        SubmitDB_Owner();
    }

    public static void RemoveOwner(Song song) {
        ownerList.Remove(song.key);
        SubmitDB_Owner();
    }

    public static void SaveLike(Song song) {
        likedList.Add(song.key);
        SubmitDB_Like();
    }

    public static void RemoveLike(Song song) {
        likedList.Remove(song.key);
        SubmitDB_Like();
    }

    public static Action onFavoriteChange;

    public static void SaveFavorite(Song song) {
        AnalyticHelper.FireEvent(FIRE_EVENT.Add_Favorite, new Dictionary<string, object> {
            { TRACK_NAME.item_name, song.name }, { TRACK_NAME.item_id, song.path }
        });

        _favoriteList.Add(song.acm_id_v3);
        SubmitDB_Favorite();

        Util.ShowSmallMessage(LocalizationManager.instance.GetLocalizedValue("ADDED_TO_FAVORITES"));
        onFavoriteChange?.Invoke();
    }

    public static void RemoveFavorite(Song song) {
        AnalyticHelper.FireEvent(FIRE_EVENT.Remove_Favorite, new Dictionary<string, object> {
            { TRACK_NAME.item_name, song.name }, { TRACK_NAME.item_id, song.path }
        });
        _favoriteList.Remove(song.acm_id_v3);
        SubmitDB_Favorite();
        Util.ShowSmallMessage(LocalizationManager.instance.GetLocalizedValue("REMOVED_FAVORITES"));
        onFavoriteChange?.Invoke();
    }

    public void AddFavoriteGenre(string selectedGenre) {
        favoriteGenres.Add(selectedGenre);
    }

    public void RemoveFavoriteGenre(string selectedGenre) {
        favoriteGenres.Remove(selectedGenre);
    }

    public static void SubmitDB_Owner() {
        string update = string.Join(FileHelper.Split, ownerList.ToArray());
        PlayerPrefs.SetString(FB_DB_KEY.ownerList, update);

        /* * Push to remote db */
        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.ownerList] = update;
            UpdateAsyncFirebase(childUpdates); //submit owner data
        }
    }

    static void SubmitDB_Share() {

        string update = string.Join(FileHelper.Split, sharedList.ToArray());
        PlayerPrefs.SetString(FB_DB_KEY.sharedList, update);

        /* * Push to remote db */
        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.sharedList] = update;
            UpdateAsyncFirebase(childUpdates); //shubmit share data
        }
    }

    private static void UpdateAsyncFirebase(Dictionary<string, object> childUpdates, bool isUpdateInEditor = false) {
        if (!Application.isEditor || (Application.isEditor && isUpdateInEditor)) {
            Dictionary<string, object> tempDict = new Dictionary<string, object>(childUpdates);
            foreach (KeyValuePair<string, object> o in tempDict) {
                string key = o.Key;
                if (IsUnvalidKey(key)) {
                    LogUnValidKey(childUpdates);
                    childUpdates.Remove(key);
                }
            }

            FirebaseSDK.instance.GetDB()?.UpdateChildrenAsync(childUpdates);
        }
    }

    private static bool IsUnvalidKey(string key) {
        // '.' '#' '$' '[' or ']'
        return key.Contains('.') || key.Contains('#') || key.Contains('$') || key.Contains('[') || key.Contains(']');
    }

    private static void LogUnValidKey(Dictionary<string, object> objects) {
        StringBuilder sb = new();
        sb.AppendLine("Total Key:" + " => " + objects.Count);

        foreach (KeyValuePair<string, object> o in objects) {
            string key = o.Key;
            if (IsUnvalidKey(key)) {
                sb.AppendLine(key + " => " + o.Value);
            } else if (key.Contains("name") || key.Contains("Users")) {
                sb.AppendLine(key + " => " + o.Value);
            } else if (!key.Contains("LeaderBoardsPerfect")) {
                sb.AppendLine(key + " => " + o.Value);
            }
        }

        CustomException.SetKey(FirebaseKey.UnValidKey, sb.ToString());
        CustomException.Fire("[LogUnValidKey]", "More in Key UnValidKey");
    }

    static void SubmitDB_Favorite() {
        string update = string.Join(FileHelper.Split, _favoriteList.ToArray());
        PlayerPrefs.SetString(FB_DB_KEY.favoriteACMList, update);

        /* * Push to remote db */
        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.favoriteACMList] = update;
            //childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.favoriteList] = null; //remove this late
            UpdateAsyncFirebase(childUpdates); //submit favor data
        }
    }

    static void SubmitDB_Like() {
        string update = string.Join(FileHelper.Split, likedList.ToArray());
        PlayerPrefs.SetString(FB_DB_KEY.likedList, update);

        /* * Push to remote db */
        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.likedList] = update;
            UpdateAsyncFirebase(childUpdates); // submib like data
        }
    }

    public void SubmitDB_FavoriteGenres() {
        string update = string.Join(FileHelper.Split, favoriteGenres.ToArray());
        update = FormatFavoriteGenresString(update);
        PlayerPrefs.SetString(FB_DB_KEY.favoriteGenres, update);

        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();

            var savedFavoriteGenres = GetAndFormatLocalFavoriteGenres();

            if (savedFavoriteGenres != null) {
                childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.favoriteGenres] = savedFavoriteGenres;
            }

            //Check if interaction is changed or not
            if (PlayerPrefs.GetInt(PlayerPrefsKey.InteractedWithFavoriteGenres, 0) !=
                CoreUser.instance.user.interactedWithFavoriteGenres) {
                childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.interactedWithGenres] =
                    PlayerPrefs.GetInt(PlayerPrefsKey.InteractedWithFavoriteGenres, 0);
            }

            Debug.LogError("Update favorite genres and push to DB");
            UpdateAsyncFirebase(childUpdates); // submit favorite genres data
        }
    }

    public void SubmitDB_RecentlyPlayedSongs() {
        if (CoreUser.instance.user != null) {
            // if profileKey isn't valid -> can't update firebaseDatabase
            if (string.IsNullOrWhiteSpace(CoreUser.instance.user.profileKey)) {
                return;
            }

            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            var listRecentSong = CoreData.GetPlayerData().GetRecentSongs();
            var savedRecentlyPlayedSong = listRecentSong.Take(Mathf.Min(9, listRecentSong.Count)).ToList();
            if (!savedRecentlyPlayedSong.IsNullOrEmpty()) {
                childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.recentlyPlayedSongs] =
                    savedRecentlyPlayedSong;
            }

            Debug.LogError("Update recent songs and push to DB");
            UpdateAsyncFirebase(childUpdates); // submit favorite genres data
        }
    }

    private string GetAndFormatLocalFavoriteGenres() {
        string savedFavoriteGenres = "";

        //Get DB Favorite genres
        if (PlayerPrefs.HasKey(FB_DB_KEY.favoriteGenres)) {
            savedFavoriteGenres = PlayerPrefs.GetString(FB_DB_KEY.favoriteGenres);
            if (!string.IsNullOrWhiteSpace(savedFavoriteGenres)) {
                PlayerPrefs.SetInt(PlayerPrefsKey.ParsedDataFromDemographic, 1);
                //Convert string to match discovery
                savedFavoriteGenres = FormatFavoriteGenresString(savedFavoriteGenres);
                return savedFavoriteGenres;
            }
        }

        //Cannot get from db, get from demographic instead
        if (PlayerPrefs.HasKey(PlayerPrefsKey.survey_demographic_select_genre) &&
            PlayerPrefs.GetInt(PlayerPrefsKey.ParsedDataFromDemographic, 0) == 0) {
            savedFavoriteGenres = PlayerPrefs.GetString(PlayerPrefsKey.survey_demographic_select_genre);
            if (!string.IsNullOrWhiteSpace(savedFavoriteGenres)) {
                savedFavoriteGenres = FormatFavoriteGenresString(savedFavoriteGenres);
                PlayerPrefs.SetString(FB_DB_KEY.favoriteGenres, savedFavoriteGenres);
                PlayerPrefs.SetInt(PlayerPrefsKey.ParsedDataFromDemographic, 1);
                return savedFavoriteGenres;
            }
        }

        return savedFavoriteGenres;
    }

    public void Backup() {
        // if profileKey isn't valid -> can't update firebaseDatabase
        if (string.IsNullOrWhiteSpace(profileKey)) {
            return;
        }

        Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
        childUpdates[profileKey + "/" + FB_DB_KEY.gameData] = GetGameData();
        childUpdates[profileKey + "/" + FB_DB_KEY.lang] = LocalizationManager.GetLanguageID();
        childUpdates[profileKey + "/" + FB_DB_KEY.lastDate] = DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat);
        childUpdates[profileKey + "/" + FB_DB_KEY.version] = UnityEngine.Application.version;
        childUpdates[profileKey + "/" + FB_DB_KEY.recentlyPlayedSongs] = string.Join(FileHelper.Split,
            CoreData.GetPlayerData().GetRecentSongs().ToArray());

        var listRecentSong = CoreData.GetPlayerData().GetRecentSongs();
        var savedRecentlyPlayedSong = listRecentSong.Take(Mathf.Min(9, listRecentSong.Count)).ToList();
        if (!savedRecentlyPlayedSong.IsNullOrEmpty()) {
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.recentlyPlayedSongs] =
                savedRecentlyPlayedSong;
        }

        string update = string.Join(FileHelper.Split, favoriteGenres.ToArray());
        update = FormatFavoriteGenresString(update);
        PlayerPrefs.SetString(FB_DB_KEY.favoriteGenres, update);
        var savedFavoriteGenres = GetAndFormatLocalFavoriteGenres();
        if (savedFavoriteGenres != null) {
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.favoriteGenres] = savedFavoriteGenres;
        }

        //Check if interaction is changed or not
        if (PlayerPrefs.GetInt(PlayerPrefsKey.InteractedWithFavoriteGenres, 0) !=
            CoreUser.instance.user.interactedWithFavoriteGenres) {
            childUpdates[CoreUser.instance.user.profileKey + "/" + FB_DB_KEY.interactedWithGenres] =
                PlayerPrefs.GetInt(PlayerPrefsKey.InteractedWithFavoriteGenres, 0);
        }

        // createdDate
        if (createdDate != null) {
            childUpdates[profileKey + "/" + FB_DB_KEY.createdDate] = createdDate;
            createdDate = null;
        }

        UpdateAsyncFirebase(childUpdates); // backup data
    }

    /// <summary>
    /// Tổng hợp data local hiện có -> để compare với dữ liệu trên server
    /// </summary>
    public void FromLocal() {
        this.createdDate = DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat);
        this.lastDate = createdDate;
        this.gameData = GetGameData();
        this.lang = LocalizationManager.GetLanguageID();
        this.photoUrl = string.Empty;
        this.scores = GetScoreData();
        this.version = Application.version;
        this.favoriteGenres = GetLocalFavoriteGenresList();
        this.recentlyPlayedSongs = GetLocalRecentlyPlayed();
        this.interactedWithFavoriteGenres = PlayerPrefs.GetInt(PlayerPrefsKey.InteractedWithFavoriteGenres, 0);
    }

    /// <summary>
    /// Tổng hợp score hiện có -> để compare với dữ liệu trên server
    /// </summary>
    /// <returns></returns>
    private Dictionary<string, int> GetScoreData() {
        var scores = new Dictionary<string, int>();
        try {
            // Update scores to user object
            int totalScore = 0;
            IEnumerable<Song> valueCollection = SongManager.instance.songs.Values;
            foreach (Song song in valueCollection) {
                if (song == null) {
                    continue;
                }

                SONGTYPE type = song.savedType;
                if (type != SONGTYPE.OPEN) {
                    continue;
                }

                int score = Configuration.GetBestScore(song.path);
                if (score > 0) {
                    string boardID = Util.SongToBoardId(song.path);
                    scores[boardID] = score;
                    totalScore += score;
                }
            }

            scores[CONFIG_STRING.DBKey_Total] = totalScore;
        } catch (Exception e) {
            CustomException.Fire("[GetScoreData]", e.Message);
        }

        return scores;
    }

    public long GetMyRank(string boardID) {
        if ((!ranks.ContainsKey(boardID)) || ranks[boardID] == 0) {
            SetMyRank(boardID);
        }

        if (ranks.ContainsKey(boardID)) {
            return ranks[boardID];
        } else {
            return 0;
        }
    }

    private List<string> GetLocalFavoriteGenresList() {
        var favGenres = new List<string>();

        var favoriteGenresString = GetAndFormatLocalFavoriteGenres();
        if (!string.IsNullOrEmpty(favoriteGenresString)) {
            favGenres = Util.StringToList(favoriteGenresString);
        }

        return favGenres;
    }

    private List<string> GetLocalRecentlyPlayed() {
        return CoreData.GetPlayerData().GetRecentSongs();
    }

    //Convert genres to to match song discovery
    public static string FormatFavoriteGenresString(string joinedString) {
        joinedString = joinedString.ToUpper() //cho giống GenreType
            .Replace("GENRE_CLASSICAL", "CLASSICAL_OPERA_ORCHESTRA").Replace("GENRE_", "") // do sử dụng localizedId
            .Replace("HIP-HOP-RAP", "HIP-HOP/RAP").Replace("RNB", "R&B")
            .Replace("DANCE-EDM", "DANCE;EDM") //Tách 2 GenreType
            .Replace("INDIE-ALTERNATIVE", "ALTERNATIVE/INDIE") //Tách 2 GenreType
            .Replace('/', '_');
        return joinedString;
    }

    public void SetMyRank(string boardID) {
        // Update all list;
        CoreUser.instance.GetFriendList(boardID);
    }

    public string GetGameData() {

        Dictionary<string, System.Object> data = new Dictionary<string, System.Object>();

        data[CONFIG_STRING.NoAds] = PlayerPrefs.GetInt(CONFIG_STRING.NoAds);
        data[CONFIG_STRING.Bonus_Login] = PlayerPrefs.GetInt(CONFIG_STRING.Bonus_Login);
        data[CONFIG_STRING.Bonus_Sharing] = PlayerPrefs.GetInt(CONFIG_STRING.Bonus_Sharing);
        data[CONFIG_STRING.Diamonds] = PlayerPrefs.GetInt(CONFIG_STRING.Diamonds);
        data[CONFIG_STRING.ThemesUnlocked] = PlayerPrefs.GetString(CONFIG_STRING.ThemesUnlocked);
        data[CONFIG_STRING.SelectedTheme] = PlayerPrefs.GetInt(CONFIG_STRING.SelectedTheme);
        data[CONFIG_STRING.Balls] = PlayerPrefs.GetString(CONFIG_STRING.Balls);
        data[CONFIG_STRING.SelectedBall] = PlayerPrefs.GetInt(CONFIG_STRING.SelectedBall);

        data[CONFIG_STRING.RateLater] = PlayerPrefs.GetInt(CONFIG_STRING.RateLater, 0);
        data[CONFIG_STRING.DailyGiftNumber] = PlayerPrefs.GetInt(CONFIG_STRING.DailyGiftNumber);
        data[CONFIG_STRING.DailyGiftDate] = PlayerPrefs.GetString(CONFIG_STRING.DailyGiftDate);

        data[CONFIG_STRING.Retry] = PlayerPrefs.GetInt(CONFIG_STRING.Retry);
        data[CONFIG_STRING.Revive] = PlayerPrefs.GetInt(CONFIG_STRING.Revive);
        data[CONFIG_STRING.IsTop1] = PlayerPrefs.GetInt(CONFIG_STRING.IsTop1);
        data[CONFIG_STRING.PerfectCount] = PlayerPrefs.GetInt(CONFIG_STRING.PerfectCount);
        data[CONFIG_STRING.TutorialPerfect] = PlayerPrefs.GetInt(CONFIG_STRING.TutorialPerfect);
        data[CONFIG_STRING.NormalPerfect] = PlayerPrefs.GetInt(CONFIG_STRING.NormalPerfect);
        data[AchievementType.TWITTER_SHARE.ToString()] =
            PlayerPrefs.GetString(AchievementType.TWITTER_SHARE.ToString());

        foreach (Song song in SongManager.instance.songs.Values) {
            if (song.savedType == SONGTYPE.OPEN) {

                //Song theme
                //2022-01-28 Removed by hungtx: Do not need to save selected themeid
                //data[CONFIG_STRING.SelectedTheme + song.path] = ThemeConfig.GetSelectedTheme(song);

                //Song stars (Scores are saved in the dictionary)
                string bestStarsPath = Util.BuildString(String.Empty, CONFIG_STRING.BestStars, song.path);
                data[bestStarsPath] = PlayerPrefs.GetInt(bestStarsPath);

                //Songs don't have any score
                if (!scores.ContainsKey(Util.SongToBoardId(song.path))) {
                    data[song.path] = PlayerPrefs.GetString(song.path);
                }
            }
        }

        var achieveList = AchievementCenter.GetAchievementList();
        if (achieveList != null) {
            foreach (AchievementObj ojb in achieveList) {
                if (ojb.status == AchievementStatus.RECEIVED) {
                    string preStatus = Util.BuildString(string.Empty, AchievementObj.PRE_STATUS, ojb.type.ToString());
                    data[preStatus] = PlayerPrefs.GetInt(preStatus, 0);
                }
            }
        }

        return data.ToJson();
    }

    public void RestoreGameData() {
        try {
            if (gameData != null) {
                Debug.Log("RestoreGameData decodeData");
                var decodeData = Json.Deserialize(gameData);
                if (decodeData != null) {
                    Debug.Log("RestoreGameData Scores");
                    // Scores
                    PlayerPrefs.SetInt(CONFIG_STRING.OpenedSongTotal, 0);

                    if (scores != null) {
                        foreach (KeyValuePair<string, int> pair in scores) {
                            string key = Util.BoardToSongId(pair.Key);
                            PlayerPrefs.SetString(key, SONGTYPE.OPEN.ToString());

                            int score = Mathf.Max(Configuration.GetBestScore(key), pair.Value);
                            if (SongManager.instance.songs.ContainsKey(key)) {
                                SongManager.instance.songs[key].savedType = SONGTYPE.OPEN;
                                SongManager.instance.songs[key].BestScore = score;
                            }

                            Configuration.SetBestScore(score, key);
                        }
                    }

                    Debug.Log("RestoreGameData AchievementObj");
                    foreach (AchievementObj ojb in AchievementCenter.GetAchievementList()) {
                        ojb.ClearData();
                    }

                    Debug.Log("RestoreGameData gameDataNew");
                    Dictionary<string, System.Object> gameDataNew = (Dictionary<string, System.Object>) decodeData;
                    foreach (var pair in gameDataNew) {
                        switch (pair.Key) {
                            case CONFIG_STRING.Diamonds:
                                int diamonds = 0;
                                if (int.TryParse(pair.Value.ToString(), out diamonds)) {
                                    Configuration.instance.SetDiamonds(diamonds);
                                }

                                break;

                            case CONFIG_STRING.NoAds:
                                if (pair.Value.ToString() == "1") {
                                    Configuration.instance.AddNoAds();
                                }

                                break;

                            case CONFIG_STRING.Bonus_Login:
                            case CONFIG_STRING.Bonus_Sharing:
                            case CONFIG_STRING.SelectedTheme:
                            case CONFIG_STRING.SelectedBall:
                            case CONFIG_STRING.DailyGiftNumber:
                            case CONFIG_STRING.RateLater:
                            case CONFIG_STRING.Retry:
                            case CONFIG_STRING.Revive:
                            case CONFIG_STRING.IsTop1:
                            case CONFIG_STRING.PerfectCount:
                            case CONFIG_STRING.TutorialPerfect:
                            case CONFIG_STRING.NormalPerfect:
                                DataUpdate_Int(pair);
                                break;

                            default:
                                if (pair.Key.Contains(CONFIG_STRING.BestStars)) { // Compare star TH-812
                                    int _star = Mathf.Max(PlayerPrefs.GetInt(pair.Key),
                                        int.Parse(pair.Value.ToString()));
                                    PlayerPrefs.SetInt(pair.Key, _star);
                                } else if ( //pair.Key.Contains(CONFIG_STRING.BestStars) ||
                                           //pair.Key.Contains(CONFIG_STRING.BestScore) ||
                                           pair.Key.Contains(AchievementObj.PRE_STATUS) ||
                                           pair.Key.Contains(CONFIG_STRING.SelectedTheme)) {
                                    DataUpdate_Int(pair);
                                } else if (pair.Key.Contains(CONFIG_STRING.Balls)) {
                                    // Add ball active before login TH-806
                                    string ballActive = PlayerPrefs.GetString(CONFIG_STRING.Balls);
                                    string restoreBallActive = pair.Value.ToString();
                                    string num;
                                    for (int i = 0; i < ballActive.Length; i++) {
                                        if (ballActive[i] != ',') {
                                            num = ballActive[i].ToString();
                                            for (int k = i + 1; k < ballActive.Length; k++)
                                                if (ballActive[k] != ',')
                                                    num += ballActive[k];
                                                else {
                                                    if (!restoreBallActive.Contains(num))
                                                        restoreBallActive += FileHelper.Split + num + FileHelper.Split;
                                                    i = k;
                                                    break;
                                                }
                                        }
                                    }

                                    PlayerPrefs.SetString(CONFIG_STRING.Balls, restoreBallActive);
                                } else {
                                    PlayerPrefs.SetString(pair.Key, pair.Value.ToString());
                                    if (SongManager.instance.songs.ContainsKey(pair.Key)) {
                                        SongManager.instance.songs[pair.Key].savedType = SONGTYPE.OPEN;
                                    }
                                }

                                break;
                        }
                    }

                    //Sync data from user entry with locally saved playerprefs
                    Debug.Log("RestoreGameData Update favorite genres");
                    string updateFavoriteGenres = string.Join(FileHelper.Split, favoriteGenres.ToArray());
                    PlayerPrefs.SetString(FB_DB_KEY.favoriteGenres, updateFavoriteGenres);

                    Debug.Log("RestoreGameData Update interaction with favorite genres");
                    PlayerPrefs.SetInt(FB_DB_KEY.interactedWithGenres, interactedWithFavoriteGenres);

                    Debug.Log("RestoreGameData Update recent songs");
                    CoreData.GetPlayerData().OverrideRecentSong(recentlyPlayedSongs);
                }

                Debug.Log("RestoreGameData UpdateAchievement");
                Configuration.instance.UserDataCached.ReCalculateAll();
                AchievementCenter.UpdateAchievement();
                PlayerPrefs.SetInt(CONFIG_STRING.Bonus_Login, 1);
                Debug.Log("RestoreGameData End");
            }

            //local vip
            long localExpiredLong = 0;
            string oldExpired = PlayerPrefs.GetString(SubscriptionController.Key_ExpireDate);
            long.TryParse(oldExpired, out localExpiredLong);

        } catch (Exception ex) {
            Debug.Log("RestoreGameData Error: " + ex.ToString());
        }
    }

    private void DataUpdate_Int(KeyValuePair<string, System.Object> pair) {
        int value = 0;
        if (int.TryParse(pair.Value.ToString(), out value)) {
            PlayerPrefs.SetInt(pair.Key, value);

        }
    }

    public void UpdateFriendList(List<UserEntry> friends) {
        // if profileKey isn't valid -> can't update firebaseDatabase
        if (string.IsNullOrWhiteSpace(profileKey)) {
            return;
        }

        string strFriendList = string.Empty;
        foreach (UserEntry userEntry in friends) {
            if (!string.IsNullOrEmpty(userEntry.auId)) {
                strFriendList += userEntry.auId + ";";
            }
        }

        if (!strFriendList.Equals(friendList)) {
            Dictionary<string, System.Object> childUpdates = new Dictionary<string, System.Object>();
            childUpdates[profileKey + "/" + FB_DB_KEY.friendList] = strFriendList;
            UpdateAsyncFirebase(childUpdates); //update friend list
        }
    }

    public static void SaveUnlockSong(Song song = null) {
        List<string> unlockSongs = GetUnlockSongs();
        if (song != null && !unlockSongs.Contains(song.acm_id_v3)) {
            unlockSongs.Add(song.acm_id_v3);
        }

        string update = string.Join(FileHelper.Split, unlockSongs.ToArray());
        PlayerPrefs.SetString(PlayerPrefsKey.UnlockSongs, update);
    }

    public static List<string> GetUnlockSongs() {
        if (_unlockSongs is { Count: > 0 }) {
            return _unlockSongs;
        }

        _unlockSongs = Util.StringToList(PlayerPrefs.GetString(PlayerPrefsKey.UnlockSongs, ""));
        return _unlockSongs;
    }

    public static bool IsUnlockedSong(string acmID) {
        _unlockedSongHashSet ??= new HashSet<string>(GetUnlockSongs());
        return _unlockedSongHashSet.Contains(acmID);
    }
}