using System;
using System.Collections;
using UnityEngine;
using DG.Tweening;
using Sirenix.OdinInspector;
using TileHop.Cores.Pooling;

public class Diamond : MonoBehaviour {
    //static
    private static readonly int DiffuseColour = Shader.PropertyToID("_DiffuseColour");

    //public
    [SerializeField] private     Transform container;
    [SerializeField] private new Collider  collider;

    [SerializeField] private MeshRenderer   mesh;
    [SerializeField] private ParticleSystem effect;
    [SerializeField] private ParticleSystem psGemFxv2;

    //private
    private bool _isOnTile;
    private int  _idOnTile;

    [ShowInInspector] private int                _value;
    [ShowInInspector] private CurrencyEarnSource _source;

    public int IdOnTile => _idOnTile;
    private bool      _isMagnet;
    private Transform _transCache;
    private Transform _transModel;
    public Transform transCache => _transCache;

    private Follow _cacheFollow;

    private Follow follow {
        get {
            if (_cacheFollow == null) {
                _cacheFollow = SingletonManager.instance.GetSingleton<Follow>();
            }

            return _cacheFollow;
        }
    }

    private ParticleSystem _fxEarnDiamond;

    private Action<Transform> _onCompleteMagnet;
    private Tween             _tweenRecycle;

    #region Unity Method

    private void Awake() {
        _transCache = transform;
        _transModel = mesh.transform;

        _fxEarnDiamond = effect; //set default
    }

    private void OnEnable() {
        mesh.enabled = true;
        if (_isMagnet) {
            collider.enabled = true;
            _transModel.localPosition = Vector3.up;
        }

        if (RemoteConfig.instance.Economy_IsEnable) { //new fx
            _fxEarnDiamond = psGemFxv2;
            effect.gameObject.SetActive(false);
        } else {
            _fxEarnDiamond = effect;
            psGemFxv2.gameObject.SetActive(false);
        }

        _fxEarnDiamond.gameObject.SetActive(true);
    }

    private void OnDisable() {
        _tweenRecycle?.Kill();
        _onCompleteMagnet?.Invoke(transCache);
        _onCompleteMagnet = null;
    }

    private void OnTriggerEnter(Collider col) {
        if (mesh.enabled && col.CompareTag(GAMETAG.Ball)) {
            Eat();
        }
    }

    private void Eat() {
        collider.enabled = false;
        bool waitBonus = false;
        if (RemoteConfig.instance.NotesDifficult_IsEnable && RemoteConfig.instance.NotesDifficult_Diamond_InEnable) {
            //nothing
            waitBonus = false;
        } else {
            // BonusDiamond();
            waitBonus = true;
        }

        mesh.enabled = false;
        ShowEffectEatDiamond(() => {
            if (waitBonus) {
                BonusDiamond();
            }
        });

        transCache.SetParent(null);
        _tweenRecycle = DOVirtual.DelayedCall(0.6f, RecycleDiamondDelayed);
    }

    private void RecycleDiamondDelayed() {
        mesh.enabled = true;
        RecycleDiamond();
    }

    private void BonusDiamond() {
        int valueDiamond = _value;
        GameController.instance.AddDiamond(valueDiamond, _source);
        SoundManager.PlayEatSingleDiamond();
    }

    private void OnTriggerExit(Collider col) {
        if (mesh.enabled) {
            if (col.CompareTag(GAMETAG.Collector)) { //Is Musicalization
                RecycleDiamond();
            }
        }
    }

    public void RecycleDiamond() {
        _isOnTile = false;
        _idOnTile = -1;
        this.Recycle();
        collider.enabled = true;
        mesh.enabled = true;
    }

    #endregion

    public void Init(int value, CurrencyEarnSource source, int idOnTile) {
        _value = value;
        _source = source;
        _idOnTile = idOnTile;
        _isOnTile = _idOnTile >= 0;
        transform.localScale = new Vector3(1.3f, 1.3f, 1.3f);
        container.SetRotation(new Vector3(10, 0, 0));
    }

    private void ShowEffectEatDiamond(Action complete) {
        if (_fxEarnDiamond == this.psGemFxv2) {
            _fxEarnDiamond.Play();

            ProgressBarScript progressBarScript =
                TopBar.instance == null ? null : TopBar.instance.GetProgressBarScript();
            if (progressBarScript == null || !progressBarScript.gameObject.activeSelf ||
                !(progressBarScript is ProgressBarWithDiamondScript newProgressbar)) {
                complete?.Invoke();
                return;
            }

            Camera mainCamera = GameController.instance == null ? null : GameController.instance.mainCamera;
            if (mainCamera == null) {
                complete?.Invoke();
                return;
            }

            Vector3 fromScreenPoint = mainCamera.WorldToScreenPoint(_transCache.position);
            fromScreenPoint.x -= mainCamera.pixelWidth / 2;
            fromScreenPoint.y -= mainCamera.pixelHeight / 2;
            if (UIController.ui == null) {
                complete?.Invoke();
                return;
            }

            UIController.ui.AddDiamondsV2(fromScreenPoint + new Vector3(0, 40), newProgressbar.DiamondLocalPosition, 1,
                complete);
        } else {
            _fxEarnDiamond.Play();
            complete?.Invoke();
        }
    }

    public void Magnet(Transform target, float magnetStrong, Action<Transform> onCompleteMagnet) {
        StartCoroutine(IEMagnet(target, magnetStrong, onCompleteMagnet));
        SoundManager.PlayBoosterMagnetActive(0.5f);
    }

    private IEnumerator IEMagnet(Transform target, float magnetStrong, Action<Transform> onCompleteMagnet) {
        _onCompleteMagnet = onCompleteMagnet;
        _isMagnet = true;
        float distance = 0;
        float distanceMove;
        Vector3 deltaMove;
        while (collider.enabled) {
            distanceMove = magnetStrong * Time.deltaTime;
            distance = Vector3.Distance(target.position, _transModel.position);
            if (distance < distanceMove) {
                _transCache.position = target.position;
            } else {
                deltaMove = (target.position - _transModel.position).normalized * distanceMove;
                _transCache.position += deltaMove;
            }

            yield return YieldPool.GetWaitForEndOfFrame();
        }

        if (collider.enabled) {
            Eat();
        }

        _onCompleteMagnet?.Invoke(transCache);
        _onCompleteMagnet = null;
    }

    public void DropFromCloud(float height, float time) {
        mesh.enabled = true;
        collider.enabled = false;
        container.localPosition = Vector3.up * height;
        container.DOLocalMoveY(0, time).OnComplete(OnCompleteDrop);
    }

    private void OnCompleteDrop() {
        collider.enabled = true;
    }
}