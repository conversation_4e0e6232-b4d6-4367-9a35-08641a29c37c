using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;

public class BoatItem : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameSubscriber {
    [SerializeField] private SkeletonAnimation[] skeletonMain;
    [SerializeField] private SkeletonAnimation   skeletonEffect;
    [SerializeField] private Sprite<PERSON><PERSON>er      spriteRendererTrail;
    [SerializeField] private Sprite[]            imageBoatTrails;
    [SerializeField] private string[]            skinNames;
    [SerializeField] private string[]            animationNames;
    private                  float               _speed        = 0;
    private                  float               _speedTarget  = 0;
    private                  float               animTimeScale = 1f;
    private                  List<Spine.Slot>    slots         = new List<Spine.Slot>();
    private                  Color               color         = Color.white;

    #region Unity Method

    private void Awake() {
        StopAnim();
        if (NotesManager.instance.song.bmp > 120) {
            animTimeScale = animTimeScale * NotesManager.instance.song.bmp / 120f;
        }
        Spawner.onBgChange += OnBgChange;
        if (GameController.enableEndless) {
            Platform.OnHit += OnHit;
        }
    }

    private void Start() {
        GameController.instance.addSubcriber(this);

        foreach (var sm in skeletonMain) {
            foreach (Spine.Slot slot in sm.skeleton.Slots) {
                slots.Add(slot);
            }
        }
    }

    private void OnDestroy() {
        Spawner.onBgChange -= OnBgChange;
        if (GameController.enableEndless) {
            Platform.OnHit -= OnHit;
        }
    }

    #endregion

    // Update is called once per frame
    private void OnBgChange(byte current, byte next, float duration) {
        StopAllCoroutines();
        Spawner.s.StartCoroutine(Transition(current, next, duration));
    }

    private IEnumerator Transition(byte current, byte next, float duration) {
        if (next < 0 || next > animationNames.Length - 1) {
            Debug.LogError("[Transition] index out of animationNames");
            yield break;
        }

        float t = 0;
        float a = 1;

        if (animationNames.Length > 0) {
            skeletonEffect.AnimationName = animationNames[next];
        }

        foreach (Spine.Slot slot in slots) {
            slot.A = a;
        }

        while (t < duration) {
            t += Time.deltaTime;
            a = Mathf.Lerp(1f, 0.1f, t / duration);
            foreach (Spine.Slot slot in slots) {
                slot.A = a;
            }

            color.a = a;
            spriteRendererTrail.color = color;
            yield return null;
        }

        t = 0;
        foreach (var ske in skeletonMain) {
            ske.Skeleton.SetSkin(skinNames[next]);
        }

        spriteRendererTrail.sprite = imageBoatTrails[next];

        while (t < duration) {
            t += Time.deltaTime;
            a = Mathf.Lerp(0.1f, 1f, t / duration);
            foreach (Spine.Slot slot in slots) {
                slot.A = a;
            }

            color.a = a;
            spriteRendererTrail.color = color;
            yield return null;
        }
    }

    private void OnHit(Platform bl, int perfectCount) {
        UpdateSpeed();
    }

    public void gameStart() {
        StartAnim();
    }

    public void gameContinue() {
    }

    public void gameOver() {
        StopAnim();
    }

    private void StartAnim() {
        foreach (var ske in skeletonMain) {
            ske.timeScale = animTimeScale;
        }
    }

    private void UpdateSpeed() {
        _speed = SuperpoweredSDK.instance.GetSpeed() * animTimeScale;
        foreach (var ske in skeletonMain) {
            ske.timeScale = _speed;
        }
    }

    private void StopAnim() {
        foreach (var ske in skeletonMain) {
            ske.timeScale = 0.1f;
        }
    }
}