using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using NoteData = GamePlay.Levels.NoteData;

public class HipHopModernController : MonoBehaviour {
    [SerializeField] private List<ParticleSystem> stadiumFX;
    [SerializeField] private List<ParticleSystem> strongNoteFX;
    [SerializeField] private List<ParticleSystem> ringBeatFX;
    [SerializeField] private ParticleSystem       moodChange;
    [SerializeField] private ParticleSystem       Flash;
    [SerializeField] private Animator             lightAnim;

    [SerializeField] private Animator[] hiphopAnimator;
    private string[] AnimState = new string[4] { "Green1", "Blue1", "Orange1", "Purple1" };
    private string[] AnimLight = new string[4] { "BeatNote_01", "BeatNote_02", "BeatNote_03", "BeatNote_04" };

    //Color
    [SerializeField] private List<Color> hightLightColors;
    [SerializeField] private List<Color> baseColors;

    [SerializeField] private Material characterMaterail;

    //Handmade
    public static bool RunFollowTimeline = false;

    [Serializable]
    public struct ContentInfo {
        public PlayableDirector    songTimeline;
        public List<int>           moodChangesIndex;
        public List<NoteData.Mood> styleMoodChanges;
    }

    [SerializeField] private List<ContentInfo> contentsInfo;
    private                  int               songIndex;

    private float _songBpm;
    private float _animSpeed;

    private void Awake() {
        if (DeviceHelper.IsHigh())
            CheckSong();
        else { //Low end
            ringBeatFX[0].transform.parent.parent.gameObject.SetActive(false);
            Flash.gameObject.SetActive(false);
            hiphopAnimator[1].gameObject.SetActive(false);
            hiphopAnimator[2].gameObject.SetActive(false);
        }

        _songBpm = NotesManager.instance.song.bmp;
        _animSpeed = _songBpm / 120f;
    }

    public void HitNote() {
        if (!DeviceHelper.IsHigh())
            return;

        int skinIndex = Spawner.s.currentIndexSkin;

        stadiumFX[skinIndex].Play();
        ringBeatFX[skinIndex].Play();

        //Restart fx Flash
        Flash.Stop();
        Flash.Play();

        if (!RunFollowTimeline) {
            for (int i = 0; i < hiphopAnimator.Length; i++) {
                hiphopAnimator[i].speed = Ball.b.timeScale * _animSpeed;
            }

            lightAnim.speed = Ball.b.timeScale * _animSpeed;
        }
    }

    public void PlayStrongEffect() {
        if (!DeviceHelper.IsHigh()) {
            return;
        }

        int skinIndex = Spawner.s.currentIndexSkin;

        strongNoteFX[skinIndex].Play();
    }

    public IEnumerator PlayMoodChange() {
        StartCoroutine(ChangeColor());
        if (Spawner.s.currentJumpNoteID == 0)
            lightAnim.enabled = false;

        yield return null;

        while (GameController.instance.game != GameStatus.LIVE) {
            yield return null;
        }

        if (!RunFollowTimeline)
            for (int i = 0; i < hiphopAnimator.Length; i++) {
                hiphopAnimator[i].SetTrigger(AnimState[Spawner.s.currentIndexSkin]);
            }

        if (DeviceHelper.IsHigh()) {
            if (Spawner.s.currentJumpNoteID != 0) {
                moodChange.Play();
            }

            lightAnim.enabled = true;
            lightAnim.Play(AnimLight[Spawner.s.currentIndexSkin]);
        }
    }

    public IEnumerator ContinueWithTimeline() {
        while (GameController.instance.game != GameStatus.LIVE) {
            yield return null;
        }

        if (RunFollowTimeline) {
            contentsInfo[songIndex].songTimeline.gameObject.SetActive(true);
            contentsInfo[songIndex].songTimeline.playableGraph.GetRootPlayable(0)
                .SetTime(NotesManager.instance.noteDatas[Spawner.s.currentJumpNoteID].timeAppear);
            contentsInfo[songIndex].songTimeline.Play();
        }
    }

    public void StopAnimation() {
        if (RunFollowTimeline)
            contentsInfo[songIndex].songTimeline.gameObject.SetActive(false);
    }

    public void ChangeContentFollowTimeline() {
        if (!RunFollowTimeline || contentsInfo[songIndex].moodChangesIndex.Count == 0)
            return;

        int count = 0;
        int currentNote = 0;
        while (count < contentsInfo[songIndex].moodChangesIndex.Count) {
            int index = contentsInfo[songIndex].moodChangesIndex[count];

            for (int i = currentNote; i < index; i++) {
                if (i < NotesManager.instance.noteCount)
                    NotesManager.instance.noteDatas[i].mood = contentsInfo[songIndex].styleMoodChanges[count];
            }

            currentNote = index;
            count++;
        }

        for (int i = currentNote + 1; i < NotesManager.instance.noteCount; i++)
            NotesManager.instance.noteDatas[i].mood = contentsInfo[songIndex].styleMoodChanges[count];
    }

    public void EnableTimeLine(bool isNextRound = false) {
        if (RunFollowTimeline) {
            contentsInfo[songIndex].songTimeline.gameObject.SetActive(true);
            contentsInfo[songIndex].songTimeline.Play();
            if (isNextRound)
                contentsInfo[songIndex].songTimeline.playableGraph.GetRootPlayable(0).SetTime(0);
        }
    }

    public void CheckSong() {
        if (string.IsNullOrEmpty(RemoteConfig.instance.Hiphop_Songspath))
            return;

        string[] songs = RemoteConfig.instance.Hiphop_Songspath.Split(',', ';');
        ;

        songIndex = -1;
        int count = 0;
        foreach (var songName in songs) {
            if (NotesManager.instance.song.path == songName && count < contentsInfo.Count) {
                songIndex = count;
                break;
            }

            count++;
        }

        RunFollowTimeline = songIndex >= 0;
    }

    public IEnumerator ChangeColor() {
        Color current_hightlightColor = characterMaterail.GetColor("_ColorHighlight");
        Color current_baseColor = characterMaterail.GetColor("_ColorBase");
        int skinIndex = Spawner.s.currentIndexSkin;

        float timer = 0;

        while (timer < 0.5f) {
            timer += Time.deltaTime;
            characterMaterail.SetColor("_ColorHighlight",
                Color.Lerp(current_hightlightColor, hightLightColors[skinIndex], timer / 0 / 5f));
            characterMaterail.SetColor("_ColorBase",
                Color.Lerp(current_baseColor, baseColors[skinIndex], timer / 0.5f));

            yield return null;
        }
    }
}