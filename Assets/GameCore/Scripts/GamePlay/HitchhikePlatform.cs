using DG.Tweening;
using UnityEngine;
using UnityEngine.Analytics;

public class HitchhikePlatform : Platform {
    [Header("Hitch hike platform")] [SerializeField]
    private Transform Wall;

    [SerializeField] private Transform wireRoad;
    [SerializeField] private Transform Steep;

    private Material _wireRoadMaterial;
    private Material _wallMaterial;
    private Color    _changedSaturationColor;

    private float   _orbIntervalTime;
    private int     _orbVariant;
    private int     _totalOrbs;
    private Vector3 _startPoint;
    private Vector3 _endPoint;

    protected override void Awake() {
        base.Awake();
        _wallMaterial = Steep.GetComponent<MeshRenderer>().material;
        _wireRoadMaterial = wireRoad.GetComponent<MeshRenderer>().material;
    }

    protected override void OnDisable() {
        if (hitted) {
            RecycleOrbs();
        }

        base.OnDisable();
    }

    public void ShowLine(Vector3 positionPlatform, float targetZ) {
        Wall.localPosition = new Vector3(-positionPlatform.x, Wall.localPosition.y, targetZ);

        wireRoad.localPosition = ((targetZ + 2) * 0.5f) * Vector3.back;
        var localScale = wireRoad.localScale;
        localScale = new Vector3(localScale.x, localScale.y, targetZ / 10f);
        wireRoad.localScale = localScale;
    }

    public void CreateLineOfOrbs(float _orbIntervalTime, int _orbVariant, Vector3 nextPosition) {
        this._orbIntervalTime = _orbIntervalTime;
        this._orbVariant = _orbVariant;
        this._endPoint = nextPosition;
        CreateOrbs();
    }

    protected override void CreateOrbs() {
        listOrbPositions.Clear();
        _totalOrbs = (int)(timeSlide / _orbIntervalTime);
        if (_totalOrbs == 0) return;

        _startPoint = platformData.positionPlatform;

        if (_orbVariant == 0) {
            _orbVariant = UnityEngine.Random.Range(0, 999) % 4 + 1;
        }

        switch (_orbVariant) {
            case 1: // static straight line
                CreateStraightLineOrbs();
                break;
            case 2: // moving straight line
                CreateStraightLineOrbs();
                break;
            case 3: // static interleaved line
                CreateInterleavedLineOrbs();
                break;
            case 4: //move opposite in straight Line
                CreateStraightLineOrbs();
                break;
            default:
                CreateStraightLineOrbs();
                break;
        }

        base.CreateOrbs();

        float timePlay = GameController.instance.timePlay;
        float totalTimeMove;
        float timeAppear = platformData.currNoteObj.timeAppear;
        //make move
        switch (_orbVariant) {
            case 1: // static straight line
                break;
            case 2: // moving straight line
                bool isMoveToRight = _startPoint.x < 0;
                foreach (var orb in listOrbs) {
                    timeAppear += _orbIntervalTime;
                    totalTimeMove = (timeAppear - timePlay) / Ball.b.timeScale;
                    orb.SetMove(true, isMoveToRight, totalTimeMove);
                }

                break;
            case 3: // static interleaved line
                break;
            case 4: //move opposite in straight Line
                bool isRight = _startPoint.x < 0;
                foreach (var orb in listOrbs) {
                    timeAppear += _orbIntervalTime;
                    totalTimeMove = (timeAppear - timePlay) / Ball.b.timeScale;
                    orb.SetMove(true, isRight, totalTimeMove);
                    isRight = !isRight;
                }

                break;
        }
    }

    private void CreateStraightLineOrbs() {
        if (_totalOrbs == 0) return;
        for (int i = 1; i < _totalOrbs + 1; i++) {
            Vector3 pos = Vector3.Lerp(_startPoint, _endPoint, ((float)i / (_totalOrbs + 2)));
            listOrbPositions.Add(pos);
        }
    }


    private void CreateInterleavedLineOrbs() {
        if (_totalOrbs == 0) return;

        const float offsetX = 1.2f;
        for (int i = 1; i < _totalOrbs + 1; i++) {
            Vector3 pos = Vector3.Lerp(_startPoint, _endPoint, ((float)i / (_totalOrbs + 2)));
            pos.x += (i % 2 == 0 ? offsetX : -offsetX);
            listOrbPositions.Add(pos);
        }
    }

    protected override void SinkEffect() {
        // base.SinkEffect();
    }

    public override void OnHitchhike(bool isActive) {
        base.OnHitchhike(isActive);
        if (isActive) {
            Wall.transform.SetParent(transform.parent);
        } else {
            Wall.transform.SetParent(this.transform);
        }
    }

    protected override void SetPlinthColor(Color c) {
        base.SetPlinthColor(c);
        _changedSaturationColor = c.ChangeSaturation(0.8f);

        _wireRoadMaterial.SetColor("_ColorMark", c);
        _wireRoadMaterial.SetColor("_ColorTile1", _changedSaturationColor);
        _wireRoadMaterial.SetColor("_RimpColor", _changedSaturationColor);
        //_wallMaterial.SetColor("_ColorMark", c);
        _wallMaterial.SetColor("_ColorTile1", c);
        _wallMaterial.SetColor("_RimpColor", c);
    }

    public override bool IsPerfectLanding(Vector3 ballPosition, float perfectSize) {
        return true;
    }

    protected override Vector3 GetDiamondPosition(float positionZ) {
        float range = RemoteConfigBase.instance.GetTileMaxPositionX();
        return new Vector3(Random.Range(-range / 2, range / 2), 0.5f, positionZ - transCache.position.z);
    }
}