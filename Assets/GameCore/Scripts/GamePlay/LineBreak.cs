using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class LineBreak : MonoBehaviour {
    public TextMesh text;
    Vector3 originalPosition = Vector3.zero;
    bool isHiding = false;
    private Transform cachedTransform;

    void Awake() {

        originalPosition = transform.localPosition;
        GetComponent<Renderer>().sortingLayerName = "Front";
        cachedTransform = this.transform;
    }

    public void SetText(string str) {
        if (originalPosition != Vector3.zero) {
            transform.localPosition = originalPosition;
        }

        text.text = str;
    }

    private void OnEnable() {
        isHiding = false;
    }

    public void StartHide() {
        if (isHiding == false) {
            StartCoroutine(Hide());
            //SoundManager.instance.PlayCheckPoint();
            isHiding = true;
        }
    }

    private IEnumerator Hide(float time = 0.5f) {
        float t = 0;
        while (t < time) {
            t += Time.deltaTime;
            cachedTransform.position += Vector3.back * Time.deltaTime * 20;
            yield return null;
        }
        gameObject.SetActive(false);
        cachedTransform.localPosition = originalPosition;
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }
}