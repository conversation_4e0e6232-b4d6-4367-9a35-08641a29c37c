using System;
using System.Collections.Generic;
using System.Text;
using GamePlay.Levels;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TileHop.GamePlay.NewElement {
    [Serializable]
    public class NewTilesByDifficulty {
        #region data percentType

        private readonly byte[,] _percentSpecialPart = new byte[,] {
            {90, 0, 10},
            {70, 20, 10},
            {70, 20, 10},
            {70, 20, 10},
        };

        private readonly IngameSectionType[] _listSpecialSection = new[]
            {IngameSectionType.HyperBoost, IngameSectionType.Mirror, IngameSectionType.UpsideDown};

        private readonly byte[,] _percentSpecialSection = new byte[,] {
            {100, 0, 0},
            {0, 0, 0},
            {0, 100, 0},
            {0, 0, 100}
        };

        private readonly NoteElementType[] _listLongTile = new[]
            {NoteElementType.LongTile, NoteElementType.LongTileBreak};

        private readonly byte[,] _percentLongTile = new byte[,] {
            {100, 0},
            {75, 25},
            {75, 25},
            {75, 25}
        };

        private readonly NoteElementType[] _listFakeTile = new[] {
            NoteElementType.FakeTile, NoteElementType.FakeTransform, NoteElementType.FakeConveyor,
            NoteElementType.FakeThunder
        };

        private readonly byte[,] _percentFakeTile = new byte[,] {
            {100, 0, 0, 0},
            {80, 0, 20, 0},
            {80, 0, 0, 20},
            {80, 20, 0, 0}
        };

        private readonly NoteElementType[] _listNormalTile = new[] {
            NoteElementType.None, NoteElementType.MovingTile, NoteElementType.FadeOut,
            NoteElementType.FadeInOut, NoteElementType.MovingCircle, NoteElementType.Teleport
        };

        private readonly byte[,] _percentNormalTile = new byte[,] {
            {100, 0, 0, 0, 0, 0},
            {80, 20, 0, 0, 0, 0},
            {70, 10, 10, 10, 0, 0},
            {60, 10, 0, 0, 20, 10}
        };

        #endregion

        [ShowInInspector, ReadOnly] private List<int>             _idLongNotes   = new List<int>();
        [ShowInInspector, ReadOnly] private List<int>             _idFakeNotes   = new List<int>();
        [ShowInInspector, ReadOnly] private List<int>             _idNormalNotes = new List<int>();
        [ShowInInspector, ReadOnly] private List<NoteElementType> _elementInSong = new List<NoteElementType>();

        private int   _countFakeTotal;
        private int[] _countFakeTiles;

        private int   _countNormalTotal;
        private int[] _countNormalTiles;

        private int _maxElement        = -1;
        private int _lastIdElementNote = 0;

        private byte _row = 0;
        public bool reachMaxElement => _maxElement > 0 && _elementInSong.Count >= _maxElement;

        private const NoteElementType DefaultLongNote   = NoteElementType.LongTile;
        private const NoteElementType DefaultNormalNote = NoteElementType.None;
        private const NoteElementType DefaultFakeNote   = NoteElementType.FakeTile;

        public void ResetData(DifficultyTag difficulty, int maxElement) {
            _row = GetRowByDifficulty(difficulty);
            _maxElement = maxElement;
            
            _elementInSong.Clear();
            _idLongNotes.Clear();
            _idFakeNotes.Clear();
            _idNormalNotes.Clear();
        }

        private byte GetRowByDifficulty(DifficultyTag difficulty) {
            switch (difficulty) {
                case DifficultyTag.EASY:
                    return 0;

                case DifficultyTag.NONE:
                case DifficultyTag.MEDIUM:
                    return 1;

                case DifficultyTag.HARD:
                    return 2;

                case DifficultyTag.EXTREME:
                case DifficultyTag.INSANE:
                    return 3;

                default:
                    Logger.EditorLogError("Not handle this type!!!! -> using MEDIUM");
                    return 1;
            }
        }

        public (IngameSectionType type, int amount) GetSpecialSection(int countTotal) {
            int rand1 = Random.Range(0, 9999) % 100;
            for (byte i = 0; i < _listSpecialSection.Length; i++) {
                rand1 -= _percentSpecialSection[_row, i];
                if (rand1 <= 0) {
                    return (_listSpecialSection[i],
                        Mathf.RoundToInt(_percentSpecialPart[_row, 2] * countTotal / 100f) - 2);
                }
            }

            return (IngameSectionType.Normal, 0);
        }

        private NoteElementType GetLongNoteType() {
            if (reachMaxElement) {
                if (_idLongNotes.Count != 0) { //exist long note
                    byte rand = (byte) (Random.Range(0, 9999) % 100);
                    for (byte i = 0; i < _idLongNotes.Count; i++) {

                        rand -= _percentLongTile[_row, _idLongNotes[i]];
                        if (rand <= 0) {
                            return _listLongTile[_idLongNotes[i]];
                        }
                    }
                }

                return DefaultLongNote;
            }

            byte rand1 = (byte) (Random.Range(0, 9999) % 100);
            for (byte i = 0; i < _listLongTile.Length; i++) {
                rand1 -= _percentLongTile[_row, i];
                if (rand1 <= 0) {
                    if (i != 0 && !_elementInSong.Contains(_listLongTile[i])) { //not default
                        _idLongNotes.Add(i);
                        _elementInSong.Add(_listLongTile[i]);
                    }

                    return _listLongTile[i];
                }
            }

            return DefaultLongNote;
        }

        private NoteElementType GetNormalType(bool canTurningCircle) {
            if (_countNormalTotal <= 0) {
                return NoteElementType.None;
            }

            if (reachMaxElement) {
                if (_idNormalNotes.Count != 0) { //exist Normal note
                    int rand = Random.Range(0, 9999) % _countNormalTotal;
                    for (byte i = 0; i < _idNormalNotes.Count; i++) {
                        int index = _idNormalNotes[i];
                        rand -= _countNormalTiles[index];
                        if (rand <= 0) {
                            if (!canTurningCircle && _listNormalTile[index] == NoteElementType.MovingCircle) {
                                return NoteElementType.None;
                            }

                            _countNormalTiles[index] -= 1;
                            _countNormalTotal -= 1;
                            return _listNormalTile[index];
                        }
                    }
                }

                return DefaultNormalNote;
            }

            int rand1 = Random.Range(0, 9999) % _countNormalTotal + 1;
            for (byte i = 0; i < _listNormalTile.Length; i++) {
                rand1 -= _countNormalTiles[i];
                if (rand1 <= 0) {
                    if (!canTurningCircle && _listNormalTile[i] == NoteElementType.MovingCircle) {
                        return NoteElementType.None;
                    }

                    _countNormalTiles[i] -= 1;
                    _countNormalTotal -= 1;
                    if (i != 0 && !_elementInSong.Contains(_listNormalTile[i])) {
                        _idNormalNotes.Add(i);
                        _elementInSong.Add(_listNormalTile[i]);
                    }

                    return _listNormalTile[i];
                }
            }

            return DefaultNormalNote;
        }

        private NoteElementType GetFakeType() {
            if (_countFakeTotal <= 0) {
                return DefaultFakeNote;
            }

            if (reachMaxElement) {
                if (_idFakeNotes.Count != 0) { //exist Fake note
                    int rand = Random.Range(0, 9999) % _countFakeTotal;
                    for (byte i = 0; i < _idFakeNotes.Count; i++) {
                        int index = _idFakeNotes[i];
                        rand -= _countFakeTiles[index];
                        if (rand <= 0) {
                            _countFakeTiles[index] -= 1;
                            _countFakeTotal -= 1;
                            return _listFakeTile[index];
                        }
                    }
                }

                return DefaultFakeNote;
            }

            int rand1 = Random.Range(0, 9999) % _countFakeTotal + 1;
            for (byte i = 0; i < _listFakeTile.Length; i++) {
                rand1 -= _countFakeTiles[i];
                if (rand1 <= 0) {
                    _countFakeTiles[i] -= 1;
                    _countFakeTotal -= 1;
                    if (!_elementInSong.Contains(_listFakeTile[i])) {
                        _idFakeNotes.Add(i);
                        _elementInSong.Add(_listFakeTile[i]);
                    }

                    return _listFakeTile[i];
                }
            }

            return DefaultFakeNote;
        }

        private bool CanTurnFakeTile(float progress, bool fake) {
            if (_countFakeTotal <= 0)
                return false;

            return Random.Range(0, 9999) % (_countFakeTotal + _countNormalTotal) < _countFakeTotal;
        }

        private void CalculateNumberTiles(int total, int row) {
            _countFakeTiles ??= new int[_listFakeTile.Length];
            _countFakeTotal = 0;
            for (int i = 0; i < _listFakeTile.Length; i++) {
                _countFakeTiles[i] =
                    Mathf.RoundToInt(total * _percentSpecialPart[row, 1] / 100f * _percentFakeTile[row, i] / 100f);
                _countFakeTotal += _countFakeTiles[i];
            }

            _countNormalTiles ??= new int[_listNormalTile.Length];
            _countNormalTotal = 0;
            for (int i = 0; i < _listNormalTile.Length; i++) {
                if (_listNormalTile[i] == NoteElementType.None) {
                    _countNormalTiles[i] = 0;
                    continue;
                }

                _countNormalTiles[i] =
                    Mathf.RoundToInt(total * _percentSpecialPart[row, 0] / 100f * _percentNormalTile[row, i] / 100f);
                _countNormalTotal += _countNormalTiles[i];
            }
        }

#if UNITY_EDITOR
        private Dictionary<NoteElementType, List<int>> _dictElements = new Dictionary<NoteElementType, List<int>>();
#endif
        public void Process(ref List<NoteData> noteList, int specialStart, int specialEnd,
                            IngameSectionType sectionType, ref HashSet<NoteElementType> elementList, float longNoteDuration) {
            int total = noteList.Count;
            CalculateNumberTiles(total, _row);
            int totalFake = 0;
#if UNITY_EDITOR
            _dictElements.Clear();
#endif
            _lastIdElementNote = 0;
            bool _turnFake;
            for (int i = 6; i < total - 2; i++) {
                if (noteList[i - 1].elementType == NoteElementType.MovingCircle)
                    continue;

                if (i >= specialStart - 1 && i <= specialEnd) {
                    i += (specialEnd - specialStart) + (sectionType == IngameSectionType.UpsideDown ? 5 : 2);
                    continue;
                }

                if (noteList[i].elementType != NoteElementType.None) {
                    _lastIdElementNote = i;
                    continue;
                }

                if (noteList[i].fakeTile != NoteData.FakeTile.NONE) {
                    //fake tile -> hold!!!
                    if (!noteList[i].elementType.IsFakeElement()) {
                        noteList[i].elementType = NoteElementType.FakeTile;
                    }

                    elementList.Add(noteList[i].elementType);
                    totalFake++;
                    _countFakeTotal -= 1;
                    _countFakeTiles[0] -= 1;
#if UNITY_EDITOR
                    TryAdd(i, noteList[i].elementType);
#endif
                    continue;
                }

                if (noteList[i].mood != noteList[i + 1].mood) { //current note is mood change
                    continue;
                }

                if (noteList[i].duration >= NotesManager.LongDurationValue) {
                    noteList[i].elementType = GetLongNoteType();
                    elementList.Add(noteList[i].elementType);
#if UNITY_EDITOR
                    TryAdd(i, noteList[i].elementType);
#endif
                    continue;
                }

                if (noteList[i].distance <= NotesManager.ShortDistanceValue) {
                    continue;
                }

                if (i - _lastIdElementNote < 5) {
                    continue;
                }

                _turnFake = CanTurnFakeTile(i / (float) total, false);
                if (_turnFake) {
                    noteList[i].elementType = GetFakeType();
                    _lastIdElementNote = i;
                    elementList.Add(noteList[i].elementType);
                    totalFake++;
#if UNITY_EDITOR
                    TryAdd(i, noteList[i].elementType);
#endif
                } else {
                    bool canTurnMovingCircle = true;
                    if (noteList[i + 1].distance <= NotesManager.ShortDistanceValue)
                        canTurnMovingCircle = false;
                    else if (noteList[i + 1].duration >= longNoteDuration) {
                        canTurnMovingCircle = false;
                    } else if (noteList[i + 1].mood != noteList[i + 2].mood) { // next note is mood change
                        canTurnMovingCircle = false;
                    }

                    var elementType = GetNormalType(canTurnMovingCircle);

                    noteList[i].elementType = elementType;
                    if (noteList[i].elementType != NoteElementType.None) {
                        _lastIdElementNote = i;
                        elementList.Add(noteList[i].elementType);
#if UNITY_EDITOR
                        TryAdd(i, noteList[i].elementType);
#endif
                    }
                }
            }

            int targetFake = Mathf.RoundToInt(total * _percentSpecialPart[_row, 1] / 100f);

            if (totalFake > targetFake) {
                //remove bớt
            } else if (totalFake < targetFake) {
                // add thêm
                _lastIdElementNote = 0;
                for (int i = 6; i < total - 2; i++) {
                    if (i - _lastIdElementNote < 5)
                        continue;

                    if (i >= specialStart && i <= specialEnd) {
                        i += (specialEnd - specialStart) + 1;
                        continue;
                    }

                    if (noteList[i - 1].elementType == NoteElementType.MovingCircle)
                        continue;

                    if (noteList[i].mood != noteList[i + 1].mood) { //current note is mood change
                        continue;
                    }

                    if (noteList[i].elementType != NoteElementType.None) {
                        _lastIdElementNote = i;
                        continue;
                    }

                    if (CanTurnFakeTile(i / (float) total, true)) {
                        noteList[i].elementType = GetFakeType();
                        _lastIdElementNote = i;
#if UNITY_EDITOR
                        TryAdd(i, noteList[i].elementType);
#endif
                        elementList.Add(noteList[i].elementType);
                        totalFake++;
                        if (totalFake >= targetFake)
                            break;
                    }
                }
            }

#if UNITY_EDITOR
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Remain tile Normal: {ViewString(_countNormalTiles)}");
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Remain tile Fake: {ViewString(_countFakeTiles)}");
            ShowDict();

            void TryAdd(int i, NoteElementType type) {
                if (_dictElements.ContainsKey(type)) {
                    _dictElements[type].Add(i);
                } else {
                    _dictElements.Add(type, new List<int>() {i});
                }
            }
            void ShowDict() {
                StringBuilder builder = new StringBuilder();
                builder.Append("All Element Note:\n");
                foreach (var element in _dictElements) {
                    builder.Append($"{element.Key.ToString()} : ");
                    foreach (var index in element.Value) {
                        builder.Append($"{index} ;");
                    }

                    builder.AppendLine();
                }

                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"{builder.ToString()}");
            }
            string ViewString(int[] data) {
                return string.Join(" ; ", data);
            }
#endif
        }
    }
}