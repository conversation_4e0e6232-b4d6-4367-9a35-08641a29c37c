using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using Random = UnityEngine.Random;

public class TeleportMovingTile : MonoBehaviour {
    private float    _desiredPositionOnStop;
    private int      _desiredValue;
    private int      _currentLine;
    private int      _noteIdStartTeleport;
    private Platform _platform;
    private bool     _initialized;

    public  float     xPos;
    public  float     nextPos;
    private List<int> _listPos = new List<int>();

    private readonly string path          = "VFX/VFX_TeleportTile_Plinth";
    private readonly string pathDust      = "VFX/VFX_TeleportTile_Dust";
    private readonly string pathChristmas = "VFX/VFX_TeleportTile_Christmas";
    private readonly string pathPointer   = "VFX/VFX_TeleportTile_Next";

    private ParticleSystem _vfxPointer;

    private void Start() {
        Ball.OnActionJump += CalculatePositionX;
    }

    private void OnDestroy() {
        Ball.OnActionJump -= CalculatePositionX;
    }

    private void OnDisable() {
        StopNextPointer();
    }

    public void Init(Platform platform) {
        _desiredPositionOnStop = platform.transCache.position.x;
        _noteIdStartTeleport = platform.noteID - Mathf.Min(platform.noteID - Spawner.s.currentJumpNoteID,
            RemoteConfigBase.instance.NewElements_Teleport_Count);
        _platform = platform;

        CalculatePositions();
        _initialized = true;

        CalculatePositionX();
    }

    private void CalculatePositions() {
        _listPos.Clear();
        for (int i = 0; i < Spawner.s.LineMap.Length; i++) {
            if (Mathf.Approximately(_desiredPositionOnStop, Spawner.s.LineMap[i])) {
                _desiredValue = i;
                break;
            }
        }

        int lastLine = _desiredValue;
        _listPos.Add(_desiredValue);
        int total = _platform.noteID - Spawner.s.currentJumpNoteID;
        for (int i = 0; i < total; i++) {
            switch (lastLine) {
                case 0: // 234
                    lastLine = Random.Range(0, 9999) % 3 + 2;
                    break;

                case 1: //34
                    lastLine = Random.Range(0, 9999) % 2 + 3;
                    break;

                case 2: // 04
                    lastLine = Random.Range(0, 9999) % 2 == 0 ? 0 : 4;
                    break;

                case 3: //01
                    lastLine = Random.Range(0, 9999) % 2;
                    break;

                case 4: //012
                    lastLine = Random.Range(0, 9999) % 3;
                    break;
            }

            _listPos.Add(lastLine);
        }
    }

    private void CalculatePositionX() {
        if (!GameController.IsPlaying || GameController.instance.game != GameStatus.LIVE ||
            Spawner.s.currentJumpNoteID < _noteIdStartTeleport || Spawner.s.currentJumpNoteID >= _platform.noteID - 1 ||
            !_initialized) {
            if (Spawner.s.currentJumpNoteID == _platform.noteID - 1) {
                ShowVFX();
            }

            _currentLine = _listPos[0];
            xPos = _desiredPositionOnStop;
            StopNextPointer();
            return;
        }

        int offset = _platform.noteID - Spawner.s.currentJumpNoteID - 1;
        _currentLine = _listPos[offset];
        xPos = Spawner.s.LineMap[_currentLine];
        ShowNextPointer(offset - 1);
        ShowVFX();
    }

    private void ShowNextPointer(int offset) {
        // if (_vfxPointer == null) {
        //     _vfxPointer = Instantiate(Resources.Load<ParticleSystem>(pathPointer), this.transform);
        // }
        //
        // float posNext = Spawner.s.LineMap[_listPos[offset]];
        // _vfxPointer.transform.localPosition = Vector3.right * (posNext - xPos);
        // _vfxPointer.gameObject.SetActive(true);
        // _vfxPointer.Play();
    }

    private void StopNextPointer() {
        // if (_vfxPointer) {
        //     _vfxPointer.gameObject.SetActive(false);
        // }
    }

    private void ShowVFX() {
        var pathVfx = ThemeManager.GetPlayingThemeId() == ThemeManager.ThemeXmas2021 ? pathChristmas : path;
        var vfx = ObjectPool.Spawn(Resources.Load<TeleportTileVFX>(pathVfx));
        var dust = ObjectPool.Spawn(Resources.Load<TeleportTileVFX>(pathDust));
        var ratio = _platform.sq.transform.localScale.z /
                    3.225f; //3.225f is the default z-scale of the tile in prefab mode
        dust.transform.position = _platform.GetContainer().position;
        vfx.transform.parent = _platform.GetContainer();
        vfx.transform.localPosition = Vector3.zero;
        vfx.Init(ratio, _platform.GetContainer(), _platform.transform,
            _platform.IsUsePlinth && RemoteConfigBase.instance.NewElements_Teleport_UsePlinth);
        dust.Init(ratio, _platform.GetContainer(), _platform.transform);
        DOVirtual.DelayedCall(0.5f, () => {
            ObjectPool.Recycle(vfx);
            ObjectPool.Recycle(dust);
        });
    }
}