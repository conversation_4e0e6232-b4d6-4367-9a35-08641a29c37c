using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;
using System.Text;
using DG.Tweening;
using Inwave;
using Newtonsoft.Json;

public enum AdDelayOption {
    BeforeFirstFS,
    BetweenFS,
    BetweenRVAndFS
}

public enum AdErrorType {
    FsShow,
    FsCaching,
    RwShow,
    RwCaching,
    BnCaching
}

public class AdsManager : Singleton<AdsManager>, IEscapeHandler {
    #region Fields

    [HideInInspector] public VIDEOREWARD  rewardType = VIDEOREWARD.SKIPABLE;
    [HideInInspector] public object       rewardItem = null;
    private                  bool         isFirst    = true;
    public                   AdNetwork    adn;
    public                   AdNetwork    secondaryAdn;
    public                   List<string> secondaryCountries;
    [HideInInspector] public bool         allowBanner    = true;
    float                                 nextAdsTime    = 0;
    int                                   nextGameCount  = 0;
    private float                         _lastRwAdsTime = 0;

    public float lastRwAdsTime => _lastRwAdsTime;
    [HideInInspector] public bool isCachingFs = false;
    [HideInInspector] public bool isCachingRw = false;

    private int          rewardCompletedStep = 0;
    private Action<bool> OnRewardCompleted;

    private string _locationRewardAds = String.Empty;
    public string LocationRewardAds => _locationRewardAds;

    private event Action onCloseInterstitial;

    public Action OnCloseInterstitial;

	private string _locationInterstitial = string.Empty;
    public string LocationInterstitialAds => _locationInterstitial;

    private Coroutine                       _corWaitingRv;
    private Dictionary<AdErrorType, string> _adErrors = new Dictionary<AdErrorType, string>();

    public delegate void OnBannerLoadedHandler();

    public delegate void OnBannerChangeHandler();

    public event OnBannerLoadedHandler OnBannerLoaded;
    public event OnBannerChangeHandler OnBannerChange;

    public static bool IsLeavingByAds = false;

    public bool IsEnableBanner {
        get {
            if (!RemoteConfigBase.instance.BannerAd_Enable) {
                return false;
            }

            if (Configuration.IsNoAllAds()) {
                return false;
            }

            if (Configuration.IsNoSomeAds()) {
                return false;
            }

            if (Configuration.IsNoFSAds()) {
                return false;
            }

            int amountSession = UserProperties.GetSessionCount();
            if (amountSession < RemoteConfigBase.instance.BannerAd_x_session) {
                return false;
            }

            int songResult = AnalyticHelper.CountEvent(SONG_STATUS.song_result.ToString());
            if (songResult < RemoteConfigBase.instance.BannerAd_x_song_results) {
                return false;
            }

            return true;
        }
    }

    public bool IsEnableInterstitial {
        get {
            if (Configuration.IsNoAllAds()) {
                return false;
            }

            if (Configuration.IsNoSomeAds()) {
                return false;
            }

            if (Configuration.IsNoFSAds()) {
                return false;
            }

            return true;
        }
    }

    public float timeShowInterstitial { get; private set; } = -1f;
    public float timeShowRewardedAds { get; private set; } = -1f;

    #endregion

    #region Unity Method

    private void Start() {
        _adErrors = new Dictionary<AdErrorType, string> {
            { AdErrorType.FsShow, null },
            { AdErrorType.FsCaching, null },
            { AdErrorType.RwShow, null },
            { AdErrorType.RwCaching, null }
        };
        SetConsent();
        SetPrivacyMetaData();

        if (Configuration.isFirstOpen) {
            Util.WaitSongListDone(InitAdNet);
        } else {
            InitAdNet();
        }

        CMPWrapper.OnConsentCheckPassed += SetConsent;
    }

    #endregion

    #region Escape Handlers

    public bool CanHandleEventBack() {
        return IsWaitingRv();
    }

    public bool HandleEventBack() {
        DiscardWaitingRv();
        return true;
    }

    #endregion

    public string GetAdError(AdErrorType type) {
        if (_adErrors == null || !_adErrors.ContainsKey(type)) {
            return null;
        }

        return _adErrors[type];

    }

    private void InitAdNet() {
        Init();
        Util.WaitRemoteConfigDone(RemoteConfigLoaded, true);
    }

    private void Init() {
        if (secondaryCountries.Count > 0 && secondaryAdn != null) {
            string country = AnalyticHelper.instance.GetCountryCode();
            if (!string.IsNullOrEmpty(country) && secondaryCountries.Contains(country.ToUpper())) {
                adn = secondaryAdn;
            }
        }

        if (adn != null) {
            adn.Init();
            LoadFullAds();
            LoadRewardAds(VIDEOREWARD.None, null);
        }
    }

    public void SetConsent() {
        UnityMainThreadDispatcher.Instance().Enqueue(() => {
            bool isAllowed = true; // UserPermissions.IsATTAllowed();
            string additionalConsent = IabTcfStringHelper.GetAdditionalConsent();
            if (CMPWrapper.IsEeaOrUK && !string.IsNullOrEmpty(additionalConsent)) {
                //Debug.Log($"[AdsManager] Passing consent to ironSource: {additionalConsent.Contains("2878")}. In EEA or UK: {CMPWrapper.IsEeaOrUK}.");
                isAllowed &= additionalConsent.Contains("2878");
            } else {
                isAllowed = true;
                //Debug.Log($"[AdsManager] Force passing consent to ironSource. In EEA or UK: {CMPWrapper.IsEeaOrUK}.");
            }

            adn.SetConsent(isAllowed);
        });
    }

    private void RemoteConfigLoaded() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        //Load banner
        allowBanner = IsEnableBanner;
        LoadBanner();

        //Set ads time
        SetNextAdsTime(AdDelayOption.BeforeFirstFS);
    }

    public void ChangeSecondaryAdn() {
        if (secondaryAdn != null) {
            adn = secondaryAdn;
            adn.Init();
            LoadBanner();
        } else {
            Debug.LogError("Please set SecondaryAdn");
        }
    }

    public bool IsWaitingRv() {
        return _corWaitingRv != null;
    }

    public bool DiscardWaitingRv() {
        if (_corWaitingRv != null) {
            SceneFader.instance.HideOverlay();
            StopCoroutine(_corWaitingRv);
            _corWaitingRv = null;
            EventEscapeManager.Pop(this);
            return true;
        } else {
            return false;
        }
    }

    public void RequestShowInterstitial(string location, Action complete = null) {
        // Show Interstitial Video Ads
        if (IsEnableInterstitial) {
            nextGameCount++;
            if (Time.time > nextAdsTime || nextGameCount >= RemoteConfig.instance.Ad_MaxGamesBetweenFS) {
                ShowInterstitial(location, complete);
            } else {
                complete?.Invoke();
            }
        } else {
            complete?.Invoke();
        }
    }

    // opt==0: Ad_DelayBeforeFirstFS
    // opt==1: Ad_DelayBetweenFS
    // opt==2: Ad_DelayBetweenRVAndFS
    public void SetNextAdsTime(AdDelayOption opt) {
        nextGameCount = 0;
        switch (opt) {
            case AdDelayOption.BeforeFirstFS:
                nextAdsTime = Time.time + RemoteConfig.instance.Ad_DelayBeforeFirstFS;
                break;

            case AdDelayOption.BetweenFS:
                nextAdsTime = Time.time + RemoteConfig.instance.Ad_DelayBetweenFS;
                break;

            case AdDelayOption.BetweenRVAndFS:
                _lastRwAdsTime = Time.time;
                nextAdsTime = _lastRwAdsTime + RemoteConfigBase.instance.Ad_DelayBetweenRVAndFS;
                break;
        }
    }

    /// <summary>
    /// LoadFullAds
    /// </summary>
    /// <returns></returns>
    private void LoadFullAds() {
        StartCoroutine(IELoadFullAds());
    }

    private IEnumerator IELoadFullAds() {
        yield return null;

        int timeStep = 2;
        var wait = new WaitForSeconds(timeStep);
        var timeOut = RemoteConfig.instance.Ad_CacheRetryTimeout;
        var counter = 0;
        while (true) {
            if (Utils.isInternetReachable && !adn.IsInterstitialReady()) {
                if (!isCachingFs || counter >= timeOut) {
                    counter = 0;
                    isCachingFs = true;
                    AnalyticHelper.LogFullAds(AD_STATE.request, string.Empty);
                    adn.LoadInterstitial();
                }
            }

            counter += timeStep;
            yield return wait;
        }
    }

    #region Banner Ad

    public void LoadBanner() {
        if (!Utils.isInternetReachable) {
            return;
        }

        if (allowBanner && IsEnableBanner) {
            adn.LoadBanner();
        }
    }

    public float GetBannerHeight() {
        if (allowBanner && IsEnableBanner) {
            return adn.GetBannerHeight();
        }

        Logger.Log($"[Banner] bannerSize DPI: {0} because banner not active!!!????");
        return 0;
    }

    public void HideBanner() {
        //Debug.Log("[HideBanner] invoke");
        if (adn != null) {
            //Debug.Log("[AdsManager] HideBanner");
            adn.HideBanner();
        }
    }

    public void DestroyBanner() {
        if (adn != null) {
            adn.DestroyBanner();
        }
    }

    public void ShowBanner() {
        //Debug.Log("[ShowBanner] invoke");
        if (IsBannerAvailable() && adn != null) {
            //Debug.Log("[AdsManager] ShowBanner");
            adn.ShowBanner();
        }
    }

    public void BannerAdRequestEvent() {
        AnalyticHelper.LogBannerAds(AD_STATE.request);
    }

    public void BannerAdLoadedEvent() {
        OnBannerLoaded?.Invoke();
    }

    public void BannerAdShowedEvent() {
        AnalyticHelper.LogBannerAds(AD_STATE.show);
        OnBannerChange?.Invoke();
#if UNITY_EDITOR
        EditorShowBanner();
#endif
    }

    public void BannerAdHideEvent() {
        OnBannerChange?.Invoke();
#if UNITY_EDITOR
        EditorHideBanner();
#endif
    }

    public void BannerAdLoadFailedEvent(string error) {
        _adErrors[AdErrorType.BnCaching] = error;
        AnalyticHelper.LogBannerAds(AD_STATE.request_failed);
    }

    public bool IsBannerAvailable() {
        return allowBanner && IsEnableBanner;
    }

    public bool IsBannerLoaded() {
        return adn.bannerRequestStatus == AdNetwork.BannerRequestStatus.Loaded;
    }

    public bool IsBottomBannerShown() {
        return RemoteConfigBase.isInstanced && RemoteConfigBase.instance.BannerAd_IsBottom && IsBannerAvailable() &&
               IsBannerShowed();
    }

    public bool IsTopBannerShown() {
        return RemoteConfigBase.isInstanced && !RemoteConfigBase.instance.BannerAd_IsBottom && IsBannerAvailable() &&
               IsBannerShowed();
    }

    public bool IsBannerShowed() {
        return adn.bannerStatus == AdNetwork.BANNER_STATUS.SHOWED;
    }

    #endregion

    #region InterstitialAds

    public void InterstitialAdOpenedEvent() {
        timeShowInterstitial = Time.realtimeSinceStartup;
        AdsManager.IsLeavingByAds = true;
        AnalyticHelper.LogFullAds(AD_STATE.opened, _locationInterstitial);
    }

    public void InterstitialAdShowSucceededEvent() {
        AnalyticHelper.LogFullAds(AD_STATE.show_success, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    public void ShowInterstitial(string location, Action onClose) {
        this._locationInterstitial = location;
        this.onCloseInterstitial = onClose;
        AnalyticHelper.LogFullAds(AD_STATE.show_ready, _locationInterstitial);
        if (isFirst) {
            isFirst = false;
        }

        bool isReady = adn.IsInterstitialReady() || Application.isEditor;
        if (isReady) {
            UIOverlay.instance.InterstitialAdShowEvent();
            ToggleGameActivities(_locationInterstitial, true);
            AnalyticHelper.LogFullAds(AD_STATE.show, _locationInterstitial);
            AnalyticHelper.Screen(LOCATION_NAME.INTERSTITIAL_AD.ToString(), LOCATION_NAME.ADS.ToString());
            if (RemoteConfig.instance.SilentMode_IsEnable) {
                AmanoteNativeBinding.PlaySoundInSilentMode();
            }

            timeShowInterstitial = Time.realtimeSinceStartup;
            IsLeavingByAds = true;
            adn.ShowInterstitial();

            if (Application.isEditor) {
                InterstitialAdShowSucceededEvent();
                DOVirtual.DelayedCall(0.1f, InterstitialAdClosedEvent);
            }
        } else if (PlaygapManager.CanShowAd()) {
            _locationInterstitial = "PlaygapAd";
            PlaygapManager.instance.ShowInterstitialAdPressed();
        } else {
            AnalyticHelper.LogFullAds(AD_STATE.show_notready, _locationInterstitial);
            this.onCloseInterstitial?.Invoke();
        }
    }

    public void InterstitialAdClickedEvent() {
        AnalyticHelper.LogFullAds(AD_STATE.click, _locationInterstitial);
    }

    public void InterstitialAdReadyEvent() {
        isCachingFs = false;
        AnalyticHelper.LogFullAds(AD_STATE.request_success, String.Empty);
    }

    public void InterstitialAdLoadFailedEvent(string error) {
        isCachingFs = false;
        _adErrors[AdErrorType.FsCaching] = error;
        AnalyticHelper.LogFullAds(AD_STATE.request_failed, String.Empty);
    }

    public void InterstitialAdShowFailedEvent(string error) {
        this.onCloseInterstitial?.Invoke();
        UIOverlay.instance.InterstitialAdShowFailedEvent();
        _adErrors[AdErrorType.FsShow] = error;
        AnalyticHelper.LogFullAds(AD_STATE.show_failed, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    public void InterstitialAdClosedEvent() {
        this.onCloseInterstitial?.Invoke();
        OnCloseInterstitial?.Invoke();
        AnalyticHelper.LogFullAds(AD_STATE.finish, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
        bool inShowFromGameSetting =
            _locationInterstitial.Equals(LOCATION_NAME.GAME_SETTINGS.ToString(), StringComparison.OrdinalIgnoreCase);

        UIOverlay.instance.InterstitialAdClosedEvent(!inShowFromGameSetting);
        SetNextAdsTime(AdDelayOption.BetweenFS);
        ToggleGameActivities(_locationInterstitial, false);
        AnalyticHelper.ScreenBack(_locationInterstitial);
        if (RemoteConfig.instance.SilentMode_IsEnable) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }
    }

    #endregion

    #region Rewarded Video

    public void ShowRewardAds(VIDEOREWARD reward_type, object reward_item, string location, bool isFirstRequest = true,
                              Action<bool> OnCompleted = null, bool isAutoRewardWhenAdsNotReady = true) {
        OnRewardCompleted = OnCompleted;
        rewardCompletedStep = 0;
        rewardItem = reward_item;
        rewardType = reward_type;
        this._locationRewardAds = location;
        if (isFirstRequest) {
            AnalyticHelper.LogVideoAds(AD_STATE.show_ready, rewardType, rewardItem, location: _locationRewardAds);
        }

        bool isAvailable = adn.IsRewardedVideoAvailable() || Configuration.instance.isDebug;
        if (isAvailable) {
            ToggleGameActivities(_locationRewardAds, true);
            AnalyticHelper.Screen(LOCATION_NAME.REWARD_AD.ToString(), LOCATION_NAME.ADS.ToString());
            AnalyticHelper.LogVideoAds(AD_STATE.show, rewardType, rewardItem, this._locationRewardAds);
            UIOverlay.instance.RewardAdShowEvent();

            if (RemoteConfig.instance.SilentMode_IsEnable) {
                AmanoteNativeBinding.PlaySoundInSilentMode();
            }

            timeShowRewardedAds = Time.realtimeSinceStartup;
            IsLeavingByAds = true;
            adn.ShowRewardedVideo(GetPlacement());

            if (RemoteConfig.instance.downloadWhenWatchingRW && reward_item is Song song) {
                DownloadManager.instanceSafe.CheckDataAndDownload(song);
            }

            if (Configuration.instance.isDebug) {
                //AnalyticHelper.LogRewardAds(AD_STATE.show, rewardType, rewardItem);
                rewardCompletedStep = 1;
                CompleteAdRewarded();
            }
        } else if (isFirstRequest) {
            _corWaitingRv = StartCoroutine(WaitingRewardedVideo(isAutoRewardWhenAdsNotReady));
            EventEscapeManager.Push(this);

        } else if (PlaygapManager.CanShowAd()) {
            _locationRewardAds = "PlaygapAd";
            PlaygapManager.instance.ShowRewardedAdPressed();

        } else {
            // because of no-internet
            if (Utils.IsAndroid() && !Utils.isInternetReachable) {
                NoInternetPopup.Show();
            } else {
                Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("REWARD_VIDEO_UNAVAILABLE"));
            }

            AnalyticHelper.LogVideoAds(AD_STATE.show_notready, rewardType, rewardItem, this._locationRewardAds);
            OnCompleted?.Invoke(false);
        }
    }

    private string GetPlacement() {
        switch (rewardType) {
            case VIDEOREWARD.revive:
                return "reborn";

            case VIDEOREWARD.currency_x2:
                return "double";

            case VIDEOREWARD.currency:

                if (FreeVideo.instance != null) {
                    return "diamond_freegift";
                } else if (Shop.instance != null) {
                    return "diamond_shop";
                }

                break;

            case VIDEOREWARD.item:
                // if (BallList.instance != null && BallList.instance.gameObject.activeSelf) {
                //     return "character_collection";
                // }
                if (FreeVideo.instance != null) {
                    return "character_freegift";
                }

                if (UIController.ui != null && UIController.ui.gameover.gameObject.activeSelf) {
                    return "character_result";
                }

                break;

            case VIDEOREWARD.song_unlock:
                if (FreeVideo.instance != null) {
                    return "stage_freegift";
                } else if (Util.IsHomeScene()) {
                    return "stage_home";
                } else if (UIController.ui != null && UIController.ui.gameover.gameObject.activeSelf) {
                    return "stage_result";
                }

                break;

            case VIDEOREWARD.CHALLENGE:
                return "challenge_home";
        }

        return null;
    }

    private IEnumerator WaitingRewardedVideo(bool isAutoRewardWhenAdsNotReady = true) {
        SceneFader.instance.ShowOverlay();
        var wait = new WaitForSeconds(0.2f);
        float time = Time.realtimeSinceStartup;
        // Special case
        while (!adn.IsRewardedVideoAvailable() &&
               Time.realtimeSinceStartup - time < RemoteConfigBase.instance.Ad_WaitMaxTime &&
               Utils.isInternetReachable) {
            yield return wait;
        }

        SceneFader.instance.HideOverlay();
        if (!adn.IsRewardedVideoAvailable() && Utils.isInternetReachable &&
            RemoteConfigBase.instance.Ad_WaitMaxAllowUnlock && isAutoRewardWhenAdsNotReady) {
            if (PlaygapManager.CanShowAd()) {
                _locationRewardAds = "PlaygapAd";
                PlaygapManager.instance.ShowRewardedAdPressed();
            } else {
                AnalyticHelper.LogVideoAds(AD_STATE.special, rewardType, rewardItem, this._locationRewardAds);
                rewardCompletedStep = 1;
                CompleteAdRewarded(true);
            }
        } else {
            // try again
            ShowRewardAds(rewardType, rewardItem, this._locationRewardAds, false, OnRewardCompleted);
        }

        _corWaitingRv = null;
        EventEscapeManager.Pop(this);
    }

    public void RewardedVideoAdOpenedEvent() {
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = true;
        AnalyticHelper.LogVideoAds(AD_STATE.opened, rewardType, rewardItem, this._locationRewardAds);
    }

    public void RewardedVideoLoaded() {
        AnalyticHelper.LogVideoAds(AD_STATE.request_success, rewardType, rewardItem, string.Empty);
    }

    public void RewardedVideoLoadFailed(string error) {
        _adErrors[AdErrorType.RwCaching] = error;
        AnalyticHelper.LogVideoAds(AD_STATE.request_failed, rewardType, rewardItem, string.Empty);
    }

    public void RewardedVideoClickedEvent() {
        AnalyticHelper.LogVideoAds(AD_STATE.click, rewardType, rewardItem, this._locationRewardAds);
    }

    public void ToggleGameActivities(string location, bool isPause = true) {
        if (Application.isMobilePlatform && Application.platform == RuntimePlatform.IPhonePlayer) {
            if (isPause) {
                GroundMusic.instance.StopMusic(0);
            } else {
                if (Util.IsHomeScene()) {
                    GroundMusic.instance.PlayMusic(location);
                }
            }
        }
    }

    public void RewardedVideoAdRewardedEvent() {
        CompleteAdRewarded();
    }

    public void RewardedVideoAdClosedEvent() {
        UIOverlay.instance.RewardAdClosedEvent();
        ToggleGameActivities(_locationRewardAds, false);
        AnalyticHelper.ScreenBack(_locationRewardAds);
        SetNextAdsTime(AdDelayOption.BetweenRVAndFS);
        if (RemoteConfigBase.instance.SilentMode_IsEnable) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }

        CompleteAdRewarded();
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    private void CompleteAdRewarded(bool isSpecial = false) {
#if UNITY_EDITOR
        if (RemoteConfigBase.instance.SilentMode_IsEnable) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }
#endif

        if (Configuration.instance.isDebug) {
            UIOverlay.instance.RewardAdClosedEvent();
        }

        rewardCompletedStep++;
        if (rewardCompletedStep == 2) { //Require two steps: RewardedVideoAdClosedEvent and RewardedVideoAdRewardedEvent
            if (!isSpecial) {
                AnalyticHelper.LogVideoAds(AD_STATE.finish, rewardType, rewardItem, this._locationRewardAds);
            }

            OnRewardCompleted?.Invoke(true);
        }
    }

    public void RewardedVideoAdSkipped() {
        ToggleGameActivities(_locationRewardAds, false);
        OnRewardCompleted?.Invoke(false);
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    public void RewardedVideoAdShowFailedEvent(string error) {
        UIOverlay.instance.RewardAdShowFailedEvent();
        _adErrors[AdErrorType.RwShow] = error;
        ToggleGameActivities(_locationRewardAds, false);
        AnalyticHelper.ScreenBack(_locationRewardAds);
        AnalyticHelper.LogVideoAds(AD_STATE.show_failed, rewardType, rewardItem, this._locationRewardAds);
        OnRewardCompleted?.Invoke(false);
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    private void LoadRewardAds(VIDEOREWARD type, object reward) {
        //Auto caching
    }

    #endregion

    public void OnApplicationPause(bool pauseStatus) {
        if (adn != null) {
            adn.AppPause(pauseStatus);
        }

        // if (!pauseStatus) {
        //     if (IsLeavingByAds) {
        //         StartCoroutine(IEResumeByAds());
        //     }
        // }
    }

    // private static IEnumerator IEResumeByAds() {
    //     yield return new WaitForSeconds(0.5f);
    //     IsLeavingByAds = false;
    // }

    #region UnityAds

    private void SetPrivacyMetaData() {
#if UNITY_ANDROID //&& !UNITY_EDITOR
        if (Application.isEditor) {
            return;
        }
        
        AndroidJavaClass unityPlayerClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject unityActivity = unityPlayerClass.GetStatic<AndroidJavaObject>("currentActivity");

        AndroidJavaObject metaDataObj = new AndroidJavaObject("com.unity3d.ads.metadata.MetaData", unityActivity);
        metaDataObj.Call<bool>("set", "privacy.mode", "mixed");
        metaDataObj.Call<bool>("set", "user.nonbehavioral", false);
        metaDataObj.Call("commit");
#endif
    }

    #endregion

    #region Log AD ID

    private StringBuilder _sbLogAd;

    public void AddLogAd(Dictionary<string, string> dicAdInfo) {
        string serializeObject = DateTime.Now.ToLongTimeString() + " => " + JsonConvert.SerializeObject(dicAdInfo);

        if (Configuration.isAdmin) {
            _sbLogAd ??= new StringBuilder();
            _sbLogAd.AppendLine(serializeObject);
        }

        PlayerPrefs.SetString(PlayerPrefsKey.lastIdAds, serializeObject);
    }

    public string GetLogAd() {
        if (_sbLogAd == null) {
            string s = PlayerPrefs.GetString(PlayerPrefsKey.lastIdAds, "Empty!");
            return s;
        } else {
            return _sbLogAd.ToString();
        }
    }

    #endregion

    #region editor

#if UNITY_EDITOR

    private GameObject _testBanner;

    private void EditorShowBanner() {
        if (_testBanner == null) {
            string path = @"Assets\GameCore\Editor\Prefabs\Canvas_Banner.prefab";
            GameObject textAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(path);
            _testBanner = Instantiate(textAsset);
        }

        _testBanner.SetActive(true);
    }

    private void EditorHideBanner() {
        if (_testBanner) {
            _testBanner.SetActive(false);
        }
    }

#endif

    #endregion

    public void RecheckBannerStatus() {
        if (allowBanner)
            return;

        //Load banner
        allowBanner = IsEnableBanner;
        if (allowBanner) {
            LoadBanner();
        }
    }
}