using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;

//using Sirenix.OdinInspector;

public class AudioPlayer : MonoBehaviour {
    #region Fields

    private static AudioPlayer instance;

    //[DictionaryDrawerSettings] [ShowInInspector]
    private Dictionary<string, AudioClip> dictionary;

    [SerializeField] private AudioSource[] audioSources;

    public bool sound = true;

    #endregion

    #region Unity Method

    void Awake() {
        if (instance == null) {
            instance = this;
            Initialize();
        }
    }

    #endregion

    public static bool GetSound() {
        return instance.sound;
    }

    public static void SetSound(bool sound) {
        instance.sound = sound;
    }

    private void Initialize() {
#if UNITY_EDITOR
        // make sure that in Project Settings > Audio, the "DSP Buffer Size" is set to "Best latency"
        AudioConfiguration config = AudioSettings.GetConfiguration();
        Debug.Assert(config.dspBufferSize <= 256, "DSP buffer size: " + config.dspBufferSize);
#endif

        dictionary = new Dictionary<string, AudioClip>();
        foreach (AudioSource audioSource in audioSources) {
            if (audioSource != null) {
                audioSource.playOnAwake = false;
                audioSource.bypassEffects = true;
                audioSource.loop = false;
            }
        }
    }

    private void Destroy() {
        for (int channel = 0; channel < audioSources.Length; ++channel) {
            if (audioSources[channel] != null)
                Destroy(audioSources[channel]);
            audioSources[channel] = null;
        }

        foreach (KeyValuePair<string, AudioClip> clip in dictionary) {
            if (!clip.Value.preloadAudioData)
                clip.Value.UnloadAudioData();
        }

        dictionary.Clear();
    }

    private IEnumerator IELoad(string path, string soundName) {
        // www loading must be done in a coroutine
        WWW www = new WWW(path);
        yield return www;

        AudioClip clip = www.GetAudioClip(false);

        if (clip != null && clip.loadState == AudioDataLoadState.Loaded) {
            clip.name = soundName;
            dictionary[soundName] = clip;
        } else {
            Logger.LogError($"[AudioPlayer] Can't load audio clip with name: {soundName}");
        }
    }

    public static IEnumerator Play(int channel, string name, float volume = 1f, bool loop = false) {
        if (!Configuration.instance.SoundIsOn()) {
            yield break;
        }

        //Load sound if not exist
        if (!instance.dictionary.ContainsKey(name)) {
            string path = $"{FileHelper.StreamingURL()}SFXs/{name}{FILE_EXT.OGG}";
            yield return instance.StartCoroutine(instance.IELoad(path, name));
        }

        //Play sound
        AudioSource audioSource = instance.audioSources[channel];
        if (audioSource != null && instance.dictionary.TryGetValue(name, out AudioClip clip)) {
            audioSource.clip = clip;
            audioSource.volume = volume;
            audioSource.pitch = 1f; // see note in Play() for Android
            audioSource.loop = loop;
            if (RemoteConfigBase.instance.SilentMode_IsEnable) {
                AmanoteNativeBinding.PlaySoundInSilentMode();
            }

            audioSource.Play();
        } else {
            Logger.LogError($"[Play] Sound {name} is null");
        }
    }

    public static void Stop(int channel) {
        if (instance.audioSources[channel] != null) {
            instance.audioSources[channel].Stop();
        }
    }

    public static void Stop(string nameSound) {
        foreach (var audioSource in instance.audioSources) {
            if (audioSource.clip != null && audioSource.clip.name.Equals(nameSound)) {
                audioSource.Stop();
            }
        }
    }

    public static bool IsPlaying(int channel) {
        if (instance.audioSources[channel] != null)
            return instance.audioSources[channel].isPlaying;
        else
            return false;
    }

    public static void Pause() {
        for (int channel = 0; channel < instance.audioSources.Length; ++channel) {
            if (instance.audioSources[channel] != null)
                instance.audioSources[channel].Pause();
        }
    }

    public static void Resume() {
        for (int channel = 0; channel < instance.audioSources.Length; ++channel) {
            if (instance.audioSources[channel] != null) {
                if (instance.sound)
                    instance.audioSources[channel].UnPause();
                else
                    instance.audioSources[channel].Stop();
            }
        }
    }

    public static void EndLoad() {
    }

    public static void ClearAudio() {
        foreach (KeyValuePair<string, AudioClip> clip in instance.dictionary) {
            if (!clip.Value.preloadAudioData) {
                clip.Value.UnloadAudioData();
            }
        }

        instance.dictionary.Clear();
    }
}