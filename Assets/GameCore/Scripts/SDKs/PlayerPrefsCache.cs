using System.Collections.Generic;
using UnityEngine;

public static class PlayerPrefsCache {
    private static Dictionary<string, object> _cache = new Dictionary<string, object>();

    public static int GetInt(string key, int defaultValue = 0) {
        if (_cache.TryGetValue(key, out object value)) {
            return (int) value;
        }

        int result = PlayerPrefs.GetInt(key, defaultValue);
        _cache[key] = result;
        return result;
    }

    public static void SetInt(string key, int value) {
        _cache[key] = value;
        PlayerPrefs.SetInt(key, value);
    }

    public static string GetString(string key, string defaultValue = null) {
        if (_cache.TryGetValue(key, out object value)) {
            return (string) value;
        }

        string result = PlayerPrefs.GetString(key, defaultValue);
        _cache[key] = result;
        return result;
    }

    public static void SetString(string key, string value) {
        _cache[key] = value;
        PlayerPrefs.SetString(key, value);
    }

    public static void Save() {
        PlayerPrefs.Save();
    }

    public static void ClearCache() {
        _cache.Clear();
    }
}