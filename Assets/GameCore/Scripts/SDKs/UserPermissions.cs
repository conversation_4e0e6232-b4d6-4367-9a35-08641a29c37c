using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Runtime.InteropServices;
using System;
using Amanotes.Push;

public class UserPermissions : Singleton<UserPermissions> {
#if UNITY_IPHONE && !UNITY_EDITOR
	[DllImport ("__Internal")]
    private static extern void _inwaveFBSetAdvertiserTrackingEnabled(bool enabled);
    [DllImport ("__Internal")]
    private static extern void _inwaveRequestUserIDFA();
#endif
    [SerializeField] private NotificationInit _notificationInit;

    private Action _onRequestUserIdfaCompleted;
    private Action _onRequestNotificationCompleted;
    private bool   _requestingNotification;

    private delegate void PermissionRequest();

    private PermissionRequest _permissionRequest1a;
    private PermissionRequest _permissionRequest1b;
    private PermissionRequest _permissionRequest1c;
    private PermissionRequest _permissionRequest2a;

    private const string REQUESTED_NOTIFICATION_KEY = "requestedNotification";
    public const  string IS_ALLOW_ATT_KEY           = "is_att_allowed";

    private void OnApplicationFocus(bool focus) {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            if (focus && _requestingNotification) {
                PlayerPrefs.SetInt(REQUESTED_NOTIFICATION_KEY, 1);
                StartCoroutine(IEInvokeRequestNotificationCompleted());
            }
        }
    }

    public void ShowFirstPermissionsGroup() {
        //Debug.Log("[UserPermissions] Showing first permission group...");
        if (Application.platform == RuntimePlatform.Android || Application.platform == RuntimePlatform.WindowsEditor) {
            // map permissions order
            // 1st permission
            if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "1a") {
                _permissionRequest1a = CMPWrapper.instance.TryLoadConsentForm;
                CMPWrapper.OnConsentCheckPassed += InvokePermissionRequest1B;
            } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "1a") {
                _permissionRequest1a = RequestOfflineNotification;
                _onRequestNotificationCompleted += InvokePermissionRequest1B;
            }

            // 2nd permission
            if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "1b") {
                _permissionRequest1b = CMPWrapper.instance.TryLoadConsentForm;
            } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "1b") {
                _permissionRequest1b = RequestOfflineNotification;
            }
        } else if (Application.platform == RuntimePlatform.IPhonePlayer ||
                   Application.platform == RuntimePlatform.OSXEditor) {
            // map permissions order
            // 1st permission
            if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "1a") {
                _permissionRequest1a = CMPWrapper.instance.TryLoadConsentForm;
                CMPWrapper.OnConsentCheckPassed += InvokePermissionRequest1B;
            } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "1a") {
                _permissionRequest1a = RequestOfflineNotification;
                _onRequestNotificationCompleted += InvokePermissionRequest1B;
            } else if (RemoteConfigBase.instance.UserPermissions_ATTOrder == "1a") {
                _permissionRequest1a = () => StartCoroutine(IELoadUserIDFA());
                _onRequestUserIdfaCompleted += InvokePermissionRequest1B;
            }

            // 2nd permission
            if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "1b") {
                _permissionRequest1b = CMPWrapper.instance.TryLoadConsentForm;
                CMPWrapper.OnConsentCheckPassed += InvokePermissionRequest1C;
            } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "1b") {
                _permissionRequest1b = RequestOfflineNotification;
                _onRequestNotificationCompleted += InvokePermissionRequest1C;
            } else if (RemoteConfigBase.instance.UserPermissions_ATTOrder == "1b") {
                _permissionRequest1b = () => StartCoroutine(IELoadUserIDFA());
                _onRequestUserIdfaCompleted += InvokePermissionRequest1C;
            }

            // 3rd permission
            if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "1c") {
                _permissionRequest1c = CMPWrapper.instance.TryLoadConsentForm;
            } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "1c") {
                _permissionRequest1c = RequestOfflineNotification;
            } else if (RemoteConfigBase.instance.UserPermissions_ATTOrder == "1c") {
                _permissionRequest1c = () => StartCoroutine(IELoadUserIDFA());
            }
        }

        if (_permissionRequest1a != null) {
            _permissionRequest1a.Invoke();
        } else if (_permissionRequest1b != null) {
            _permissionRequest1b.Invoke();
        } else if (_permissionRequest1c != null) {
            _permissionRequest1c.Invoke();
        }
    }

    private void InvokePermissionRequest1B() => _permissionRequest1b?.Invoke();
    private void InvokePermissionRequest1C() => _permissionRequest1c?.Invoke();

    public void ShowSecondPermissionsGroup() {
        //Debug.Log("[UserPermissions] Showing second permission group...");
        // map permissions order
        if (RemoteConfigBase.instance.UserPermissions_CMPOrder == "2a") {
            _permissionRequest2a = CMPWrapper.instance.TryLoadConsentForm;
        } else if (RemoteConfigBase.instance.UserPermissions_NotificationsOrder == "2a") {
            _permissionRequest2a = RequestOfflineNotification;
        }

        _permissionRequest2a?.Invoke();
    }

    private IEnumerator IELoadUserIDFA() {
        RequestUserIDFA();
        SetTracking(true);
        yield return new WaitForSeconds(0f);
    }

    /// <summary>
    /// Status = 1 or 0, 1 means that user accepted
    /// </summary>
    /// <param name="status"></param>
    public void OnRequestUserIDFAReady(string status) {
        PlayerPrefs.SetInt(IS_ALLOW_ATT_KEY, int.Parse(status));
        _onRequestUserIdfaCompleted?.Invoke();
        if (RevenueCatPurchases.instance) {
            RevenueCatPurchases.instance.ReloadAttributes();
        }
        if (AdsManager.instance) {
            AdsManager.instance.SetConsent();
        }
    }

    /// <summary>
    /// Set Facebook Ad Tracking
    /// </summary>
    /// <param name="enable"></param>
    static void SetTracking(bool enable) {
#if UNITY_IPHONE && !UNITY_EDITOR
		_inwaveFBSetAdvertiserTrackingEnabled(enable);
#endif
    }

    /// <summary>
    /// To display the App Tracking Transparency authorization request for accessing the IDFA
    /// </summary>
    static void RequestUserIDFA() {
#if UNITY_IPHONE && !UNITY_EDITOR
        _inwaveRequestUserIDFA();
#endif
    }

    private IEnumerator IEInvokeRequestNotificationCompleted() {
        yield return null;

        _requestingNotification = false;
        _onRequestNotificationCompleted?.Invoke();
    }

    // Regist offline Notification
    private void RequestOfflineNotification() {
        //Debug.Log("Requesting offline notifications...");
        _notificationInit.InitNotification(); //Call showing notification request

        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            _requestingNotification = true;
            if (PlayerPrefs.HasKey(REQUESTED_NOTIFICATION_KEY)) {
                StartCoroutine(IEInvokeRequestNotificationCompleted());
            }
        } else {
            _onRequestNotificationCompleted?.Invoke();
        }
    }

    public static bool IsATTAllowed() {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            return PlayerPrefs.GetInt(IS_ALLOW_ATT_KEY, 0) == 1;
        }

        return true;
    }
}