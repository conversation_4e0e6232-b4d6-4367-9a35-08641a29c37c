using System;
using System.Collections.Generic;
using Facebook.MiniJSON;
using Inwave;
using Newtonsoft.Json;
using UnityEngine;

[Serializable]
public class SubPackage {
    public string id;
    public string price;
    public float  priceValue;
    public string currencyCode;
    public string week_price;
    public string saleoff;
}

public static class SubPackageManager {
    private static Dictionary<IAPDefinitionId, SubPackage> _packages;

    private const  string WeekPackageId         = "WeekPackageID";
    private const  string PackagePrefix         = "subscription_";
    // private const  string DataKey               = "SubPackageManager";
    private  const string CurrentPackageIdKey        = "CurrentPackageId";
    public const   string CurrentPurchasedIdKey      = "CurrentPurchasedId";
    public const   string CachedDefinitionId = "CurrentPurchasePackageType";

    public static bool            isEnableMultiple      = true;
    static        IAPDefinitionId _currentPackageChoose = IAPDefinitionId.subscription_week;
    public static Action          OnPriceUpdate;
    public static bool            IsLoadedPrices = false;

    private static PaywallWeeklySegmentConfig segment;

    public static void Init() {
        _packages = new Dictionary<IAPDefinitionId, SubPackage>();

        if (RemoteConfig.instance.MultiSubPackages_Enable) {
            string input = GetCurrentConfig();
            try {
                if (!string.IsNullOrEmpty(input) && Json.Deserialize(input) is Dictionary<string, object> list) {
                    foreach (var item in list) {
                        if (item.Value != null) {
                            var subpackage = new SubPackage();
                            var rawPackage = (Dictionary<string, object>) item.Value;
                            foreach (var raw in rawPackage) {
                                if (raw.Key != null && raw.Value != null) {
                                    var field = typeof(SubPackage).GetField(raw.Key);
                                    if (field != null) {
                                        field.SetValue(subpackage, raw.Value.ToString());
                                    }
                                }
                            }

                            _packages.Add(
                                (IAPDefinitionId) Enum.Parse(typeof(IAPDefinitionId), PackagePrefix + item.Key),
                                subpackage);
                        }
                    }
                }

            } catch (Exception e) {
                CustomException.Fire("SubPackageManager Init", e.Message);
            }
        }

        if (_packages.Count == 0) {
            isEnableMultiple = false;
            var oldPackage = new SubPackage {
                id = RemoteConfig.instance.Subscription_WeekPackageID,
                price = RemoteConfig.instance.Subscription_Price
            };
            _packages.Add(IAPDefinitionId.subscription_week, oldPackage);
        }

        if (RemoteConfig.instance.Subscription_NoAds_IsEnable) {
            SubPackage noAdsPackage = new SubPackage {
                id = RemoteConfig.instance.Subscription_NoAds_PackageID,
                price = RemoteConfig.instance.Subscription_NoAds_Price
            };
            _packages.Add(IAPDefinitionId.subs_no_ads, noAdsPackage);
        }

        OptimizeWeeklyPaywall(); //TH-2597 (03/2024)

        //Add product to IapBase
        AddProducts();
        //Update cached prices
        UpdatePrices();
    }

    private static void OptimizeWeeklyPaywall() {
        if (string.IsNullOrEmpty(RemoteConfig.instance.paywall_weekly_plan_optimization)) {
            return;
        }

        PayWallWeeklyConfig config =
            JsonUtility.FromJson<PayWallWeeklyConfig>(RemoteConfig.instance.paywall_weekly_plan_optimization);
        if (config == null || config.segments == null || config.segments.Length == 0) return;

        bool isSurveyDemographicDone = PlayerPrefs.GetInt(PlayerPrefsKey.survey_demographic_done, 0) == 1;

        string os = Utils.IsAndroid() ? SubscriptionConfig.Platform.Android : SubscriptionConfig.Platform.IOS;
        string region = Configuration.instance.CountryName;

        if (isSurveyDemographicDone) {
            string age = PlayerPrefs.GetString(PlayerPrefsKey.survey_demographic_select_age);
            string gender = PlayerPrefs.GetString(PlayerPrefsKey.survey_demographic_select_gender);
            Logger.EditorLog("[Subscription Weekly]", $"After Survey OS: {os}. region: {region}. Age: {age}. Gender: {gender}");
            segment = config.GetSegment(os,region, age, gender);
        } else {
            //not survey
            Logger.EditorLog("[Subscription Weekly]", $"Before Survey OS: {os}. region: {region}");
            segment = config.GetSegmentBeforeSurvey(os, region);
        }

        if (segment != null) {
            Logger.EditorLog("[Subscription Weekly]", JsonUtility.ToJson(segment));
            var package =   new SubPackage {
                id = segment.pack,
                price = segment.price
            };
            if (_packages.ContainsKey(IAPDefinitionId.subscription_week)) {
                _packages[IAPDefinitionId.subscription_week] = package;
            } else {
                _packages.Add(IAPDefinitionId.subs_no_ads, package);
            }
        }
    }

    public static string[] GetOderSubscriptions() {
        return segment?.order_in_see_all;
    }

    public static void ReInitSubscriptionPackages() {
        OptimizeWeeklyPaywall();

        //Add product to IapBase
        AddProducts();
        //Update cached prices
        UpdatePrices();
    }

    public static void UpdatePrices() {
        foreach (var pack in _packages) {
            var priceUpdate = IapBase.GetInforProduct(pack.Key);
            if (!string.IsNullOrEmpty(priceUpdate.price)) {
                pack.Value.price = priceUpdate.price;
                pack.Value.priceValue = priceUpdate.priceValue;
                pack.Value.currencyCode = priceUpdate.priceCode;
            }
        }

        IsLoadedPrices = true;
        OnPriceUpdate?.Invoke();
    }

    static string GetCurrentConfig() {
        return RemoteConfig.instance.SubPackages;
    }

    public static string GetPackageName(IAPDefinitionId id) {
        return IapBase.GetProductName(id);
    }

    public static string GetPackageId(IAPDefinitionId id) {
        return _packages.TryGetValue(id, out SubPackage package) ? package.id : null;
    }

    public static string GetPackagePrice(IAPDefinitionId id) {
        return _packages.TryGetValue(id, out SubPackage package) ? package.price : string.Empty;
    }

    public static float GetPackagePriceValue(IAPDefinitionId id) {
        return _packages.TryGetValue(id, out SubPackage package) ? package.priceValue : 0;
    }

    public static string GetPackagePriceCode(IAPDefinitionId id) {
        return _packages.TryGetValue(id, out SubPackage package) ? package.currencyCode : string.Empty;
    }

    public static string GetPackageSubPrice(IAPDefinitionId id) {
        return _packages[id].week_price;
    }

    public static string GetSaleOff(IAPDefinitionId id) {
        return _packages.TryGetValue(id, out SubPackage package) ? package.saleoff : string.Empty;
    }

    private static void AddProducts() {
        if (PlayerPrefs.HasKey(WeekPackageId)) {
            _packages[IAPDefinitionId.subscription_week].id = PlayerPrefs.GetString(WeekPackageId);
        }

        foreach (var pack in _packages) {
            IapBase.AddProduct(pack.Key, ProductType.Subscription, pack.Value.id, pack.Value.id);
        }
    }

    public static void SetCurrentPackageId(IAPDefinitionId id) {
        _currentPackageChoose = id;
        PlayerPrefs.SetString(CurrentPackageIdKey, GetPackageId(id));
    }
#if !UNITY_PURCHASING
    /// <summary>
    /// Get current package id (RevenueCat)
    /// </summary>
    /// <param name="p"></param>
    /// <returns></returns>
    public static string GetCurrentPackageId(Purchases.StoreProduct p = null) {
        if (p == null) {
            return PlayerPrefs.GetString(CurrentPackageIdKey, GetPackageId(GetDefaultSubPackageId()));
        } else {
            return p.Identifier;
        }
    }
#else
    /// <summary>
    /// Get current package id (Unity Purchasing)
    /// </summary>
    /// <param name="p"></param>
    /// <returns></returns>
    public static string GetCurrentPackageId(Product p) {
        if (p == null) {
            return PlayerPrefs.GetString(CurrentPackageIdKey, GetPackageId(GetDefaultSubPackageId()));
        } else {
            return p.definition.storeSpecificId;
        }
    }
#endif

    /// <summary>
    /// Get current IAPDefinitionId (cached from SetCurrentPackageId)
    /// </summary>
    /// <returns></returns>
    public static IAPDefinitionId GetCurrentIapDefinitionId() {
        return _currentPackageChoose;
    }

    /// <summary>
    /// Return DefaultSubPackageId follows RemoteConfig 
    /// </summary>
    /// <returns></returns>
    public static IAPDefinitionId GetDefaultSubPackageId() {
        if (RemoteConfig.instance.MultiSubPackages_Enable) {
            var _orders = SubPackageManager.GetOderSubscriptions();
            if (_orders != null && _orders.Length > 1) {
                var center = _orders[1];
                if (center.Equals("week", StringComparison.InvariantCultureIgnoreCase)) {
                    return IAPDefinitionId.subscription_week;
                } else if (center.Equals("month", StringComparison.InvariantCultureIgnoreCase)) {
                    return IAPDefinitionId.subscription_month;
                } else if (center.Equals("year", StringComparison.InvariantCultureIgnoreCase)) {
                    return IAPDefinitionId.subscription_year;
                }
            }

            return IAPDefinitionId.subscription_month;
        }

        return IAPDefinitionId.subscription_week;
    }

    /// <summary>
    /// Use for discount purchase purpose
    /// </summary>
    /// <param name="id"></param>
    public static void SetCurrentPurchasedId(string id) {
        PlayerPrefs.SetString(CurrentPurchasedIdKey, id);
    }

    /// <summary>
    /// Use for discount purchase purpose
    /// </summary>
    public static string GetCurrentPurchasedId() {
        return PlayerPrefs.GetString(CurrentPurchasedIdKey);

    }

    private static Dictionary<string, IAPDefinitionId> _cachedDefinitionIAP;
    public static void SetCachedDefinitionId(string id, IAPDefinitionId group) {
        if (_cachedDefinitionIAP == null) {
            LoadCachedDefinitionId();
        }
        _cachedDefinitionIAP ??= new Dictionary<string, IAPDefinitionId>();
        if(_cachedDefinitionIAP.ContainsKey(id)) return;
        _cachedDefinitionIAP.Add(id,group);
        PlayerPrefs.SetString(CachedDefinitionId, JsonConvert.SerializeObject(_cachedDefinitionIAP));
    }

    private static void LoadCachedDefinitionId() {
        if (PlayerPrefs.HasKey(CachedDefinitionId)) {
            _cachedDefinitionIAP =
                JsonConvert.DeserializeObject<Dictionary<string, IAPDefinitionId>>(
                    PlayerPrefs.GetString(CachedDefinitionId, string.Empty));
        }
    }
    public static IAPDefinitionId GetCachedDefinitionId(string id) {
        if (string.IsNullOrWhiteSpace(id)) {
            return IAPDefinitionId.none;
        }
        if (_cachedDefinitionIAP == null) {
            LoadCachedDefinitionId();
        }

        _cachedDefinitionIAP ??= new Dictionary<string, IAPDefinitionId>();
        if (_cachedDefinitionIAP.ContainsKey(id))
            return _cachedDefinitionIAP[id];

        //check remote!!!!
        var weeks = RemoteConfig.instance.SubPackages_Week_Old.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var old in weeks) {
            if(string.IsNullOrWhiteSpace(old)) continue;
            if (id.Equals(old)) {
                var iapDefinitionId = IAPDefinitionId.subscription_week;
                SetCachedDefinitionId(id, iapDefinitionId);
                return iapDefinitionId;
            }
        }

        var months = RemoteConfig.instance.SubPackages_Month_Old.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var old in months) {
            if(string.IsNullOrWhiteSpace(old)) continue;
            if (id.Equals(old)) {
                var iapDefinitionId = IAPDefinitionId.subscription_month;
                SetCachedDefinitionId(id, iapDefinitionId);
                return iapDefinitionId;
            }
        }

        var years = RemoteConfig.instance.SubPackages_Year_Old.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var old in years) {
            if(string.IsNullOrWhiteSpace(old)) continue;
            if (id.Equals(old)) {
                var iapDefinitionId = IAPDefinitionId.subscription_year;
                SetCachedDefinitionId(id, iapDefinitionId);
                return iapDefinitionId;
            }
        }

        return IAPDefinitionId.none;
    }
}