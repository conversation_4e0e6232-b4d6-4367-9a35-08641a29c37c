using System;
using UnityEngine;

public class ColorSongCardHelper {

	private static Color[] _themeColors = new Color[] {
		new Color(0.4196079f,0.7019608f,0.7960785f),//01
		new Color(0.9647059f,0.6f,0.7607844f),//03
		new Color(0.9803922f,0.9803922f,0.4f),//05
		new Color(0.6039216f,0.8509805f,0.9490197f),//07
		new Color(0.145098f,0.1372549f,0.1411765f),//09	
		new Color(0.8313726f,0.7882354f,0.7568628f),//11
		new Color(0.854902f,0.1490196f,0.5803922f),//13
		new Color(0.764706f,0.2745098f,0.2745098f),//15
		new Color(0.9254903f,0.1568628f,0.2313726f),//17
		new Color(0.945f,0.949f,0.9411f),//19
		new Color(0.12549f,0.3137255f,0.172549f),//21 hex:20502C
		new Color(0.3137255f,0.27843f,0.788235f),//23 hex:5047C9
		new Color(0.007843f,0.2549f,0.317647f),//25 hex:024151
		new Color(0f,0f,0f),//27 hex:000000
		new Color(0.02745f,0.12549f,0.698f),//29 hex:0720B2
		new Color(0.27843f,0.145098f,0.109804f),//31 hex:47251C
		new Color(0.807843f,0.415686f,0.17647f),//33 hex:CE6A2D

	};
	private static bool[] _whiteColors = new bool[] {
		false,//01
		false,//03
		true,//05
		false,//07
		false,//09
		true,//11
		false,//13
		false,//15
		false,//17
		true,//19
		false,//21
		false,//23
		false,//25
		false,//27
		false,//29
		false,//31
		false,//33
	};
	public static int GetIdTheme(Texture2D texture2D) {
		Color dominantColor = GetDominantColor(texture2D);
		return GetIdTheme(dominantColor);
	}
	private static Color GetDominantColor(Texture2D texture2D) {
		if (!texture2D.isReadable) {
			Logger.EditorLogError("texture can't readable -> need to fix it !!!");
			return Color.black;
		}
		//Used for tally
		float r = 0;
		float g = 0;
		float b = 0;
		int total = 0;
		int width = texture2D.width;
		int height = texture2D.height;
		for (int x = 0; x < width; x++) {
			for (int y = 0; y < height; y++) {
				if (x == 0 || y == 0 || x == width - 1 || y == height - 1) { // chỉ lấy viền
					Color clr = texture2D.GetPixel(x, y);
					r += clr.r;
					g += clr.g;
					b += clr.b;
					total++;
				}
			}
		}

		//Calculate average
		r /= total;
		g /= total;
		b /= total;

		return new Color(r, g, b);
	}

	private static int GetIdTheme(Color color) {
		if (_themeColors.Length < 1) return -1;

		int tempIndex = 0;
		double minOffset = ColorDiff(color, _themeColors[tempIndex]);
		for (int i = 1; i < _themeColors.Length; i++) {
			double offset = ColorDiff(color, _themeColors[i]);
			if (offset < minOffset) {
				minOffset = offset;
				tempIndex = i;
			}
		}

		return tempIndex;
	}

	public static bool IsWhite(int idCardStyle) {
		if (idCardStyle < 0 || idCardStyle >= _whiteColors.Length)
			return true;
		return _whiteColors[idCardStyle];
	}


	public static bool IsWhite(Texture2D texture) {
		Color mainColor = GetDominantColor(texture);
		float average = (mainColor.r + mainColor.g + mainColor.b) / 3;
		return average > 0.8f;
	}

	// distance in RGB space
	private static double ColorDiff(Color c1, Color c2) {
		return Math.Sqrt((c1.r - c2.r) * (c1.r - c2.r)
						+ (c1.g - c2.g) * (c1.g - c2.g)
						+ (c1.b - c2.b) * (c1.b - c2.b));
	}
}
