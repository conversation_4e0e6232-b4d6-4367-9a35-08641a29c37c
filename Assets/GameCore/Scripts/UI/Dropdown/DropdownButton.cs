using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;

namespace TileHop.UI {
    public class DropdownButton : MonoBehaviour, IMessageReceiver {
        [SerializeField] private Button    mainButton;
        [SerializeField] private Transform container;
        [SerializeField] private float     collapsedHeight = 85f;
        [SerializeField] private float     expandedHeight  = 300f;

        [Space]
        [Header("Animation")]
        [SerializeField] private GameObject iconDown;

        [SerializeField] private   GameObject    iconUp;
        [SerializeField] protected RectTransform rectBox;
        [SerializeField] private   float         animatedTime;
        [SerializeField] private   Ease          expandEasing   = Ease.OutBack;
        [SerializeField] private   Ease          collapseEasing = Ease.Linear;

        public    bool      isExpanded   = false;
        private   bool      _isAnimating = false;
        private   Coroutine _animateCoroutine;
        protected Vector2   collapsedSize;
        protected Vector2   expandedSize;

        protected List<ButtonWithBadge> listItems;

        public Transform itemContainer => container;
        
        private void Start() {
            float sizeX = rectBox.sizeDelta.x;
            collapsedSize = new Vector2(sizeX, collapsedHeight);
            expandedSize = new Vector2(sizeX, expandedHeight);
            mainButton.onClick.AddListener(ToggleDropdown);
            ForceCollapse();
        }

        private void OnDestroy() {
            if (listItems != null) {
                ListPool<ButtonWithBadge>.Release(listItems);
            }
        }

        public void ToggleDropdown() {
            if (_isAnimating) {
                StopAnimation();
            }

            isExpanded = !isExpanded;

            if (isExpanded) {
                _animateCoroutine = StartCoroutine(IEExpand());
            } else {
                _animateCoroutine = StartCoroutine(IECollapse());
            }
        }

        protected virtual IEnumerator IEExpand() {
            _isAnimating = true;
            container.gameObject.SetActive(true);
            SwitchIcon(false);
            
            rectBox.DOSizeDelta(expandedSize, animatedTime).SetEase(expandEasing);
            yield return YieldPool.GetWaitForSeconds(animatedTime);

            _isAnimating = false;
        }

        protected virtual IEnumerator IECollapse() {
            _isAnimating = true;
            rectBox.DOSizeDelta(collapsedSize, animatedTime).SetEase(collapseEasing);
            yield return YieldPool.GetWaitForSeconds(animatedTime);

            SwitchIcon(true);
            _isAnimating = false;
            container.gameObject.SetActive(false);
        }

        private void SwitchIcon(bool isCollapsed) {
            iconDown.SetActive(isCollapsed);
            iconUp.SetActive(!isCollapsed);
        }

        protected void ForceCollapse() {
            StopAnimation();
            rectBox.sizeDelta = collapsedSize;
            isExpanded = false;
            SwitchIcon(true);
            container.gameObject.SetActive(false);
        }

        protected void Collapse() {
            if (_isAnimating) {
                StopAnimation();
            }
            
            if (isExpanded) {
                isExpanded = false;
                _animateCoroutine = StartCoroutine(IECollapse());
            }
        }

        private void StopAnimation() {
            if (_animateCoroutine != null) {
                StopCoroutine(_animateCoroutine);
            }

            rectBox.DOKill();
            _isAnimating = false;
        }

        public bool AddButton(string buttonResourceName, ref ButtonWithBadge button) {
            var asset = Resources.Load<ButtonWithBadge>(Util.BuildString(string.Empty, "Buttons/Dropdown_", buttonResourceName));
            if (asset == null) {
                return false;
            }
            
            button = Instantiate(asset, container);
            button.messageReceiver = this;
            
            listItems ??= ListPool<ButtonWithBadge>.Get();
            listItems.Add(button);
            
            #if UNITY_EDITOR
            button.gameObject.name = buttonResourceName;
            #endif
            
            return true;
        }

        public virtual void PassInt(int value) { }
    }
}
