using System.Collections;
using DG.Tweening;
using Inwave.CommunicationSystem;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public sealed class SeasonalPackRewardInfoPanel : Mono<PERSON><PERSON><PERSON>our, IEscapeHandler {
    [Header("Animate Open/Close")]
    [SerializeField] private float animatedDuration;
    [SerializeField] private Transform[] animatedTransforms;
    [SerializeField] private Button      btnClose;
    [SerializeField] private Button      btnShowBallReward;

    private static bool _isNavigating = false;
    private Tweener[] _animatedTweeners;
    private int       _animatedCount;
    
    [HideInInspector] public SeasonalPackPopup popupInstance;

    #region UNITY EVENTS

    private void Awake() {
        _animatedCount = animatedTransforms.Length;
        _animatedTweeners = new Tweener[_animatedCount];

        // setup animated objects
        for (int i = 0; i < _animatedCount; i++) {
            int index = i;
            _animatedTweeners[index] = DOTween.To(x => animatedTransforms[index].localScale = Vector3.one * x, 
                    0.0f, 1.0f, animatedDuration)
                .SetAutoKill(false)
                .SetEase(Ease.OutBack)
                .SetId(index)
                .Pause();
        }
        
        //setup onclick
        btnClose.onClick.AddListener(Close);
        btnShowBallReward.onClick.AddListener(ShowBallReward);
    }

    private void OnEnable() {
        for (int i = 0; i < _animatedCount; i++) {
            _animatedTweeners[i].PlayForward();
        }
        EventEscapeManager.Push(this);
        
        btnShowBallReward.interactable = true;
    }

    private void OnDestroy() {
        foreach (Tweener tweener in _animatedTweeners) {
            tweener?.Kill();
        }
    }

    #endregion

    #region ESCAPE HANDLE

    public bool CanHandleEventBack() {
        return gameObject.activeInHierarchy;
    }

    public bool HandleEventBack() {
        Close();
        return true;
    }

    #endregion

    #region BASE METHODS

    public void Close() {
        for (int i = 0; i < _animatedCount; i++) {
            _animatedTweeners[i].PlayBackwards();
        }
        
        EventEscapeManager.Pop(this);
        StartCoroutine(IEClose());
    }

    private IEnumerator IEClose() {
        yield return YieldPool.GetWaitForSeconds(animatedDuration);
        gameObject.SetActive(false);
    }

    #endregion

    #region METHODS

    private void ShowBallReward() {
        btnShowBallReward.interactable = false;
        popupInstance.ForceClose();

        int ballId =
            SeasonalPackManager.Config.BallRewards[Random.Range(0, 99999) % SeasonalPackManager.Config.BallRewards.Length];

        if (HomeManager.instance) {
            _isNavigating = true;
            HomeManager.waitOnStart = new WaitWhile(() => _isNavigating || HomeManager.instance.IsShowingShop);
            HomeManager.instance.NavigateBallShop(ballId);
            _isNavigating = false;
        } else {
            //TH-3656: prevent auto-show 'discovery challenge' when navigate to shop ball
            if (DiscoveryChallengeManager.isInstanced && DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge) {
                DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge = false;
                HomeManager.CachedTabActived = BottomMenuScript.Type.Home;
            }

            LastStateData.LastLocation = LastStateData.Locations.Unknown;
            
            // if not in home then back to home and navigate to shop ball
            Util.GoHome();
            HomeManager.waitOnStart = new WaitWhile(() => !HomeManager.instance || _isNavigating || HomeManager.instance.IsShowingShop);
            Configuration.instance.StartCoroutine(IENavigateToShopBall(ballId));
        }
    }

    private IEnumerator IENavigateToShopBall(int ballId) {
        _isNavigating = true;
        yield return new WaitUntil(() => HomeManager.instance);
        HomeManager.instance.NavigateBallShop(ballId);
        _isNavigating = false;
    }

    #endregion
}
