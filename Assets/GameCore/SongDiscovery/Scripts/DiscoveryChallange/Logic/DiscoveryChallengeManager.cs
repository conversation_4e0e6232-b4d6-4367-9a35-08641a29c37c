using System;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CielaSpike;
using Inwave;
using TileHop.DiscoveryChallenge;
using Unity.Collections;
using UnityEngine.Pool;
using Random = UnityEngine.Random;

public class DiscoveryChallengeManager : FastSingleton<DiscoveryChallengeManager> {
    [Serializable]
    private class DiscoveryChallengeUserData {
        /// <summary>
        /// Trạng thái active của event, khi đếm ngược kết thúc thì cũng reset trạng thái active về False (True = actived)
        /// </summary>
        public bool isActive;

        /// <summary>
        /// Trạng thái thực hiện challenge của user
        /// </summary>
        public ChallengeState eventState = ChallengeState.Disable;

        /// <summary>
        /// Thời gian active event gần nhất, dùng để xác định thời điểm active tiếp theo (theo tuần)
        /// </summary>
        public string lastActiveDateTime;

        /// <summary>
        /// list id các bài hát challenge trong tuần, reset mỗi khi sang tuần mới
        /// </summary>
        public List<string> challengeSongPaths;

        public DiscoveryChallengeUserData() {
            challengeSongPaths = new List<string>();
        }
    }

    private const string KEY_TOOLTIP_HOME                        = "discovery_challenge_tooltip_home";
    private const string DISCOVERY_CHALLENGE_USERDATA            = "Discovery_Challenge_UserData";
    private const string DISCOVERY_CHALLENGE_NUMBER              = "Discovery_Challenge_Number";
    private const string CHALLENGELIST_KEY                       = "ChallengeSongList";
    private const byte   DEFAULT_NUMBER_OF_HARDCORE_SONG_TO_SHOW = 7;

    private const string DISCOVERY_PREVIEWBOX_NUMBER = "Discovery_PreviewBox_Number";

    #region Fiels

    public Dictionary<string, Song> challengeSongsPool;

    private List<string> _challengeSongPaths;
    private List<Song>   _cachedChallengeSongs;

    private Coroutine _updateCoroutine;

    [SerializeField] [ReadOnly] private bool                       _isInit;
    [SerializeField] [ReadOnly] private DiscoveryChallengeConfig   _config;
    [SerializeField] [ReadOnly] private DiscoveryChallengeUserData _userData;

    private DateTime _eventEndTime;
    private DateTime _eventNextTime;
    private DateTime _lastActiveDateTime;

    private Sprite _challengeBannerSprite;
    private Sprite _challengeAcceptButtonSprite;

    public static bool IsInitedSongList;

    public static int consecutiveShortSongStart = 0;
    public static event Action<ChallengeState> OnEventStateChange;
    public static event Action OnInitedSongList;

    private byte? _currentTotalStar;
    public byte totalStar {
        get {
            return _currentTotalStar ??= GetChallengeTotalStars();
        }
        private set => _currentTotalStar = value;
    }

    private byte? _currentUnlockSong;

    private byte totalUnlockSong {
        get {
            return _currentUnlockSong ??= GetUnlockedSongs();
        }
        set => _currentUnlockSong = value;
    }

    #endregion

    #region Properties

    /// <summary>
    /// Trạng thái hiện tại của challenge
    /// </summary>
    public ChallengeState EventState {
        get => _userData.eventState;
        private set {
            if (value != _userData.eventState) {
                _userData.eventState = value;
                OnEventStateChange?.Invoke(value);
            }
        }
    }

    public bool IsActive => _userData != null && _userData.isActive;

    public DiscoveryChallengeConfig Config => _config;

    /// <summary>
    /// Thời gian còn lại đến khi sang event tiếp theo
    /// </summary>
    public TimeSpan RemainTimeToNextEvent => _eventNextTime - DateTime.UtcNow;

    /// <summary>
    /// Thời gian còn lại đến khi kết thúc event
    /// </summary>
    public TimeSpan RemainTimeToEndEvent => _eventEndTime - DateTime.UtcNow;

    public static int ChallengeNumber {
        get => PlayerPrefs.GetInt(DISCOVERY_CHALLENGE_NUMBER, 0);
        private set => PlayerPrefs.SetInt(DISCOVERY_CHALLENGE_NUMBER, value);
    }

    private static int previewBoxNumber {
        get => PlayerPrefs.GetInt(DISCOVERY_PREVIEWBOX_NUMBER, 1);
        set => PlayerPrefs.SetInt(DISCOVERY_PREVIEWBOX_NUMBER, value);
    }

    public bool IsInprogress => EventState is ChallengeState.Accepted or ChallengeState.Completed;

    public bool PlaySongFromChallenge;

    #endregion

    #region CONFIG AND INIT

    protected override void Awake() {
        base.Awake();
        if (instance == this) {
            DontDestroyOnLoad(this.gameObject);
            Init();
        }
    }

    private void Start() {
        SongList.OnUnlockSong += SongListOnOnUnlockSong;
        SubscriptionController.OnChange += HandleOnSubscription;
    }

    protected override void OnDestroy() {
        base.OnDestroy();
        SubscriptionController.OnChange -= HandleOnSubscription;

        if (_isInit) {
            LocalizationManager.instance.OnLanguageChange -= ProcessRemoteLocalize;
        }
    }

    /// <summary>
    /// Lưu data khi pause app
    /// </summary>
    private void OnApplicationPause(bool pause) {
        if (!pause)
            return;

        if (_userData != null) {
            SaveUserData();
        }
    }

    private void OnApplicationFocus(bool hasFocus) {
        if (hasFocus) {
            return;
        }

        if (_userData != null) {
            SaveUserData();
        }
    }

    public DiscoveryChallengeConfig GetConfig() {
        if (!_isInit) {
            Init();
        }

        return _config;
    }

    /// <summary>
    /// Hủy instance sau khi init nếu không có config
    /// </summary>
    public void InitSettings() {
        if (!_isInit) {
            Init();
        }

        if (_config == null) {
            Destroy(this.gameObject);
        }
    }

    private void Init() {
        if (_isInit) {
            return;
        }

        _isInit = true;

        challengeSongsPool = new Dictionary<string, Song>();
        // _challengeSongACMPool = new List<string>();

        string dataConfig = RemoteConfigBase.instance.DiscoveryChallenge_Config;
        if (!string.IsNullOrEmpty(dataConfig)) {
            try {
                _config = JsonUtility.FromJson<DiscoveryChallengeConfig>(dataConfig);
            } catch (Exception e) {
                CustomException.Fire("[Get Discovery Challenge Config]", e.Message + " => " + dataConfig);
            }
        }

        if (_config == null) {
            return;
        }

        ProcessRemoteLocalize();

        LocalizationManager.instance.OnLanguageChange += ProcessRemoteLocalize;

        LoadUserData();
        StartCoroutine(DownloadRemoteSongConfig(RemoteConfigBase.instance.DiscoveryChallenge_ChallengeList_Path));
    }

    public void ProcessRemoteLocalize() {
        // thêm remote localized, xử lý khi remote key trùng local key
        LocalizationManager.instance.AddLocalizedStrings(RemoteConfigBase.instance.DiscoveryChallenge_LocalizedTexts,
            LocalizationManager.GetCurrentLanguageID(), onDone: (isSuccess, duplicateMap) => {
                if (!isSuccess)
                    return;

                if (duplicateMap.ContainsKey(_config.titleLocalizedKey)) {
                    _config.titleLocalizedKey = duplicateMap[_config.titleLocalizedKey];
                }

                if (duplicateMap.ContainsKey(_config.descriptionLocalizedKey)) {
                    _config.descriptionLocalizedKey = duplicateMap[_config.descriptionLocalizedKey];
                }
            });
    }

    /// <summary>
    /// Parse SongConfig Text To SongList Dictionary
    /// </summary>
    /// <param name="songsPoolType"></param>
    /// <param name="data">SongConfig Text</param>
    public IEnumerator IEProcessSongList(string data = null) {
        yield return Ninja.JumpToUnity;

        if (string.IsNullOrEmpty(data)) {
            data = GetSavedSongConfig();
        }

        yield return Ninja.JumpBack;

        var newSongList = ItemContainer.GetSongs(data);

        TextAsset defaultData = Configuration.instance.defaultChallengeSongPoolConfig;

        if (newSongList.Count > 0) {
            yield return Ninja.JumpToUnity;

            //Apply new downloaded songList
            if (!string.IsNullOrEmpty(data)) {
                CacheSongData(data);
            }

            challengeSongsPool = newSongList;
        } else {
            //Apply default songList in build
            yield return Ninja.JumpToUnity;

            string defaultSongConfigText = defaultData.text;
            yield return Ninja.JumpBack;

            challengeSongsPool = ItemContainer.GetSongs(defaultSongConfigText);
            yield return Ninja.JumpToUnity;
        }

        yield return Ninja.JumpToUnity;

        var challengeSongACMPool = ListPool<string>.Get();

        foreach (var song in challengeSongsPool) {
            song.Value.isChallengeSong = true;
            song.Value.UpdateData();
            challengeSongACMPool.Add(song.Value.acm_id_v3);
        }

        SongManager.instance.HideSongsInList(challengeSongACMPool);
        ListPool<string>.Release(challengeSongACMPool);

        if (!_challengeSongPaths.IsNullOrEmpty()) {
            RefreshErrorChallengeSongs();
        }

        IsInitedSongList = true;
        OnInitedSongList?.Invoke();

        if (_updateCoroutine != null) {
            StopCoroutine(_updateCoroutine);
        }

        _updateCoroutine = StartCoroutine(IEUpdate());
    }

    void CacheSongData(string data) {
        PlayerPrefs.SetString(RemoteConfig.prefix + CHALLENGELIST_KEY, data);
    }

    string GetSavedSongConfig() {
        return PlayerPrefs.GetString(RemoteConfig.prefix + CHALLENGELIST_KEY);
    }

    /// <summary>
    /// Download Remote SongConfig by url
    /// </summary>
    /// <param name="songsPoolType"></param>
    /// <param name="url"></param>
    /// <returns></returns>
    IEnumerator DownloadRemoteSongConfig(string url) {
        AnalyticHelper.Game_Step(GAME_STEP.Step_SongConfigDownload);
        float startWaitTime = Time.time;
        WWW wwwTemp = new WWW(url);
        while (!wwwTemp.isDone && Time.time - startWaitTime < 5) {
            yield return null;
        }

        //Prevent loadsong at game play
        while (GameController.CheckInstanced() && GameController.instance.game == GameStatus.LIVE) {
            yield return null;
        }

        if (wwwTemp.isDone && wwwTemp.error == null) {
            yield return this.StartCoroutineAsync(IEProcessSongList(wwwTemp.text));
        } else {
            yield return this.StartCoroutineAsync(IEProcessSongList());
        }
    }

    private void LoadUserData() {
        if (PlayerPrefs.HasKey(DISCOVERY_CHALLENGE_USERDATA)) {
            string jsonData = PlayerPrefs.GetString(DISCOVERY_CHALLENGE_USERDATA);
            try {
                _userData = JsonUtility.FromJson<DiscoveryChallengeUserData>(jsonData);
            } catch (Exception e) {
                CustomException.Fire("[Get Discovery Challenge UserData]", e.Message + " => " + jsonData);
                _userData = new DiscoveryChallengeUserData();
            }

            if (DateTime.TryParse(_userData.lastActiveDateTime, CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out _lastActiveDateTime)) {
                SetupTime();
            }

            _challengeSongPaths = _userData.challengeSongPaths;
        } else {
            _userData = new DiscoveryChallengeUserData();
            _userData.eventState = ChallengeState.Disable;
        }
    }

    private void SaveUserData() {
        string jsonData = JsonUtility.ToJson(_userData);
        PlayerPrefs.SetString(DISCOVERY_CHALLENGE_USERDATA, jsonData);
    }

    #endregion

    #region METHODS

    private void HandleOnSubscription(bool isBuySuccess) {
        if (isBuySuccess && _cachedChallengeSongs != null) {
            foreach (var item in _cachedChallengeSongs) {
                item.savedType = SONGTYPE.OPEN;
            }
        }
    }

    private void SetupTime() {
        _eventEndTime = _lastActiveDateTime.AddHours(_config.eventDurationHours);
        _eventNextTime = _lastActiveDateTime.AddHours(_config.eventIntervalTimeHours);

        if (EventState == ChallengeState.Accepted) {
            _userData.isActive = DateTime.UtcNow.CompareTo(_eventNextTime) < 0;
        }
    }

#if UNITY_EDITOR
    public string       RemainEnd, RemainNext;
    public List<string> challengeSongsName;
#endif

    private IEnumerator IEUpdate() {
        var wait = new WaitForSecondsRealtime(1f);

        while (true) {
#if UNITY_EDITOR
            RemainEnd = RemainTimeToEndEvent.ToString();
            RemainNext = RemainTimeToNextEvent.ToString();
#endif

            if (EventState == ChallengeState.Accepted) {
                if (RemainTimeToEndEvent.CompareTo(TimeSpan.Zero) <= 0) {
                    TimeOut();
                }
            } else if (EventState != ChallengeState.Disable) {
                if (RemainTimeToNextEvent.CompareTo(TimeSpan.Zero) <= 0) {
                    TryActive();
                    yield break;
                }
            }

            yield return wait;
        }
    }

    private void TimeOut() {
        isClickedAccept = false;
        EventState = ChallengeState.Failed;
        DiscoveryChallengeTracker.Track_ChallengeTimOut(ChallengeNumber, _config.numberOfStarToUnlockGoldenBox,
            totalStar, _config.numberOfChallengeSongs, totalUnlockSong, EventState, _cachedChallengeSongs,
            GetElapseTime());
    }

    /// <summary>
    /// Active challenge nếu đủ điều kiện và trả về kết quả đã active hay chưa
    /// </summary>
    /// <returns></returns>
    public bool TryActive() {
        if (_userData == null || _config == null) {
            return false;
        }

        if (_config.CheckShowCondition() && EventState != ChallengeState.Unknown) {
            // Start active challenge and generate new challenge songs
            // if new event period
            if (string.IsNullOrEmpty(_userData.lastActiveDateTime) || DateTime.UtcNow - _lastActiveDateTime >=
                TimeSpan.FromHours(_config.eventIntervalTimeHours)) {
                _userData.isActive = true;

                ChallengeNumber++;
                previewBoxNumber = 1;
                // reset trạng thái event
                totalStar = 0;
                EventState = ChallengeState.Unknown;

              bool isValid =  GenerateListChallengeSongs();
              if (!isValid) {
                  return false;
              }
            }
        }

#if UNITY_EDITOR
        challengeSongsName = new List<string>();
        if (_challengeSongPaths != null)
            foreach (var song in _challengeSongPaths) {
                if (challengeSongsPool.ContainsKey(song)) {
                    challengeSongsName.Add(challengeSongsPool[song].name);
                }
            }
#endif

        return _userData.isActive;
    }

    private bool GenerateListChallengeSongs() {
        // lấy ra các bài hát ngẫu nhiên không trùng nhau
        var keyPool = challengeSongsPool.Keys.ToList();

        // remove các bài hát challenge của tuần trước
        if (_challengeSongPaths != null && _challengeSongPaths.Count != 0) {
            foreach (var path in _challengeSongPaths) {
                if (keyPool.Contains(path)) {
                    keyPool.Remove(path);
                }
            }
            _challengeSongPaths.Clear();
        } else {
            _challengeSongPaths = new List<string>(_config.numberOfChallengeSongs);
        }
        _cachedChallengeSongs = null;

        //TH-3292: fix argument exception
        if (keyPool.Count < _config.numberOfChallengeSongs) {
            return false;
        }

        List<string> hardList = new  List<string>();
        List<string> mediumList = new  List<string>();
        foreach (var key in keyPool) {
            if (challengeSongsPool[key].GetDifficultyTag() == DifficultyTag.MEDIUM) {
                mediumList.Add(key);
            } else {
                hardList.Add(key);
            }
        }
        keyPool.Clear();

        int numberHardSong = _config.numberOfChallengeSongs;
        bool existMediumSong = false;
        if (mediumList.Count != 0) {
            numberHardSong -= 1;
            existMediumSong = true;
        }
        for (int i = 0; i < numberHardSong; i++) {
            string key = hardList[Random.Range(0, hardList.Count)];
            hardList.Remove(key);
            AddKey(key);
        }
        hardList.Clear();
        if (existMediumSong) {
            string key = mediumList[Random.Range(0, mediumList.Count)];
            AddKey(key);
        }
        mediumList.Clear();

        _userData.challengeSongPaths = _challengeSongPaths;

        return true;

        void AddKey(string keySong) {
            _challengeSongPaths.Add(keySong);
            Song song = challengeSongsPool[keySong];
            RemoteConfig.instance.Midi_FullControl_Force += $";{song.acm_id_v3}";
            song.savedType = SONGTYPE.VIDEO;

            // reset best star của bài hát challenge được chọn
            song.BestStar = 0;
            Configuration.SetBestStars(0, song.path);

            // reset trạng thái IsPlayed
            SecuredPlayerPrefs.DeleteKey(CONFIG_STRING.BestScore + keySong);

            PlayerPrefs.DeleteKey(keySong);
        }
    }

    /// <summary>
    /// Lấy danh sách Challenge Songs
    /// </summary>
    /// <returns>Các bài hát dùng trong Discover Challenge</returns>
    public bool GetChallengeSongs(out List<Song> result) {
        if (_challengeSongPaths == null) {
            result = null;
            return false;
        }

        if (_cachedChallengeSongs == null) {
            _cachedChallengeSongs = new List<Song>(capacity: _config.numberOfChallengeSongs);
            foreach (string path in _challengeSongPaths) {
                if (challengeSongsPool.ContainsKey(path)) {
                    _cachedChallengeSongs.Add(challengeSongsPool[path]);
                }
            }
        }

        MakeDefaultFreeFirstChallengeSongs();
        result = _cachedChallengeSongs;
        return true;
    }

    public List<Song> GetChallengeSongs() {
        if (_challengeSongPaths == null) {
            return null;
        }

        if (_cachedChallengeSongs == null || _cachedChallengeSongs.Count == 0) {
            _cachedChallengeSongs = new List<Song>(capacity: _config.numberOfChallengeSongs);
            foreach (string path in _challengeSongPaths) {
                if (challengeSongsPool.ContainsKey(path)) {
                    _cachedChallengeSongs.Add(challengeSongsPool[path]);
                }
            }
        }

        MakeDefaultFreeFirstChallengeSongs();
        return _cachedChallengeSongs;
    }

    /// <summary>
    /// TH-3457: bài hát challenge không còn tồn tại trong Challenge Songlist
    /// => Random lại bài hát bị missing
    /// </summary>
    private void RefreshErrorChallengeSongs() {
        bool isDetectErrorSong = false;
        List<string> keyPool = null;

        int challengeSongCount = _challengeSongPaths.Count;
        for (int id = 0; id < challengeSongCount; id++) {
            if (!challengeSongsPool.ContainsKey(_challengeSongPaths[id])) {
                if (!isDetectErrorSong) { // chỉ copy key pool một lần khi phát hiện có bài hát bị missing
                    isDetectErrorSong = true;
                    keyPool = new List<string>(challengeSongsPool.Keys);

                    foreach (string songPath in _challengeSongPaths) {
                        if (keyPool.Contains(songPath)) {
                            keyPool.Remove(songPath);
                        }
                    }
                }

                string errorSongPath = _challengeSongPaths[id];
                string isPlayedKey = Util.BuildString(null, CONFIG_STRING.BestScore, _challengeSongPaths[id]);
                int backupBestStars = Configuration.GetBestStars(errorSongPath);

                // thay thế bài hát bị missing
                _challengeSongPaths[id] = keyPool[Random.Range(0, keyPool.Count)];

                // khôi phục số sao và trạng thái IsPlayed tương ứng với bài hát bị missing
                Configuration.SetBestStars(backupBestStars, _challengeSongPaths[id]);

                if (PlayerPrefs.HasKey(isPlayedKey)) {
                    PlayerPrefs.SetInt(Util.BuildString(null, CONFIG_STRING.BestScore, _challengeSongPaths[id]),
                        PlayerPrefs.GetInt(isPlayedKey, 0));
                }

                keyPool.Remove(errorSongPath);
            }
        }

        if (isDetectErrorSong) {
            _userData.challengeSongPaths = _challengeSongPaths;
        }
    }

    private void MakeDefaultFreeFirstChallengeSongs() {
        if (_config.canDefaultFreeFirstSongInChallengeList > 0 && _cachedChallengeSongs.Count > 0) {
            // nếu user là VIP, mở khóa toàn bộ challenge songs
            int n = SubscriptionController.IsSubscriptionVip()
                ? _cachedChallengeSongs.Count
                : Mathf.Min(_cachedChallengeSongs.Count, _config.canDefaultFreeFirstSongInChallengeList);

            for (int i = 0; i < n; i++) {
                _cachedChallengeSongs[i].savedType = SONGTYPE.OPEN;
                _cachedChallengeSongs[i].type = SONGTYPE_STRING.OPEN;
            }
        }
    }

    /// <summary>
    /// Lấy top {x} bài từ list các bài hardcore trong GoldenBox
    /// </summary>
    /// <param name="numberOfSongs">{x}</param>
    /// <returns></returns>
    public List<Song> GetTopHardcoreSongs(byte numberOfSongs = DEFAULT_NUMBER_OF_HARDCORE_SONG_TO_SHOW) {
        return HardcoreSongCollection.instanceSafe.GetTopHardcoreSongs(numberOfSongs);
    }

    /// <summary>
    /// gọi khi user nhận thưởng unlock hardcore song
    /// </summary>
    public void CollectHardcoreSong(Song song) {
        if (EventState != ChallengeState.Completed)
            return;

        EventState = ChallengeState.OpenedGoldenBox;
        HardcoreSongCollection.instanceSafe.CollectHardcoreSong(song);
    }

    /// <summary>
    /// Lấy tổng số stars trong challenge
    /// </summary>
    /// <returns></returns>
    public byte GetChallengeTotalStars() {
        if (GetChallengeSongs(out var challengeSongs)) {
            if (challengeSongs == null)
                return 0;

            byte totalStars = 0;
            foreach (Song song in challengeSongs) {
                byte star = (byte) Mathf.Clamp(Configuration.GetBestStars(song.path), 0, 3);
                totalStars += star;
            }

            return totalStars;
        }

        return 0;
    }

    private byte GetUnlockedSongs() {
        if (_cachedChallengeSongs == null) {
            return 0;
        }

        byte countUnlock = 0;
        foreach (Song song in _cachedChallengeSongs) {
            if (song.IsOpened()) {
                countUnlock += 1;
            }
        }

        return countUnlock;
    }

    /// <summary>
    /// check xem user đã đủ điều kiện nhận golden box chưa
    /// </summary>
    public void CheckCompletedChallenge(Song currentSong) {
        if (!IsInitedSongList)
            return;
        if (_cachedChallengeSongs == null)
            return;
        if (!_cachedChallengeSongs.Contains(currentSong))
            return;

        if (EventState is ChallengeState.Accepted) {
            int beforeStar = totalStar;
            ChallengeState beforeState = EventState;
            totalStar = GetChallengeTotalStars();
            if (totalStar >= _config.numberOfStarToUnlockGoldenBox) {
                EventState = ChallengeState.Completed;
                isClickedAccept = false;

                ShowPopupReward();

                DiscoveryChallengeTracker.Track_ChallengeComplete(ChallengeNumber,
                    _config.numberOfStarToUnlockGoldenBox, totalStar, _config.numberOfChallengeSongs, totalUnlockSong,
                    EventState, _cachedChallengeSongs, GetElapseTime());
            }

            DiscoveryChallengeTracker.Track_ChallengeUpdate(ChallengeNumber, _config.numberOfStarToUnlockGoldenBox,
                totalStar, _config.numberOfChallengeSongs, totalUnlockSong, totalUnlockSong, ActionType.play,
                beforeState, EventState, _cachedChallengeSongs, currentSong, beforeStar, GetElapseTime());
        }
    }

    public GameObject ShowPopupReward() {
        return Util.ShowPopUp(PopupName.DiscoveryChallengeReward);
    }

    /// <summary>
    /// Lấy ảnh banner challenge
    /// </summary>
    /// <returns></returns>
    public async Task<Sprite> GetChallengeImage() {
        if (string.IsNullOrEmpty(_config.challengeBannerImageUrl)) {
            return null;
        }

        if (_challengeBannerSprite != null) {
            return _challengeBannerSprite;
        }

        _challengeBannerSprite = await DownloadManager.instanceSafe.DownloadImage(_config.challengeBannerImageUrl);
        return _challengeBannerSprite;
    }

    /// <summary>
    /// Lấy ảnh của accept button
    /// </summary>
    /// <returns></returns>
    public async Task<Sprite> GetAcceptButtonImage() {
        if (string.IsNullOrEmpty(_config.acceptButtonImageUrl)) {
            return null;
        }

        if (_challengeAcceptButtonSprite != null) {
            return _challengeAcceptButtonSprite;
        }

        _challengeAcceptButtonSprite = await DownloadManager.instanceSafe.DownloadImage(_config.acceptButtonImageUrl);
        return _challengeAcceptButtonSprite;
    }

    #endregion

    #region STATIC METHODS

    public static bool IsEnable() {
        return RemoteConfigBase.instance != null && RemoteConfigBase.instance.DiscoveryChallenge_Is_Enable &&
               !string.IsNullOrEmpty(RemoteConfigBase.instance.DiscoveryChallenge_Config);
    }

    public static void WaitChallengeSongListDone(Action action) {
        if (action == null) {
            return;
        }

        if (IsInitedSongList) {
            action.Invoke();
        } else {
            OnInitedSongList += action;
        }
    }

    #endregion

    public bool isClickedAccept;

    public void OnClickBanner(Source source) {
        isClickedAccept = true;
        HomeManager.instance.CheckNoticeDiscover(EventState);
        switch (EventState) {
            case ChallengeState.Unknown:
                SaveLastActiveDateTime();
                EventState = ChallengeState.Accepted;
                if (_updateCoroutine != null) {
                    StopCoroutine(_updateCoroutine);
                }

                _updateCoroutine = StartCoroutine(IEUpdate());
                break;

            case ChallengeState.Accepted:
            case ChallengeState.Completed:
                break;

            case ChallengeState.Failed:
            case ChallengeState.OpenedGoldenBox:
                return;
        }

        //show popup
        ShowPopupChallenge(source);

        if (EventState == ChallengeState.Accepted) {
            DiscoveryChallengeTracker.Track_ChallengeAcceptClick(source, ChallengeNumber,
                _config.numberOfStarToUnlockGoldenBox, totalStar, _config.numberOfChallengeSongs, totalUnlockSong,
                EventState, _cachedChallengeSongs, GetElapseTime());
        }
    }

    private int GetElapseTime() {
        return (int) ((DateTime.UtcNow - _lastActiveDateTime).TotalSeconds);
    }

    private void SaveLastActiveDateTime() {
        // ghi lại thời điểm active, tính thời điểm end event
        _lastActiveDateTime = DateTime.UtcNow;
        SetupTime();
        _userData.lastActiveDateTime = _lastActiveDateTime.ToString(CultureInfo.InvariantCulture);
    }

    public GameObject CheckShowToolTipHome(Transform parent) {
        if (PlayerPrefs.HasKey(KEY_TOOLTIP_HOME))
            return null;

        DoneTooltipHome();

        var prefab = Resources.Load<GameObject>("UI/Tooltip-DiscoveryTab");
        if (prefab == null) {
            return null;
        }

        var tooltip = Instantiate(prefab, parent);
        return tooltip;
    }

    public void DoneTooltipHome() {
        PlayerPrefs.SetInt(KEY_TOOLTIP_HOME, 1);
    }

    private void SongListOnOnUnlockSong(string acm_id_v3) {
        if (_config == null)
            return;
        if (_cachedChallengeSongs == null)
            return;

        foreach (var song in _cachedChallengeSongs) {
            if (song.acm_id_v3.Equals(acm_id_v3)) {
                totalUnlockSong = GetUnlockedSongs();
                DiscoveryChallengeTracker.Track_ChallengeUpdate(ChallengeNumber, _config.numberOfStarToUnlockGoldenBox,
                    totalStar, _config.numberOfChallengeSongs, totalUnlockSong - 1, totalUnlockSong, ActionType.unlock,
                    EventState, EventState, _cachedChallengeSongs, null, totalStar, GetElapseTime());
                break;
            }
        }
    }

    private PopupDiscoveryChallenge _popupDiscoveryChallenge;

    public void ShowPopupChallenge(Source source) {
        if (_popupDiscoveryChallenge == null) {
            GameObject popUp = Util.ShowPopUp(PopupName.DiscoveryChallenge);
            _popupDiscoveryChallenge = popUp.GetComponent<PopupDiscoveryChallenge>();
        } else {
            _popupDiscoveryChallenge.Open();
        }

        _popupDiscoveryChallenge.Show(_config);
        DiscoveryChallengeTracker.Track_ChallengeDetailImpression(source, ChallengeNumber,
            _config.numberOfStarToUnlockGoldenBox, totalStar, _config.numberOfChallengeSongs, totalUnlockSong,
            EventState, _cachedChallengeSongs, GetElapseTime());
    }

    public void ShowPopupTimeOut() {
        Util.ShowPopUp(PopupName.DiscoveryChallengeFailed);
    }

    public void GameComplete(Song song) {
        if (!IsEnable())
            return;
        if (_challengeSongPaths == null)
            return;
        if (!_challengeSongPaths.Contains(song.path))
            return;

        switch (EventState) {
            case ChallengeState.Accepted:
                // kiểm tra tổng stars của challenge để cập nhật challenge state
                CheckCompletedChallenge(song);
                break;

            default:
                return;
        }
    }

    public void CloseOpenedPopup() {
        if (_popupDiscoveryChallenge != null && _popupDiscoveryChallenge.gameObject.activeSelf) {
            PlaySongFromChallenge = false;
            _popupDiscoveryChallenge.Close();
        }
    }

    public void TrackPreviewBox() {
        DiscoveryChallengeTracker.Track_ChallengeGoldenBoxPreview(previewBoxNumber, ChallengeNumber,
            _config.numberOfStarToUnlockGoldenBox, totalStar, _config.numberOfChallengeSongs, totalUnlockSong,
            EventState, _cachedChallengeSongs, GetElapseTime());
        previewBoxNumber++;
    }
}