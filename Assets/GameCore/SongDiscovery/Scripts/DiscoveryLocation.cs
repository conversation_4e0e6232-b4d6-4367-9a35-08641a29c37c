public enum DiscoveryLocation
{
    /// <summary>
    /// user plays song from the Artist section in the Discover tab
    /// </summary>
    discover_artist,

    /// <summary>
    /// user plays song from the Album section in the Discover tab
    /// </summary>
    discover_album,

    /// <summary>
    /// user plays song from the Genre section in the Discover tab
    /// </summary>
    discover_genre,
    discover_recent_songs,
    discover_popular,
    discover_favourite,
    discover_your_challenge,
    discover_your_challenge_golden_box,
    discover_song_search,
    discover_artist_search,
    discover_others,
    discover_song_of_the_day,
    /// <summary>
    /// tab recommend (disable)
    /// </summary>
    discover_recommend,
    /// <summary>
    /// tab new updated song (disable)
    /// </summary>
    discover_newupdate,
    discover_hardcore
}
