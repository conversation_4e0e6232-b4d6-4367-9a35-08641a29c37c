using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Inwave;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.U2D;
using UnityEngine.UI;

//trungvt DataSearchScript
public class ListAlbumScript : TabScript {
    #region Fields

    [SerializeField] private SearchScript searchScript;

    [SerializeField] private StandardScrollerAdapter scrollerAlbum;
    [SerializeField] private RectTransform            prefRowAlbum;
    [SerializeField] private Button               btnBack;

    [Space] [SerializeField] private TabAlbumDetailScript tabAlbumDetailScript;
    [SerializeField] private Catalogs02SearchScript catalogs02SearchScript;

    //private
    private List<IData> _data;
    private List<Album>    _allAlbum;
    public double ScrollPosition {
        get {
            if (scrollerAlbum.IsInitialized) {
                return scrollerAlbum.GetNormalizedPosition();
            }
            return 0f;
        }
        set {
            if (scrollerAlbum.IsInitialized) {
                scrollerAlbum.SetNormalizedPosition(value);
            } else {
                wantedPosition = value;
            }
        }
    }
    private double wantedPosition = -1f;
    #endregion

    #region Unity Methods

    protected override void Awake() {
        base.Awake();

        btnBack.onClick.AddListener(btnBackOnClick);

        scrollerAlbum.Init(prefRowAlbum.transform as RectTransform);
    }
    private void OnEnable() {
        searchScript.ShowButtonBack(false);
        RowAlbumScript.OnItemClicked += OnClickAlbum;
        SoundManager.PlaySFX_PopupOpen();
    }

    public override void Start() {
        base.Start();
        LoadAlbum();
    }

    protected override void OnDisable() {
        RowAlbumScript.OnItemClicked -= OnClickAlbum;
        base.OnDisable();
    }

    #endregion

    #region Methods

    private void LoadAlbum() {
        _allAlbum = SongCards.instance.GetDataAlbums();

        _data = new List<IData>();

        int totalRowArtist = Mathf.CeilToInt(f: _allAlbum.Count * 1f / 3);

        for (int i = 0; i < totalRowArtist; i++) {
            int index1 = 3 * i;
            int index2 = index1 + 1;
            int index3 = index1 + 2;

            string name1 = index1 >= _allAlbum.Count ? null : _allAlbum[index1].name;
            string name2 = index2 >= _allAlbum.Count ? null : _allAlbum[index2].name;
            string name3 = index3 >= _allAlbum.Count ? null : _allAlbum[index3].name;

            string tag1 = index1 >= _allAlbum.Count ? null : _allAlbum[index1].tag;
            string tag2 = index2 >= _allAlbum.Count ? null : _allAlbum[index2].tag;
            string tag3 = index3 >= _allAlbum.Count ? null : _allAlbum[index3].tag;

            _data.Add(new RowAlbum(name1, tag1, name2,tag2, name3, tag3));
        }

        // scrollerAlbum.ReloadData();
        Configuration.instance.StartCoroutine(SetData());
    }
    private IEnumerator SetData() {
        while (!scrollerAlbum.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        scrollerAlbum.SetItems(_data);
        if (wantedPosition >= 0) {
            scrollerAlbum.SetNormalizedPosition(wantedPosition);
            wantedPosition = -1;
        }
    }
    #endregion

    private void OnClickAlbum(string album) {
        Debug.Log("[OnClickAlbum] " + album);
        SoundManager.PlayGameButton();
        AnalyticHelper.Album_Click(album);

        Hide(Direction.Left);
        tabAlbumDetailScript.ShowSongsOfAlbum(album, LastStateData.Locations.ListAlbum);
    }

    private int FindIndexOfAlbum(string albumName) {
        for (int index = 0; index < _allAlbum.Count; index++) {
            if (_allAlbum[index].name == albumName) {
                return index;
            }
        }

        return -1;
    }

    private void btnBackOnClick() {
        if (isUITabMoving)
        {
            return;
        }
        SoundManager.PlayGameButton();
        SoundManager.PlaySFX_PopupClose();
        Hide(Direction.Right);
        catalogs02SearchScript.Show(Direction.Left);
    }

    public void ScrollToTop(Action onDone, out bool isAlreadyOnTop) {
        float timeScroll = 0.2f;
        scrollerAlbum.SmoothScrollTo(0, timeScroll, scrollerAlbum.Parameters.ContentPadding.top, 0f);
        isAlreadyOnTop = ScrollPosition == 0;
        DOVirtual.DelayedCall(timeScroll, () => {
            onDone?.Invoke();
        });
    }

    #region New Transition

    protected override bool HasOverridedTransition => true;

    protected override void SetupTransitionTargets()
    {
        base.SetupTransitionTargets();
        transitionTargets.Add(transform);
        transitionTargets.Add(scrollerAlbum.transform);
        newTransitionDuration = 0.3f;
    }

    #endregion
}