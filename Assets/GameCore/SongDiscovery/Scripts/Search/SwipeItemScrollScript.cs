using UnityEngine;
using System;
using Com.TheFallenGames.OSA.Core;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class SwipeItemScrollScript : MonoBeh<PERSON>our, IBeginDrag<PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON>ler, IEndDragHandler {
    [Flags]
    private enum Directions {
        None = 0,
        Right = 1,
        Left = 2,
        Up = 4,
        Down = 8,
        Horizontal = Right | Left,
        Vertical = Up | Down,
        All = Right | Left | Up | Down
    }

    #region Fields

    [SerializeField] private Directions directions;
    [SerializeField] private StandardScrollerAdapter     scrollChild;
    
    public Action<PointerEventData> onBeginDrag;
    public Action<PointerEventData> onDrag;
    public Action<PointerEventData> onEndDrag;

    //private
    private bool isChildScroll;

    private const float ratio15DeGree = 3f;
    private const float ratio30DeGree = 1.732f;
    #endregion

    #region Unity Method

    public void OnBeginDrag(PointerEventData eventData) {
        Vector2 delta = eventData.delta;


        if (Mathf.Abs(delta.x) > ratio30DeGree * Mathf.Abs(delta.y)) {
            if (delta.x > 0) {
                DoSwipe(Directions.Right, eventData);
            } else {
                DoSwipe(Directions.Left, eventData);
            }
        } else {
            if (delta.y > 0) {
                DoSwipe(Directions.Up, eventData);
            } else {
                DoSwipe(Directions.Down, eventData);
            }
        }
    }

    public void OnEndDrag(PointerEventData eventData) {
        if (isChildScroll) {
            scrollChild.OnEndDrag(eventData);
        } else {
            scrollChild.enabled = true;
            onEndDrag?.Invoke(eventData);
        }
    }

    public void OnDrag(PointerEventData eventData) {
        if (isChildScroll) {
            scrollChild.OnDrag(eventData);
        } else {
            onDrag?.Invoke(eventData);
        }
    }

    #endregion

    private void DoSwipe(Directions directionType, PointerEventData eventData) {
        if ((directionType & directions) != 0) {
            isChildScroll = true;
            scrollChild.OnBeginDrag(eventData);
        } else {
            isChildScroll = false;
            scrollChild.enabled = false;
            onBeginDrag?.Invoke(eventData);
        }
    }
}