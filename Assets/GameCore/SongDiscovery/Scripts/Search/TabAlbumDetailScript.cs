using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using DG.Tweening;
using Inwave;
using Music.ACM;
using Music.ACM.Interfaces;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;
using Utils = Inwave.Utils;

public class TabAlbumDetailScript : TabScript {
    #region Fields

    [SerializeField] private SearchScript searchScript;

    [Header("Components")] [SerializeField]
    private StandardScrollerAdapter scrollerAlbumDetail;

    [SerializeField] private Text txtAlbumName;

    //[SerializeField] private Text txtTotalSong;
    [SerializeField] private Image      imgAvatarAlbum;
    [SerializeField] private Image      imgBgAlbum;
    [SerializeField] private Sprite     sprDefaultAlbum;
    [SerializeField] private Sprite     sprBgDefaultAlbum;
    [SerializeField] private GameObject objNoSongs;
    [SerializeField] private GameObject objNoConnection;

    //private
    [HideInInspector] public LastStateData.Locations lastLocation;
    private                  OptimizedCellView       _prefSongItem;
    private readonly         List<SongData>          _songDatasACM = new List<SongData>();

    private List<IData>  _data;
    private Action<Song> _onClickSong;
    private string       _albumName;

    private bool      _isLoadingMore;
    private int       _totalDataACM;
    private bool      _isLoadEnd;
    private string    _pageToken = "";
    private string    _lastToken = "";
    private Coroutine _cProcessOnSuccess;
    private Album     _album;
    private Coroutine _cSetImage;
    private Coroutine _cSetBgImage;

    public double ScrollPosition {
        get {
            if (scrollerAlbumDetail.IsInitialized) {
                return scrollerAlbumDetail.GetNormalizedPosition();
            }

            return 0f;
        }
        set {
            if (scrollerAlbumDetail.VisibleItemsCount != 0) {
                scrollerAlbumDetail.SetNormalizedPosition(value);
            } else {
                wantedPosition = value;
            }
        }
    }

    public  int    LastCountItem => scrollerAlbumDetail.Data.Count;
    private double wantedPosition = -1f;
    public  string AlbumName => _albumName;

    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();

        _prefSongItem = SongItem.GetSongItemPrefab();
        scrollerAlbumDetail.Init(_prefSongItem.transform as RectTransform);
    }

    private void OnEnable() {
        objNoConnection.SetActive(false);
        searchScript.ShowButtonBack(true);
        scrollerAlbumDetail.ScrollPositionChanged += ScrollerScrolled;
        scrollerAlbumDetail.OnItemVisible += OnScroll_OnItemVisible;
        if (scrollerAlbumDetail.IsInitialized) {
            scrollerAlbumDetail.ForceRebuildLayoutNow();
        }

        SoundManager.PlaySFX_PopupOpen();
    }

    protected override void OnDisable() {
        scrollerAlbumDetail.OnItemVisible -= OnScroll_OnItemVisible;
        scrollerAlbumDetail.ScrollPositionChanged -= ScrollerScrolled;
        base.OnDisable();
    }

    #endregion

    #region Optimized ScrollView

    public void SetScrollState(SearchScript search, int countItem, double position) {
        search.StartCoroutine(search.IESetScrollState(scrollerAlbumDetail, _data, countItem, position));
    }

    private IEnumerator SetData() {
        while (!scrollerAlbumDetail.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        scrollerAlbumDetail.SetItems(_data);
        while (scrollerAlbumDetail.VisibleItemsCount == 0) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        if (wantedPosition >= 0) {
            scrollerAlbumDetail.SetNormalizedPosition(wantedPosition);
            wantedPosition = -1;
        }
    }

    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_data.Count < item.ItemIndex) {
            return;
        }

        SongItem view = item.cellView as SongItem;
        if (view != null) {
            Song song = (Song)_data[item.ItemIndex];
            view.SetSong(song, SONG_PLAY_TYPE.discover_album, DiscoveryLocation.discover_album.ToString(),
                item.ItemIndex + 1, _onClickSong);
            view.SetTypePlaySongDetail(_albumName);
            view.ordering = song.ordering;

            view.HighlightCurrentSongIfNeed(SceneFader.instance.LastPlaySong);
        }
    }

    private void ScrollerScrolled(double scrollposition) {
        if (_data == null || _data.Count == 0) {
            return;
        }

        if (RemoteConfig.instance.ACMv4_IsLoadMore && !Configuration.instance.enableContentTool) {
            // start to load new page when there is only a certain number of song is left & is not loading
            if (scrollerAlbumDetail == null || scrollerAlbumDetail.VisibleItemsCount == 0) {
                return;
            }

            var lastItemViewed = scrollerAlbumDetail.GetItemViewsHolder(scrollerAlbumDetail.VisibleItemsCount - 1);
            if (lastItemViewed == null) {
                return;
            }

            bool isPrepareEnd = lastItemViewed.ItemIndex > _data.Count - ACMSDKv4.LOAD_WHEN_DATA_LEFT;
            Directions directions = scrollerAlbumDetail.Velocity.y >= 0 ? Directions.Up : Directions.Down;
            if (isPrepareEnd && directions == Directions.Up && !_isLoadEnd) {
                LoadMoreSongs();
            }
        }
    }

    #endregion

    public void ShowSongsOfAlbum(string albumName, LastStateData.Locations location, Action<Song> onClickSong = null,
        bool isAnimation = true) {
        bool isNewAlbum = _albumName != albumName;
        _albumName = albumName;
        lastLocation = location;
        _onClickSong = onClickSong;

        imgAvatarAlbum.sprite = sprDefaultAlbum; //set default
        imgBgAlbum.sprite = sprBgDefaultAlbum;
        if (_cSetImage != null) {
            StopCoroutine(_cSetImage);
        }

        _cSetImage = HomeManager.instance.StartCoroutine(IESetImage(albumName));

        if (_cSetBgImage != null) {
            StopCoroutine(_cSetBgImage);
        }

        _cSetBgImage = HomeManager.instance.StartCoroutine(IESetBGImage(albumName));

        if (isNewAlbum) {
            HomeManager.instance.StartCoroutine(IELoadSongs(albumName));
        }

        Show(isAnimation ? Direction.Right : Direction.None);
    }

    public void ShowSongsOfAlbum(string albumName, string acm_id_v3) {
        ShowSongsOfAlbum(albumName, LastStateData.Locations.ListAlbum, null, false);
        if (!string.IsNullOrEmpty(acm_id_v3)) {
            StartCoroutine(IEScrollToSong(acm_id_v3));
        }
    }

    private IEnumerator IEScrollToSong(string songID) {
        while (scrollerAlbumDetail.VisibleItemsCount == 0) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        int itemIndex = -1;
        int temp = 0;
        foreach (var item in _data) {
            if (item is Song song && song.acm_id_v3 == songID) {
                itemIndex = temp;
                break;
            }

            temp++;
        }

        if (itemIndex != -1) {
            scrollerAlbumDetail.ScrollTo(itemIndex);
        }
    }

    private IEnumerator IELoadSongs(string albumName) {
        while (!SongCards.instance.IsInitedAlbums()) {
            yield return null;
        }

        _album = SongCards.instance.GetAlbum(albumName);
        _data = new List<IData>();

        txtAlbumName.text = albumName;
        UpdateUITotalSong();
        Configuration.instance.StartCoroutine(SetData());

        //load more
        ResetDownloadACM();

        if (RemoteConfig.instance.ACMv4_IsLoadMore && !Configuration.instance.enableContentTool &&
            _data.Count < ACMSDKv4.LOAD_WHEN_DATA_LEFT) {
            if (_cProcessOnSuccess != null) {
                StopCoroutine(_cProcessOnSuccess);
            }

            LoadMoreSongs();
        }
    }

    private void UpdateUITotalSong() {
        // string xSongs = LocalizationManager.instance.GetLocalizedValue("X_SONGS");
        // txtTotalSong.text = string.Format(xSongs, _data.Count);
        if (_data.Count == 0 && !Utils.isInternetReachable) {
            objNoConnection.SetActive(true);
            objNoSongs.SetActive(false);
        } else if (_data.Count == 0) {
            objNoSongs.SetActive(true);
            objNoConnection.SetActive(false);
        } else {
            objNoConnection.SetActive(false);
            objNoSongs.SetActive(false);
        }
    }

    /// <summary>
    /// ScrollToTop
    /// </summary>
    /// <param name="onDone"></param>
    /// <param name="isAlreadyOnTop"></param>
    public void ScrollToTop(Action onDone, out bool isAlreadyOnTop) {
        float timeScroll = 0.2f;
        scrollerAlbumDetail.SmoothScrollTo(0, timeScroll, scrollerAlbumDetail.Parameters.ContentPadding.top, 0f);
        isAlreadyOnTop = ScrollPosition == 0;
        DOVirtual.DelayedCall(timeScroll, () => { onDone?.Invoke(); });
    }

    #region Load More Song

    private void ResetDownloadACM() {
        _totalDataACM = 0;
        _songDatasACM.Clear();
        _isLoadingMore = false;
        _isLoadEnd = false;
        _pageToken = "";
        _lastToken = "";
    }

    private void LoadMoreSongs() {
        if (_isLoadingMore || (_pageToken == _lastToken && !string.IsNullOrEmpty(_lastToken))) {
            return;
        }

        _isLoadingMore = true;
        string lastToken = _lastToken;
        _lastToken = _pageToken;

        if (_album != null && !string.IsNullOrEmpty(_album.featured_playlist_id)) {
            ISearchAction action = ACMSDK.Instance.SearchPlaylist(_album.featured_playlist_id).IsOnlyPlaylist(true);
            OnStartSearch(action, lastToken);
        } else {
            Logger.LogWarning("_album = NULL || _album.featured_playlist_id = NULL");
        }
    }

    private void OnStartSearch(ISearchAction action, string lastToken) {
        action?.PageToken(_pageToken).ExtraFields("songParams,speed") //
            .OnError((string error) => {
                _lastToken = lastToken; //Restore token
                Logger.LogError(error);
                _isLoadingMore = false;
            }).OnSuccess((SearchSongResponse response) => {
                _cProcessOnSuccess = Configuration.instance.StartCoroutine(IeProcessSearchSuccess(response));
                //
            }).Run();
    }

    private IEnumerator IeProcessSearchSuccess(SearchSongResponse response) {
        // if (!this.gameObject.activeSelf) {
        //     yield break;
        // }
        
        yield return null;

        if (scrollerAlbumDetail == null) {
            yield break;
        }

        SongData[] songDatas = null;
        string nextToken = string.Empty;
        if (response != null) {
            _totalDataACM = response.total;
            songDatas = response.data;
            if (response.paging != null) {
                nextToken = response.paging.cursors.after;
            }
        }

        if (songDatas != null && songDatas.Length > 0) {
            _songDatasACM.AddRange(songDatas);
            _isLoadEnd = _songDatasACM.Count == _totalDataACM;

            int countBefore = _data.Count;
            for (int i = 0; i < songDatas.Length; i++) {
                SongData songData = songDatas[i];
                Song song = SongManager.instance.GetSongByAcmId(songData.id) ?? ACMSDKv4.ParseSong(songData);
                if (song != null && !SongManager.instance.IsHideSong(song.acm_id_v3)) {
                    song.isMainSong = false;
                    AddSong(song, _data);
                }
            }

            int countNewData = _data.Count - countBefore;
            if (countNewData > 0) {
                Configuration.instance.StartCoroutine(SetData());
                UpdateUITotalSong();
                if (countBefore == 0 && RemoteConfig.instance.PreviewMusic_Improve_IsEnable) {
                    searchScript.SetDelayAutoPreviewMusic(_data[0] as Song, lastLocation.ToString());
                }
            }

            //Logger.Log($"[OnACMSearch] {songDatas[0].song_name.name}");
            //Logger.Log("[OnACMSearch] " + songDatas.Length + " / " + _songDatasACM.Count + " / " + _totalDataACM);
        } else {
            _isLoadEnd = _totalDataACM == 0;
        }

        yield return YieldPool.GetWaitForSeconds(0.1f);

        _pageToken = nextToken;
        _isLoadingMore = false;

        yield return YieldPool.GetWaitForSeconds(0.4f);

        bool isPrepareEnd = scrollerAlbumDetail != null && scrollerAlbumDetail.VisibleItemsCount != 0 &&
                            scrollerAlbumDetail.GetItemViewsHolder(scrollerAlbumDetail.VisibleItemsCount - 1)
                                .ItemIndex > _data.Count - ACMSDKv4.LOAD_WHEN_DATA_LEFT;
        if (isPrepareEnd && !_isLoadEnd) {
            LoadMoreSongs();
        }
    }

    private void AddSong(Song song, List<IData> data) {
        //exist
        foreach (IData song1 in data) {
            Song songItemRaw = (Song)song1;
            if (songItemRaw.acm_id_v3 == song.acm_id_v3) {
                return;
            }

            if (songItemRaw.path == song.path) {
                return;
            }
        }

        //not
        data.Add(song);
    }

    #endregion

    private IEnumerator IESetImage(string albumName) {
        Task<Sprite> icon = SongCards.instance.GetIconAlbum(albumName);
        while (!icon.IsCompleted) {
            yield return null;
        }

        if (icon.Result == null) {
            icon = SongCards.instance.GetIconAlbum("default");
            while (!icon.IsCompleted) {
                yield return null;
            }
        }

        if (icon.Result != null) {
            imgAvatarAlbum.sprite = icon.Result;
        }
    }

    private IEnumerator IESetBGImage(string albumName) {
        Task<Sprite> icon = SongCards.instance.GetBGAlbum(albumName);
        while (!icon.IsCompleted) {
            yield return null;
        }

        if (icon.Result == null) {
            icon = SongCards.instance.GetBGAlbum("default");
            while (!icon.IsCompleted) {
                yield return null;
            }
        }

        if (icon.Result != null) {
            imgBgAlbum.sprite = icon.Result;
        }
    }

    #region New Transition

    protected override bool HasOverridedTransition => true;

    protected override void SetupTransitionTargets()
    {
        base.SetupTransitionTargets();
        transitionTargets.Add(transform);
        transitionTargets.Add(scrollerAlbumDetail.transform);
        newTransitionDuration = 0.3f;
    }

    #endregion
}