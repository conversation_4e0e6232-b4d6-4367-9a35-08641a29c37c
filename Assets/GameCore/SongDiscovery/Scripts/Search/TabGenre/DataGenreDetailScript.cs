using System;
using UnityEngine;

public class DataGenreDetailScript : MonoBehaviour {
    #region Fields

    [SerializeField]
    private UIAnimation uiAnimation;

    private RectTransform container;

    #endregion

    #region Unity Methods

    private void Start() {
        if (container == null) {
            container = uiAnimation.transform.Find("Container").GetComponent<RectTransform>();
            uiAnimation.SetParentUIList(container);
        }

        StartCoroutine(uiAnimation.DoStartUIAnimation(true));
    }

    private void OnEnable() {
        if (container != null) {
            StartCoroutine(uiAnimation.DoStartUIAnimation(true));
        }
    }

    #endregion

    #region Methods

    #endregion
}