using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using TileHop.Cores.Pooling;
using TileHop.LiveEvent;
using UnityEngine;
using UnityEngine.UI;

public class SongItemSmall : OptimizedCellView {
    public static List<String> visibledSong = new List<string>();

    [HideInInspector] public string         songID = "";
    [HideInInspector] public Song           song;
    [HideInInspector] public string         location;
    [HideInInspector] public SONGTYPE       type = SONGTYPE.LOCK;
    [HideInInspector] public SONG_PLAY_TYPE song_play_type;
    [HideInInspector] public int            ordering;

    [SerializeField] private Button btnClick;
    [SerializeField] public  Text   nameText;
    [SerializeField] private Text   txtArtist;
    [SerializeField] private Text   priceText;

    [SerializeField] private Image      buyButton;
    [SerializeField] private Image      iconCurrency;
    [SerializeField] private Sprite     sprtDiamond;
    [SerializeField] private GameObject videoButton;
    [SerializeField] private GameObject btnPlayVip;

    [SerializeField] private Image imgIconSong;

    [SerializeField] private Sprite sprDiamondEnalbe;
    [SerializeField] private Sprite sprDiamondDisable;

    [SerializeField] protected GameObject groupStar;

    [SerializeField]
    protected Sprite sprStarOn;

    [SerializeField]
    protected Sprite sprStarOff;

    [SerializeField]
    protected Image imgStar01;

    [SerializeField]
    protected Image imgStar02;

    [SerializeField]
    protected Image imgStar03;

    [SerializeField] private ScrollerText scrollerSongName;
    [SerializeField] private ScrollerText scrollerArtistName;

    public Action<Song> onClickPlay;
    public Action       onClickItemFavoriteTip;
    public Action       onClickItemUpload;

    //private
    private int           price = 0;
    private RectTransform rect;
    private Coroutine     visibleCheckIE;
    private Camera        cameraMain;

    #region Unity method

    void Awake() {
        rect = GetComponent<RectTransform>();
        cameraMain = Camera.main;

        btnClick.onClick.AddListener(ItemClick);
        LocalizationManager.instance.UpdateFont(txtArtist);
        LocalizationManager.instance.UpdateFont(nameText);
        LocalizationManager.instance.UpdateFont(priceText);
    }

    private void OnEnable() {
        Configuration.OnChangeDiamond += Configuration_OnChangeDiamond;
        LiveEventManager.OnChangeToken += LiveEventManagerOnOnChangeToken;
    }

    private void OnDisable() {
        Configuration.OnChangeDiamond -= Configuration_OnChangeDiamond;
        LiveEventManager.OnChangeToken -= LiveEventManagerOnOnChangeToken;
    }

    private void OnDestroy() {
        visibledSong.Clear();
    }

    #endregion

    private void ItemClick() {
        if (UIController.ui != null && UIController.ui.gameover.gameObject.activeInHierarchy) {
            UIController.ui.gameover.SongItem_ResultClick(song, buyButton.transform.RootLocalPos(), null);
        } else {
            song.ordering = ordering;
            switch (type) {
                case SONGTYPE.USER_PROGRESSION:
                case SONGTYPE.COUNTDOWN:
                    break;

                case SONGTYPE.VIDEO:
                    SoundManager.PlayGameButton();
                    SongLocationTracker.SetSongPlayType(song_play_type);
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, song, location: this.location, true,
                        OnRewardVideoCompleted);
                    break;

                case SONGTYPE.CHALLENGE:
                    SoundManager.PlayGameButton();
                    SongLocationTracker.SetSongPlayType(song_play_type);
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.CHALLENGE, song, location: this.location, true,
                        OnRewardVideoCompleted);
                    break;

                case SONGTYPE.OPEN:
#if UNITY_ANDROID
                    if (SongManager.instance.IsLocalSong(songID) && !File.Exists(song.GetLocalMp3Path()) &&
                        string.IsNullOrEmpty(song.key)) {
                        Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("MISSING_SONG_FILE") +
                                         "\n<color='#00E031'>" + SongManager.instance.userLocalSongs[songID].path +
                                         "</color>");
                        break;
                    }
#endif
                    PlaySong();
                    break;

                case SONGTYPE.PAY:
                    Configuration.instance.BuyProduct(IAPDefinitionId.song_st, string.Empty, (success, message) => {
                        if (success) {
                            SongList.ShopPaySingleCompleted(song);
                        }
                    });
                    break;

                case SONGTYPE.ALBUM:
                    Configuration.instance.BuyProduct(IAPDefinitionId.package_st, string.Empty, (success, message) => {
                        if (success) {
                            SongList.ShopPayPackageCompleted();
                        }
                    });
                    break;

                case SONGTYPE.BUTTON:
                    AnalyticHelper.Button_Click(BUTTON_NAME.PlayMySong);
                    onClickItemUpload?.Invoke();
                    break;

                case SONGTYPE.VIP:
                    if (SubscriptionController.IsSubscriptionVip()) {
                        PlaySong();
                    } else {
                        SubscriptionController.ShowSubscription(SUBSCRIPTION_SOURCE.Song.ToString(), userOpen: true);
                    }

                    break;

                case SONGTYPE.FAVORITE_TIP:
                    onClickItemFavoriteTip?.Invoke();

                    break;

                case SONGTYPE.EVENT:
                    if (LiveEventManager.instance.UnlockSong(LiveEventManager.IdCurrentEvent, song.acm_id_v3)) {
                        SongList.OpenSong(song, 0, SongUnlockType.token);
                        buyButton.gameObject.SetActive(false);
                        UIOverlay.instance.ShowVFXSubCustom(iconCurrency.sprite, price,
                            buyButton.transform.RootLocalPos(), new Vector2(-25, 0), PlaySong);
                    } else {
                        var popup = Util.ShowPopUp(PopupName.LiveEventNotEnoughToken);
                        if (popup != null) {
                            if (popup.TryGetComponent(out UINotEnoughToken notEnoughToken)) {
                                notEnoughToken.Show(TrackingLocation.discovery_screen.ToString());
                            } else {
                                Logger.EditorLogError("Not found component!");
                            }
                        } else {
                            Logger.EditorLogError("Not found popup!");
                        }
                    }

                    break;

                case SONGTYPE.LOCK:
                    song.TryToUnLockSongByGem((result) => {
                        if (result) {
                            buyButton.gameObject.SetActive(false);
                            UIOverlay.instance.ShowVFXSubDiamond(price, buyButton.transform.RootLocalPos(),
                                new Vector2(-25, 0), PlaySong);
                        }
                    });
                    break;

                default:
                    Logger.LogWarning($"SongItem - unknow handle this type: {type}");
                    break;
            }
        }

        onClickPlay?.Invoke(song); //put end of method
    }

    private void PlaySong() {
        SongLocationTracker.SetSongPlayType(song_play_type);
        Util.GoToGamePlay(song, location: this.location, isSongClick: true);
    }

    public void Reset() {
        buyButton.gameObject.SetActive(false);
        videoButton.SetActive(false);
        btnPlayVip.SetActive(false);
        nameText.gameObject.SetActive(true);

        //imgBg.sprite = sprBgDefault;
        imgStar01.transform.parent.gameObject.SetActive(false);
    }

    public void SetSong(Song item, string locationSong, Action<Song> onClick) {
        SetSong(item, locationSong);
        onClickPlay = onClick;
    }

    public void Reload() {
        SetSong(song, this.location);
    }

    public virtual void SetSong(Song item, string locationSong) {
        Reset();
        SONGTYPE songType = item.savedType;
        type = songType;
        songID = item.path;
        nameText.text = item.name;
        scrollerSongName.Refresh(true);

        if (string.IsNullOrEmpty(item.artist)) {
            txtArtist.gameObject.SetActive(false);
        } else {
            txtArtist.gameObject.SetActive(true);
            txtArtist.text = item.artist;

            scrollerArtistName.Refresh(true);
        }

        song = item;
        this.location = locationSong;
        SetCheckVisible();
        item.uploadedBy = null;
        SetSongCard();

        bool isOpened = (song.savedType == SONGTYPE.VIP && SubscriptionController.IsSubscriptionVip()) ||
                        song.savedType == SONGTYPE.OPEN;
        switch (songType) {
            case SONGTYPE.VIDEO:
                item.desc = LocalizationManager.instance.GetLocalizedValue("WATCH_A_VIDEO");
                videoButton.SetActive(true);
                //imgBg.sprite = sprBgAds;

                break;

            case SONGTYPE.LOCK:
                price = item.diamonds;
                priceText.text = price.ToString();
                item.desc = LocalizationManager.instance.GetLocalizedValue("DESC_TO_BUY");
                iconCurrency.sprite = sprtDiamond;
                buyButton.gameObject.SetActive(true);
                buyButton.sprite = Configuration.instance.GetDiamonds() >= price ? sprDiamondEnalbe : sprDiamondDisable;
                //imgBg.sprite = sprBgVip;
                break;

            case SONGTYPE.EVENT:
                var eventItem = LiveEventManager.instance.GetLiveEvent(LiveEventManager.IdCurrentEvent);
                price = eventItem.detailData.GetSongPrice();
                priceText.text = price.ToString();
                item.desc = LocalizationManager.instance.GetLocalizedValue("DESC_TO_BUY");
                iconCurrency.sprite = eventItem.eventConfig.IconToken;
                buyButton.gameObject.SetActive(true);
                buyButton.sprite = eventItem.progress.TokenAmount >= price ? sprDiamondEnalbe : sprDiamondDisable;
                break;

            case SONGTYPE.VIP:
            case SONGTYPE.OPEN:

                if (songType == SONGTYPE.VIP) {
                    item.desc = song.label;

                    if (isOpened) {
                        //imgBg.sprite = sprBgVip;
                    } else {
                        btnPlayVip.SetActive(true);
                    }
                } else {
                    item.desc = null;
                }

                UpdateStar(isOpened);
                break;
        }

        if (!string.IsNullOrEmpty(item.uploadedBy)) {
            item.desc = LocalizationManager.instance.GetLocalizedValue("UPLOADED_BY_X").Replace("{0}", item.uploadedBy);
        }
    }

    protected virtual void UpdateStar(bool isOpened) {
        int stars = Configuration.GetBestStars(songID);
        if (isOpened && song.IsPlayed() && stars >= 1) {
            groupStar.SetActive(true);
            imgStar01.sprite = stars >= 1 ? sprStarOn : sprStarOff;
            imgStar02.sprite = stars >= 2 ? sprStarOn : sprStarOff;
            imgStar03.sprite = stars >= 3 ? sprStarOn : sprStarOff;
        } else {
            groupStar.SetActive(false);
        }
    }

    private async void SetSongCard() {
        (Sprite, bool) data = await SongCards.instance.GetSquareIconSong(song);
        Sprite card = data.Item1;
        bool isIconTheme = data.Item2;

        if (imgIconSong != null && card != null) {
            imgIconSong.sprite = card;
            if (isIconTheme) {
                imgIconSong.rectTransform.sizeDelta = new Vector2(20, 20);
            }
        }
    }

    public void SetCheckVisible() {
        if (visibleCheckIE != null) {
            StopCoroutine(visibleCheckIE);
        }

        if (song != null && SongManager.instance.songs.ContainsKey(song.path) && gameObject.activeInHierarchy) {
            visibleCheckIE = StartCoroutine(IECheckVisible());
        }
    }

    private IEnumerator IECheckVisible() {
        yield return YieldPool.GetWaitForSeconds(RemoteConfig.instance.Song_Impression_Time);

        if (!visibledSong.Contains(song.path)) {
            if ((GameController.CheckInstanced() && rect.IsFullyVisibleFrom(cameraMain)) ||
                (!GameController.CheckInstanced() && rect.IsFullyVisibleOnScreen())) {
                AnalyticHelper.LogSong(SONG_STATUS.song_impression, song, this.location, null);
                visibledSong.Add(song.path);
            } else {
                SetCheckVisible();
            }
        }
    }

    private void OnRewardVideoCompleted(bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        SongList.OpenSong(song, 0, SongUnlockType.@default);
        PlaySong();
    }

    public override void SetData(IData _data) {
        Reset();
    }

    private void Configuration_OnChangeDiamond(int amount) {
        if (song.savedType != SONGTYPE.LOCK)
            return;

        if (buyButton.gameObject.activeSelf) {
            buyButton.sprite = amount >= price ? sprDiamondEnalbe : sprDiamondDisable;
        }
    }

    private void LiveEventManagerOnOnChangeToken(int arg1, int amount) {
        if (song.savedType != SONGTYPE.EVENT)
            return;

        if (buyButton.gameObject.activeSelf) {
            buyButton.sprite = amount >= price ? sprDiamondEnalbe : sprDiamondDisable;
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public void SetShowOnly(Action onClick) {
        buyButton.gameObject.SetActive(false);
        videoButton.SetActive(false);
        btnPlayVip.SetActive(false);
        btnClick.onClick.RemoveAllListeners();
        btnClick.onClick.AddListener(() => onClick?.Invoke());
        onClickPlay = null;
    }
}