{"skeleton": {"hash": "Vi4I6q3/B4o", "spine": "4.0.62", "x": -313.48, "y": 256.9, "width": 952.53, "height": 880.35, "images": "./Image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bird", "parent": "root", "x": 182.81, "y": 845.57}, {"name": "bone_l", "parent": "bird", "x": 0.19, "y": -10.27}, {"name": "bone2", "parent": "bone_l", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone3", "parent": "bone2", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone4", "parent": "bone3", "length": 15.06, "rotation": -4.94, "x": 15.6}, {"name": "bone5_r", "parent": "bird", "x": 0.19, "y": -10.27, "scaleX": -1}, {"name": "bone6", "parent": "bone5_r", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone7", "parent": "bone6", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone8", "parent": "bone7", "length": 15.06, "rotation": -4.94, "x": 15.6}, {"name": "Asset 3", "parent": "root", "rotation": 2.63, "x": 284.65, "y": 451.71, "scaleY": 0.9282}, {"name": "smoke_all", "parent": "root", "x": -6.39, "y": 585.38, "color": "57ff00ff"}, {"name": "smoke_1", "parent": "smoke_all", "x": 1612.76, "y": -59.61, "color": "eaff00ff"}, {"name": "Asset 10", "parent": "smoke_all", "x": 1354.59, "y": -208.71, "color": "eaff00ff"}, {"name": "Asset 9", "parent": "smoke_all", "x": 1626.26, "y": -102.16, "color": "eaff00ff"}, {"name": "mask", "parent": "root", "y": -1149.97}, {"name": "cloud", "parent": "root", "x": -45.57, "y": 789.01}, {"name": "Asset 7", "parent": "cloud", "x": 1195.19, "y": 244.59}, {"name": "Asset 8", "parent": "cloud", "x": -1209.64, "y": 136.69}, {"name": "Asset 11", "parent": "cloud", "x": 1195.19, "y": 2.21}, {"name": "bird2", "parent": "root", "x": 246.02, "y": 885.21, "scaleX": 1.23, "scaleY": 1.23}, {"name": "bone_l2", "parent": "bird2", "x": 0.19, "y": -10.27}, {"name": "bone5", "parent": "bone_l2", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone9", "parent": "bone5", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone10", "parent": "bone9", "length": 15.06, "rotation": -4.94, "x": 15.6}, {"name": "bone5_r2", "parent": "bird2", "x": 0.19, "y": -10.27, "scaleX": -1}, {"name": "bone11", "parent": "bone5_r2", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone12", "parent": "bone11", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone13", "parent": "bone12", "length": 15.06, "rotation": -4.94, "x": 15.6}, {"name": "bird3", "parent": "root", "x": 281.37, "y": 816.64, "scaleX": 0.5209, "scaleY": 0.5209}, {"name": "bone_l3", "parent": "bird3", "x": 0.19, "y": -10.27}, {"name": "bone14", "parent": "bone_l3", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone15", "parent": "bone14", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone16", "parent": "bone15", "length": 15.06, "rotation": -4.94, "x": 15.6}, {"name": "bone5_r3", "parent": "bird3", "x": 0.19, "y": -10.27, "scaleX": -1}, {"name": "bone17", "parent": "bone5_r3", "length": 12.13, "rotation": 49.83, "x": 1.75, "y": 2.57}, {"name": "bone18", "parent": "bone17", "length": 15.6, "rotation": -42.63, "x": 12.06, "y": -0.05}, {"name": "bone19", "parent": "bone18", "length": 15.06, "rotation": -4.94, "x": 15.6}], "slots": [{"name": "mask", "bone": "mask", "attachment": "mask"}, {"name": "Asset 6", "bone": "Asset 8", "attachment": "Asset 6"}, {"name": "Asset 1", "bone": "root", "color": "ffffffe8", "attachment": "Asset 1"}, {"name": "Asset 7", "bone": "Asset 7", "attachment": "Asset 7"}, {"name": "Asset 13", "bone": "Asset 11", "attachment": "Asset 7"}, {"name": "Asset 8", "bone": "smoke_1", "attachment": "Asset 8"}, {"name": "Asset 9", "bone": "Asset 9", "attachment": "Asset 9", "blend": "additive"}, {"name": "Asset 3", "bone": "Asset 3", "attachment": "Asset 3"}, {"name": "Asset 10", "bone": "Asset 10", "attachment": "Asset 10"}, {"name": "Asset 11", "bone": "bone_l", "attachment": "Asset 11"}, {"name": "Asset 17", "bone": "bone_l2", "attachment": "Asset 11"}, {"name": "Asset 18", "bone": "bone_l3", "attachment": "Asset 11"}], "skins": [{"name": "default", "attachments": {"Asset 1": {"Asset 1": {"x": -105.48, "y": 933.71, "width": 416, "height": 416}}, "Asset 3": {"Asset 3": {"x": -52.74, "y": 76.83, "width": 259, "height": 163}}, "Asset 6": {"Asset 6": {"x": -2.76, "y": -4.23, "width": 1221, "height": 42}}, "Asset 7": {"Asset 7": {"x": 76.25, "y": 16.06, "width": 1173, "height": 53}}, "Asset 8": {"Asset 8": {"x": -35.83, "y": 20.48, "width": 1935, "height": 167}}, "Asset 9": {"Asset 9": {"x": -125.06, "y": -3.25, "width": 1338, "height": 163}}, "Asset 10": {"Asset 10": {"x": -183.33, "y": 20.73, "width": 1338, "height": 163}}, "Asset 11": {"Asset 11": {"type": "mesh", "uvs": [0, 0, 0.21453, 0, 0.44781, 0, 0.50275, 0.45364, 0.5752, 7e-05, 0.80131, 0, 1, 0, 1, 0.09393, 0.81981, 0.22203, 0.65191, 0.44392, 0.50327, 0.99998, 0.36593, 0.43551, 0.19839, 0.26666, 0, 0.06671], "triangles": [1, 12, 0, 12, 13, 0, 12, 1, 11, 11, 3, 10, 11, 2, 3, 11, 1, 2, 9, 5, 8, 7, 8, 6, 8, 5, 6, 10, 3, 9, 3, 4, 9, 9, 4, 5], "vertices": [1, 9, 17.8, 0.42, 1, 2, 8, 15.27, 1.13, 0.73668, 9, -0.42, 1.1, 0.26332, 2, 7, 11.24, 5.56, 0.98641, 8, -4.41, 3.57, 0.01359, 2, 3, 1.86, 3.99, 0.46556, 7, 1.99, 3.84, 0.53444, 2, 3, 12.08, 4.53, 0.90524, 4, -3.09, 3.38, 0.09476, 2, 4, 15.97, 0.93, 0.3278, 5, 0.29, 0.96, 0.6722, 1, 5, 17.17, 0.25, 1, 1, 5, 17.1, -1.44, 1, 2, 4, 17.02, -3.24, 0.17701, 5, 1.7, -3.1, 0.82299, 2, 3, 10.15, -5.61, 0.44794, 4, 2.36, -5.38, 0.55206, 2, 3, -5.64, -2.37, 0.51724, 7, -5.54, -2.49, 0.48276, 2, 7, 9.76, -4.82, 0.52941, 8, 1.54, -5.06, 0.47059, 2, 8, 16.04, -3.8, 0.404, 9, 0.77, -3.75, 0.596, 1, 9, 17.76, -0.78, 1], "hull": 14, "edges": [0, 26, 20, 22, 8, 6, 6, 4, 8, 18, 18, 16, 8, 10, 10, 12, 16, 10, 12, 14, 16, 14, 6, 20, 18, 20, 22, 4, 22, 24, 24, 26, 0, 2, 2, 4, 24, 2], "width": 85, "height": 18}}, "Asset 13": {"Asset 7": {"x": 76.25, "y": 16.06, "width": 1173, "height": 53}}, "Asset 17": {"Asset 11": {"type": "mesh", "uvs": [0, 0, 0.21453, 0, 0.44781, 0, 0.50275, 0.45364, 0.5752, 7e-05, 0.80131, 0, 1, 0, 1, 0.09393, 0.81981, 0.22203, 0.65191, 0.44392, 0.50327, 0.99998, 0.36593, 0.43551, 0.19839, 0.26666, 0, 0.06671], "triangles": [1, 12, 0, 12, 13, 0, 12, 1, 11, 11, 3, 10, 11, 2, 3, 11, 1, 2, 9, 5, 8, 7, 8, 6, 8, 5, 6, 10, 3, 9, 3, 4, 9, 9, 4, 5], "vertices": [1, 28, 17.8, 0.42, 1, 2, 27, 15.27, 1.13, 0.73668, 28, -0.42, 1.1, 0.26332, 2, 26, 11.24, 5.56, 0.98641, 27, -4.41, 3.57, 0.01359, 2, 22, 1.86, 3.99, 0.46556, 26, 1.99, 3.84, 0.53444, 2, 22, 12.08, 4.53, 0.90524, 23, -3.09, 3.38, 0.09476, 2, 23, 15.97, 0.93, 0.3278, 24, 0.29, 0.96, 0.6722, 1, 24, 17.17, 0.25, 1, 1, 24, 17.1, -1.44, 1, 2, 23, 17.02, -3.24, 0.17701, 24, 1.7, -3.1, 0.82299, 2, 22, 10.15, -5.61, 0.44794, 23, 2.36, -5.38, 0.55206, 2, 22, -5.64, -2.37, 0.51724, 26, -5.54, -2.49, 0.48276, 2, 26, 9.76, -4.82, 0.52941, 27, 1.54, -5.06, 0.47059, 2, 27, 16.04, -3.8, 0.404, 28, 0.77, -3.75, 0.596, 1, 28, 17.76, -0.78, 1], "hull": 14, "edges": [0, 26, 20, 22, 8, 6, 6, 4, 8, 18, 18, 16, 8, 10, 10, 12, 16, 10, 12, 14, 16, 14, 6, 20, 18, 20, 22, 4, 22, 24, 24, 26, 0, 2, 2, 4, 24, 2], "width": 85, "height": 18}}, "Asset 18": {"Asset 11": {"type": "mesh", "uvs": [0, 0, 0.21453, 0, 0.44781, 0, 0.50275, 0.45364, 0.5752, 7e-05, 0.80131, 0, 1, 0, 1, 0.09393, 0.81981, 0.22203, 0.65191, 0.44392, 0.50327, 0.99998, 0.36593, 0.43551, 0.19839, 0.26666, 0, 0.06671], "triangles": [1, 12, 0, 12, 13, 0, 12, 1, 11, 11, 3, 10, 11, 2, 3, 11, 1, 2, 9, 5, 8, 7, 8, 6, 8, 5, 6, 10, 3, 9, 3, 4, 9, 9, 4, 5], "vertices": [1, 37, 17.8, 0.42, 1, 2, 36, 15.27, 1.13, 0.73668, 37, -0.42, 1.1, 0.26332, 2, 35, 11.24, 5.56, 0.98641, 36, -4.41, 3.57, 0.01359, 2, 31, 1.86, 3.99, 0.46556, 35, 1.99, 3.84, 0.53444, 2, 31, 12.08, 4.53, 0.90524, 32, -3.09, 3.38, 0.09476, 2, 32, 15.97, 0.93, 0.3278, 33, 0.29, 0.96, 0.6722, 1, 33, 17.17, 0.25, 1, 1, 33, 17.1, -1.44, 1, 2, 32, 17.02, -3.24, 0.17701, 33, 1.7, -3.1, 0.82299, 2, 31, 10.15, -5.61, 0.44794, 32, 2.36, -5.38, 0.55206, 2, 31, -5.64, -2.37, 0.51724, 35, -5.54, -2.49, 0.48276, 2, 35, 9.76, -4.82, 0.52941, 36, 1.54, -5.06, 0.47059, 2, 36, 16.04, -3.8, 0.404, 37, 0.77, -3.75, 0.596, 1, 37, 17.76, -0.78, 1], "hull": 14, "edges": [0, 26, 20, 22, 8, 6, 6, 4, 8, 18, 18, 16, 8, 10, 10, 12, 16, 10, 12, 14, 16, 14, 6, 20, 18, 20, 22, 4, 22, 24, 24, 26, 0, 2, 2, 4, 24, 2], "width": 85, "height": 18}}, "mask": {"mask": {"type": "clipping", "end": "mask", "vertexCount": 4, "vertices": [-641.19, 15.28, 638.64, 15.04, 639.16, 2287.31, -641.97, 2287.1], "color": "ce3a3aff"}}}}], "animations": {"BGR_11_green_anim": {"slots": {"Asset 7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 7.8667, "color": "ffffffff"}, {"time": 7.9, "color": "ffffff00"}, {"time": 7.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 15.1333, "color": "ffffffff"}]}, "Asset 8": {"rgba": [{"color": "fffffffe", "curve": "stepped"}, {"time": 8.5333, "color": "fffffffe"}, {"time": 8.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 8.6, "color": "ffffff00"}, {"time": 9.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 15.1, "color": "fffffffe"}]}, "Asset 9": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 9.4, "color": "ffffffff"}, {"time": 9.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 9.4667, "color": "ffffff00"}, {"time": 10.3, "color": "ffffffff", "curve": "stepped"}, {"time": 15.1, "color": "ffffffff"}]}, "Asset 10": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 7.4333, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}, {"time": 7.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 15, "color": "ffffffff"}, {"time": 15.0667, "color": "ffffff00"}, {"time": 15.1, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"value": -1.67, "curve": [0.113, -4.78, 0.2, -28.85]}, {"time": 0.3, "value": -28.85, "curve": [0.478, -28.85, 0.658, -5.66]}, {"time": 0.8333, "value": 1.86, "curve": [1.022, 9.95, 1.238, 1.86]}, {"time": 1.3667, "value": -1.67, "curve": [1.48, -4.78, 1.567, -28.85]}, {"time": 1.6667, "value": -28.85, "curve": [1.844, -28.85, 2.022, 1.86]}, {"time": 2.2, "value": 1.86, "curve": [2.378, 1.86, 2.605, 1.86]}, {"time": 2.7333, "value": -1.67, "curve": [2.847, -4.78, 2.956, -28.85]}, {"time": 3.0667, "value": -28.85, "curve": [3.233, -28.85, 3.4, 1.86]}, {"time": 3.5667, "value": 1.86, "curve": [3.756, 1.86, 4.005, 1.86]}, {"time": 4.1333, "value": -1.67, "curve": [4.247, -4.78, 4.333, -28.85]}, {"time": 4.4333, "value": -28.85, "curve": [4.6, -28.85, 4.767, 1.86]}, {"time": 4.9333, "value": 1.86, "curve": [5.122, 1.86, 5.371, 1.86]}, {"time": 5.5, "value": -1.67, "curve": [5.613, -4.78, 5.7, -28.85]}, {"time": 5.8, "value": -28.85, "curve": [5.978, -28.85, 6.156, 1.86]}, {"time": 6.3333, "value": 1.86, "curve": [6.511, 1.86, 6.738, 1.86]}, {"time": 6.8667, "value": -1.67, "curve": [6.98, -4.78, 7.067, -28.85]}, {"time": 7.1667, "value": -28.85, "curve": [7.344, -28.85, 7.522, 1.86]}, {"time": 7.7, "value": 1.86, "curve": [7.889, 1.86, 8.138, 1.86]}, {"time": 8.2667, "value": -1.67, "curve": [8.38, -4.78, 8.489, -28.85]}, {"time": 8.6, "value": -28.85, "curve": [8.767, -28.85, 8.933, 1.86]}, {"time": 9.1, "value": 1.86, "curve": [9.278, 1.86, 9.505, 1.86]}, {"time": 9.6333, "value": -1.67, "curve": [9.747, -4.78, 9.856, -28.85]}, {"time": 9.9667, "value": -28.85, "curve": [10.133, -28.85, 10.3, 1.86]}, {"time": 10.4667, "value": 1.86, "curve": [10.656, 1.86, 10.905, 1.86]}, {"time": 11.0333, "value": -1.67, "curve": [11.147, -4.78, 11.233, -28.85]}, {"time": 11.3333, "value": -28.85, "curve": [11.5, -28.85, 11.667, 1.86]}, {"time": 11.8333, "value": 1.86, "curve": [12.022, 1.86, 12.271, 1.86]}, {"time": 12.4, "value": -1.67, "curve": [12.513, -4.78, 12.6, -28.85]}, {"time": 12.7, "value": -28.85, "curve": [12.878, -28.85, 13.056, 1.86]}, {"time": 13.2333, "value": 1.86, "curve": [13.411, 1.86, 13.638, 1.86]}, {"time": 13.7667, "value": -1.67, "curve": [13.88, -4.78, 13.967, -28.85]}, {"time": 14.0667, "value": -28.85, "curve": [14.244, -28.85, 14.422, 1.86]}, {"time": 14.6, "value": 1.86, "curve": [14.778, 1.86, 15.005, 1.86]}, {"time": 15.1333, "value": -1.67}]}, "bone3": {"rotate": [{"value": 4.9, "curve": [0.077, -0.74, 0.19, 19.84]}, {"time": 0.2667, "value": 17.64, "curve": [0.35, 15.27, 0.417, -12.8]}, {"time": 0.5, "value": -12.14, "curve": [0.592, -11.42, 0.708, 2.94]}, {"time": 0.8333, "value": 6.5, "curve": [0.987, 10.88, 1.119, 11.28]}, {"time": 1.2333, "value": 11.28, "curve": [1.274, 11.28, 1.321, 8.26]}, {"time": 1.3667, "value": 4.9, "curve": [1.444, -0.74, 1.556, 19.84]}, {"time": 1.6333, "value": 17.64, "curve": [1.716, 15.27, 1.817, -12.8]}, {"time": 1.9, "value": -12.14, "curve": [1.992, -11.42, 2.075, 2.94]}, {"time": 2.2, "value": 6.5, "curve": [2.353, 10.88, 2.485, 11.28]}, {"time": 2.6, "value": 11.28, "curve": [2.641, 11.28, 2.687, 8.26]}, {"time": 2.7333, "value": 4.9, "curve": [2.81, -0.74, 2.956, 19.84]}, {"time": 3.0333, "value": 17.64, "curve": [3.116, 15.27, 3.184, -12.8]}, {"time": 3.2667, "value": -12.14, "curve": [3.359, -11.42, 3.442, 2.94]}, {"time": 3.5667, "value": 6.5, "curve": [3.72, 10.88, 3.885, 11.28]}, {"time": 4, "value": 11.28, "curve": [4.041, 11.28, 4.087, 8.26]}, {"time": 4.1333, "value": 4.9, "curve": [4.21, -0.74, 4.323, 19.84]}, {"time": 4.4, "value": 17.64, "curve": [4.483, 15.27, 4.55, -12.8]}, {"time": 4.6333, "value": -12.14, "curve": [4.726, -11.42, 4.808, 2.94]}, {"time": 4.9333, "value": 6.5, "curve": [5.087, 10.88, 5.252, 11.28]}, {"time": 5.3667, "value": 11.28, "curve": [5.407, 11.28, 5.454, 8.26]}, {"time": 5.5, "value": 4.9, "curve": [5.577, -0.74, 5.69, 19.84]}, {"time": 5.7667, "value": 17.64, "curve": [5.85, 15.27, 5.917, -12.8]}, {"time": 6, "value": -12.14, "curve": [6.092, -11.42, 6.208, 2.94]}, {"time": 6.3333, "value": 6.5, "curve": [6.487, 10.88, 6.619, 11.28]}, {"time": 6.7333, "value": 11.28, "curve": [6.774, 11.28, 6.821, 8.26]}, {"time": 6.8667, "value": 4.9, "curve": [6.944, -0.74, 7.056, 19.84]}, {"time": 7.1333, "value": 17.64, "curve": [7.216, 15.27, 7.317, -12.8]}, {"time": 7.4, "value": -12.14, "curve": [7.492, -11.42, 7.575, 2.94]}, {"time": 7.7, "value": 6.5, "curve": [7.853, 10.88, 8.019, 11.28]}, {"time": 8.1333, "value": 11.28, "curve": [8.174, 11.28, 8.221, 8.26]}, {"time": 8.2667, "value": 4.9, "curve": [8.344, -0.74, 8.456, 19.84]}, {"time": 8.5333, "value": 17.64, "curve": [8.616, 15.27, 8.717, -12.8]}, {"time": 8.8, "value": -12.14, "curve": [8.892, -11.42, 8.975, 2.94]}, {"time": 9.1, "value": 6.5, "curve": [9.253, 10.88, 9.385, 11.28]}, {"time": 9.5, "value": 11.28, "curve": [9.541, 11.28, 9.587, 8.26]}, {"time": 9.6333, "value": 4.9, "curve": [9.71, -0.74, 9.856, 19.84]}, {"time": 9.9333, "value": 17.64, "curve": [10.016, 15.27, 10.084, -12.8]}, {"time": 10.1667, "value": -12.14, "curve": [10.259, -11.42, 10.342, 2.94]}, {"time": 10.4667, "value": 6.5, "curve": [10.62, 10.88, 10.785, 11.28]}, {"time": 10.9, "value": 11.28, "curve": [10.941, 11.28, 10.987, 8.26]}, {"time": 11.0333, "value": 4.9, "curve": [11.11, -0.74, 11.223, 19.84]}, {"time": 11.3, "value": 17.64, "curve": [11.383, 15.27, 11.45, -12.8]}, {"time": 11.5333, "value": -12.14, "curve": [11.626, -11.42, 11.708, 2.94]}, {"time": 11.8333, "value": 6.5, "curve": [11.987, 10.88, 12.152, 11.28]}, {"time": 12.2667, "value": 11.28, "curve": [12.307, 11.28, 12.354, 8.26]}, {"time": 12.4, "value": 4.9, "curve": [12.477, -0.74, 12.59, 19.84]}, {"time": 12.6667, "value": 17.64, "curve": [12.75, 15.27, 12.817, -12.8]}, {"time": 12.9, "value": -12.14, "curve": [12.992, -11.42, 13.108, 2.94]}, {"time": 13.2333, "value": 6.5, "curve": [13.387, 10.88, 13.519, 11.28]}, {"time": 13.6333, "value": 11.28, "curve": [13.674, 11.28, 13.721, 8.26]}, {"time": 13.7667, "value": 4.9, "curve": [13.844, -0.74, 13.956, 19.84]}, {"time": 14.0333, "value": 17.64, "curve": [14.116, 15.27, 14.217, -12.8]}, {"time": 14.3, "value": -12.14, "curve": [14.392, -11.42, 14.475, 2.94]}, {"time": 14.6, "value": 6.5, "curve": [14.753, 10.88, 14.885, 11.28]}, {"time": 15, "value": 11.28, "curve": [15.041, 11.28, 15.093, 7.88]}, {"time": 15.1333, "value": 4.9}]}, "bone4": {"rotate": [{"value": 5.68, "curve": [0.04, 7.46, 0.089, 5.39]}, {"time": 0.1333, "value": 5.39, "curve": [0.293, 5.39, 0.435, 1.74]}, {"time": 0.6, "value": -2.81, "curve": [0.703, -5.64, 0.804, -10.63]}, {"time": 0.9, "value": -10.21, "curve": [1.078, -9.44, 1.206, -1.4]}, {"time": 1.3667, "value": 5.68, "curve": [1.407, 7.46, 1.456, 5.39]}, {"time": 1.5, "value": 5.39, "curve": [1.66, 5.39, 1.801, 1.74]}, {"time": 1.9667, "value": -2.81, "curve": [2.069, -5.64, 2.171, -10.63]}, {"time": 2.2667, "value": -10.21, "curve": [2.444, -9.44, 2.573, -1.4]}, {"time": 2.7333, "value": 5.68, "curve": [2.774, 7.46, 2.856, 5.39]}, {"time": 2.9, "value": 5.39, "curve": [3.06, 5.39, 3.168, 1.74]}, {"time": 3.3333, "value": -2.81, "curve": [3.436, -5.64, 3.537, -10.63]}, {"time": 3.6333, "value": -10.21, "curve": [3.811, -9.44, 3.973, -1.4]}, {"time": 4.1333, "value": 5.68, "curve": [4.174, 7.46, 4.223, 5.39]}, {"time": 4.2667, "value": 5.39, "curve": [4.427, 5.39, 4.535, 1.74]}, {"time": 4.7, "value": -2.81, "curve": [4.803, -5.64, 4.904, -10.63]}, {"time": 5, "value": -10.21, "curve": [5.178, -9.44, 5.339, -1.4]}, {"time": 5.5, "value": 5.68, "curve": [5.54, 7.46, 5.589, 5.39]}, {"time": 5.6333, "value": 5.39, "curve": [5.793, 5.39, 5.901, 1.74]}, {"time": 6.0667, "value": -2.81, "curve": [6.169, -5.64, 6.304, -10.63]}, {"time": 6.4, "value": -10.21, "curve": [6.578, -9.44, 6.706, -1.4]}, {"time": 6.8667, "value": 5.68, "curve": [6.907, 7.46, 6.956, 5.39]}, {"time": 7, "value": 5.39, "curve": [7.16, 5.39, 7.301, 1.74]}, {"time": 7.4667, "value": -2.81, "curve": [7.569, -5.64, 7.671, -10.63]}, {"time": 7.7667, "value": -10.21, "curve": [7.944, -9.44, 8.106, -1.4]}, {"time": 8.2667, "value": 5.68, "curve": [8.307, 7.46, 8.356, 5.39]}, {"time": 8.4, "value": 5.39, "curve": [8.56, 5.39, 8.701, 1.74]}, {"time": 8.8667, "value": -2.81, "curve": [8.969, -5.64, 9.071, -10.63]}, {"time": 9.1667, "value": -10.21, "curve": [9.344, -9.44, 9.473, -1.4]}, {"time": 9.6333, "value": 5.68, "curve": [9.674, 7.46, 9.756, 5.39]}, {"time": 9.8, "value": 5.39, "curve": [9.96, 5.39, 10.068, 1.74]}, {"time": 10.2333, "value": -2.81, "curve": [10.336, -5.64, 10.437, -10.63]}, {"time": 10.5333, "value": -10.21, "curve": [10.711, -9.44, 10.873, -1.4]}, {"time": 11.0333, "value": 5.68, "curve": [11.074, 7.46, 11.123, 5.39]}, {"time": 11.1667, "value": 5.39, "curve": [11.327, 5.39, 11.435, 1.74]}, {"time": 11.6, "value": -2.81, "curve": [11.703, -5.64, 11.804, -10.63]}, {"time": 11.9, "value": -10.21, "curve": [12.078, -9.44, 12.239, -1.4]}, {"time": 12.4, "value": 5.68, "curve": [12.44, 7.46, 12.489, 5.39]}, {"time": 12.5333, "value": 5.39, "curve": [12.693, 5.39, 12.801, 1.74]}, {"time": 12.9667, "value": -2.81, "curve": [13.069, -5.64, 13.204, -10.63]}, {"time": 13.3, "value": -10.21, "curve": [13.478, -9.44, 13.606, -1.4]}, {"time": 13.7667, "value": 5.68, "curve": [13.807, 7.46, 13.856, 5.39]}, {"time": 13.9, "value": 5.39, "curve": [14.06, 5.39, 14.201, 1.74]}, {"time": 14.3667, "value": -2.81, "curve": [14.469, -5.64, 14.571, -10.63]}, {"time": 14.6667, "value": -10.21, "curve": [14.844, -9.44, 14.971, -1.49]}, {"time": 15.1333, "value": 5.68}]}, "bone8": {"rotate": [{"value": 5.68, "curve": [0.04, 7.46, 0.089, 5.39]}, {"time": 0.1333, "value": 5.39, "curve": [0.293, 5.39, 0.435, 1.74]}, {"time": 0.6, "value": -2.81, "curve": [0.703, -5.64, 0.804, -10.63]}, {"time": 0.9, "value": -10.21, "curve": [1.078, -9.44, 1.206, -1.4]}, {"time": 1.3667, "value": 5.68, "curve": [1.407, 7.46, 1.456, 5.39]}, {"time": 1.5, "value": 5.39, "curve": [1.66, 5.39, 1.801, 1.74]}, {"time": 1.9667, "value": -2.81, "curve": [2.069, -5.64, 2.171, -10.63]}, {"time": 2.2667, "value": -10.21, "curve": [2.444, -9.44, 2.573, -1.4]}, {"time": 2.7333, "value": 5.68, "curve": [2.774, 7.46, 2.856, 5.39]}, {"time": 2.9, "value": 5.39, "curve": [3.06, 5.39, 3.168, 1.74]}, {"time": 3.3333, "value": -2.81, "curve": [3.436, -5.64, 3.537, -10.63]}, {"time": 3.6333, "value": -10.21, "curve": [3.811, -9.44, 3.973, -1.4]}, {"time": 4.1333, "value": 5.68, "curve": [4.174, 7.46, 4.223, 5.39]}, {"time": 4.2667, "value": 5.39, "curve": [4.427, 5.39, 4.535, 1.74]}, {"time": 4.7, "value": -2.81, "curve": [4.803, -5.64, 4.904, -10.63]}, {"time": 5, "value": -10.21, "curve": [5.178, -9.44, 5.339, -1.4]}, {"time": 5.5, "value": 5.68, "curve": [5.54, 7.46, 5.589, 5.39]}, {"time": 5.6333, "value": 5.39, "curve": [5.793, 5.39, 5.901, 1.74]}, {"time": 6.0667, "value": -2.81, "curve": [6.169, -5.64, 6.304, -10.63]}, {"time": 6.4, "value": -10.21, "curve": [6.578, -9.44, 6.706, -1.4]}, {"time": 6.8667, "value": 5.68, "curve": [6.907, 7.46, 6.956, 5.39]}, {"time": 7, "value": 5.39, "curve": [7.16, 5.39, 7.301, 1.74]}, {"time": 7.4667, "value": -2.81, "curve": [7.569, -5.64, 7.671, -10.63]}, {"time": 7.7667, "value": -10.21, "curve": [7.944, -9.44, 8.106, -1.4]}, {"time": 8.2667, "value": 5.68, "curve": [8.307, 7.46, 8.356, 5.39]}, {"time": 8.4, "value": 5.39, "curve": [8.56, 5.39, 8.701, 1.74]}, {"time": 8.8667, "value": -2.81, "curve": [8.969, -5.64, 9.071, -10.63]}, {"time": 9.1667, "value": -10.21, "curve": [9.344, -9.44, 9.473, -1.4]}, {"time": 9.6333, "value": 5.68, "curve": [9.674, 7.46, 9.756, 5.39]}, {"time": 9.8, "value": 5.39, "curve": [9.96, 5.39, 10.068, 1.74]}, {"time": 10.2333, "value": -2.81, "curve": [10.336, -5.64, 10.437, -10.63]}, {"time": 10.5333, "value": -10.21, "curve": [10.711, -9.44, 10.873, -1.4]}, {"time": 11.0333, "value": 5.68, "curve": [11.074, 7.46, 11.123, 5.39]}, {"time": 11.1667, "value": 5.39, "curve": [11.327, 5.39, 11.435, 1.74]}, {"time": 11.6, "value": -2.81, "curve": [11.703, -5.64, 11.804, -10.63]}, {"time": 11.9, "value": -10.21, "curve": [12.078, -9.44, 12.239, -1.4]}, {"time": 12.4, "value": 5.68, "curve": [12.44, 7.46, 12.489, 5.39]}, {"time": 12.5333, "value": 5.39, "curve": [12.693, 5.39, 12.801, 1.74]}, {"time": 12.9667, "value": -2.81, "curve": [13.069, -5.64, 13.204, -10.63]}, {"time": 13.3, "value": -10.21, "curve": [13.478, -9.44, 13.606, -1.4]}, {"time": 13.7667, "value": 5.68, "curve": [13.807, 7.46, 13.856, 5.39]}, {"time": 13.9, "value": 5.39, "curve": [14.06, 5.39, 14.201, 1.74]}, {"time": 14.3667, "value": -2.81, "curve": [14.469, -5.64, 14.571, -10.63]}, {"time": 14.6667, "value": -10.21, "curve": [14.844, -9.44, 14.971, -1.49]}, {"time": 15.1333, "value": 5.68}]}, "bone7": {"rotate": [{"value": 4.9, "curve": [0.077, -0.74, 0.19, 19.84]}, {"time": 0.2667, "value": 17.64, "curve": [0.35, 15.27, 0.417, -12.8]}, {"time": 0.5, "value": -12.14, "curve": [0.592, -11.42, 0.708, 2.94]}, {"time": 0.8333, "value": 6.5, "curve": [0.987, 10.88, 1.119, 11.28]}, {"time": 1.2333, "value": 11.28, "curve": [1.274, 11.28, 1.321, 8.26]}, {"time": 1.3667, "value": 4.9, "curve": [1.444, -0.74, 1.556, 19.84]}, {"time": 1.6333, "value": 17.64, "curve": [1.716, 15.27, 1.817, -12.8]}, {"time": 1.9, "value": -12.14, "curve": [1.992, -11.42, 2.075, 2.94]}, {"time": 2.2, "value": 6.5, "curve": [2.353, 10.88, 2.485, 11.28]}, {"time": 2.6, "value": 11.28, "curve": [2.641, 11.28, 2.687, 8.26]}, {"time": 2.7333, "value": 4.9, "curve": [2.81, -0.74, 2.956, 19.84]}, {"time": 3.0333, "value": 17.64, "curve": [3.116, 15.27, 3.184, -12.8]}, {"time": 3.2667, "value": -12.14, "curve": [3.359, -11.42, 3.442, 2.94]}, {"time": 3.5667, "value": 6.5, "curve": [3.72, 10.88, 3.885, 11.28]}, {"time": 4, "value": 11.28, "curve": [4.041, 11.28, 4.087, 8.26]}, {"time": 4.1333, "value": 4.9, "curve": [4.21, -0.74, 4.323, 19.84]}, {"time": 4.4, "value": 17.64, "curve": [4.483, 15.27, 4.55, -12.8]}, {"time": 4.6333, "value": -12.14, "curve": [4.726, -11.42, 4.808, 2.94]}, {"time": 4.9333, "value": 6.5, "curve": [5.087, 10.88, 5.252, 11.28]}, {"time": 5.3667, "value": 11.28, "curve": [5.407, 11.28, 5.454, 8.26]}, {"time": 5.5, "value": 4.9, "curve": [5.577, -0.74, 5.69, 19.84]}, {"time": 5.7667, "value": 17.64, "curve": [5.85, 15.27, 5.917, -12.8]}, {"time": 6, "value": -12.14, "curve": [6.092, -11.42, 6.208, 2.94]}, {"time": 6.3333, "value": 6.5, "curve": [6.487, 10.88, 6.619, 11.28]}, {"time": 6.7333, "value": 11.28, "curve": [6.774, 11.28, 6.821, 8.26]}, {"time": 6.8667, "value": 4.9, "curve": [6.944, -0.74, 7.056, 19.84]}, {"time": 7.1333, "value": 17.64, "curve": [7.216, 15.27, 7.317, -12.8]}, {"time": 7.4, "value": -12.14, "curve": [7.492, -11.42, 7.575, 2.94]}, {"time": 7.7, "value": 6.5, "curve": [7.853, 10.88, 8.019, 11.28]}, {"time": 8.1333, "value": 11.28, "curve": [8.174, 11.28, 8.221, 8.26]}, {"time": 8.2667, "value": 4.9, "curve": [8.344, -0.74, 8.456, 19.84]}, {"time": 8.5333, "value": 17.64, "curve": [8.616, 15.27, 8.717, -12.8]}, {"time": 8.8, "value": -12.14, "curve": [8.892, -11.42, 8.975, 2.94]}, {"time": 9.1, "value": 6.5, "curve": [9.253, 10.88, 9.385, 11.28]}, {"time": 9.5, "value": 11.28, "curve": [9.541, 11.28, 9.587, 8.26]}, {"time": 9.6333, "value": 4.9, "curve": [9.71, -0.74, 9.856, 19.84]}, {"time": 9.9333, "value": 17.64, "curve": [10.016, 15.27, 10.084, -12.8]}, {"time": 10.1667, "value": -12.14, "curve": [10.259, -11.42, 10.342, 2.94]}, {"time": 10.4667, "value": 6.5, "curve": [10.62, 10.88, 10.785, 11.28]}, {"time": 10.9, "value": 11.28, "curve": [10.941, 11.28, 10.987, 8.26]}, {"time": 11.0333, "value": 4.9, "curve": [11.11, -0.74, 11.223, 19.84]}, {"time": 11.3, "value": 17.64, "curve": [11.383, 15.27, 11.45, -12.8]}, {"time": 11.5333, "value": -12.14, "curve": [11.626, -11.42, 11.708, 2.94]}, {"time": 11.8333, "value": 6.5, "curve": [11.987, 10.88, 12.152, 11.28]}, {"time": 12.2667, "value": 11.28, "curve": [12.307, 11.28, 12.354, 8.26]}, {"time": 12.4, "value": 4.9, "curve": [12.477, -0.74, 12.59, 19.84]}, {"time": 12.6667, "value": 17.64, "curve": [12.75, 15.27, 12.817, -12.8]}, {"time": 12.9, "value": -12.14, "curve": [12.992, -11.42, 13.108, 2.94]}, {"time": 13.2333, "value": 6.5, "curve": [13.387, 10.88, 13.519, 11.28]}, {"time": 13.6333, "value": 11.28, "curve": [13.674, 11.28, 13.721, 8.26]}, {"time": 13.7667, "value": 4.9, "curve": [13.844, -0.74, 13.956, 19.84]}, {"time": 14.0333, "value": 17.64, "curve": [14.116, 15.27, 14.217, -12.8]}, {"time": 14.3, "value": -12.14, "curve": [14.392, -11.42, 14.475, 2.94]}, {"time": 14.6, "value": 6.5, "curve": [14.753, 10.88, 14.885, 11.28]}, {"time": 15, "value": 11.28, "curve": [15.041, 11.28, 15.093, 7.88]}, {"time": 15.1333, "value": 4.9}]}, "bone6": {"rotate": [{"value": -1.67, "curve": [0.113, -4.78, 0.2, -28.85]}, {"time": 0.3, "value": -28.85, "curve": [0.478, -28.85, 0.656, 1.86]}, {"time": 0.8333, "value": 1.86, "curve": [1.011, 1.86, 1.238, 1.86]}, {"time": 1.3667, "value": -1.67, "curve": [1.48, -4.78, 1.567, -28.85]}, {"time": 1.6667, "value": -28.85, "curve": [1.844, -28.85, 2.022, 1.86]}, {"time": 2.2, "value": 1.86, "curve": [2.378, 1.86, 2.605, 1.86]}, {"time": 2.7333, "value": -1.67, "curve": [2.847, -4.78, 2.956, -28.85]}, {"time": 3.0667, "value": -28.85, "curve": [3.233, -28.85, 3.4, 1.86]}, {"time": 3.5667, "value": 1.86, "curve": [3.756, 1.86, 4.005, 1.86]}, {"time": 4.1333, "value": -1.67, "curve": [4.247, -4.78, 4.333, -28.85]}, {"time": 4.4333, "value": -28.85, "curve": [4.6, -28.85, 4.767, 1.86]}, {"time": 4.9333, "value": 1.86, "curve": [5.122, 1.86, 5.371, 1.86]}, {"time": 5.5, "value": -1.67, "curve": [5.613, -4.78, 5.7, -28.85]}, {"time": 5.8, "value": -28.85, "curve": [5.978, -28.85, 6.156, 1.86]}, {"time": 6.3333, "value": 1.86, "curve": [6.511, 1.86, 6.738, 1.86]}, {"time": 6.8667, "value": -1.67, "curve": [6.98, -4.78, 7.067, -28.85]}, {"time": 7.1667, "value": -28.85, "curve": [7.344, -28.85, 7.522, 1.86]}, {"time": 7.7, "value": 1.86, "curve": [7.889, 1.86, 8.138, 1.86]}, {"time": 8.2667, "value": -1.67, "curve": [8.38, -4.78, 8.489, -28.85]}, {"time": 8.6, "value": -28.85, "curve": [8.767, -28.85, 8.933, 1.86]}, {"time": 9.1, "value": 1.86, "curve": [9.278, 1.86, 9.505, 1.86]}, {"time": 9.6333, "value": -1.67, "curve": [9.747, -4.78, 9.856, -28.85]}, {"time": 9.9667, "value": -28.85, "curve": [10.133, -28.85, 10.3, 1.86]}, {"time": 10.4667, "value": 1.86, "curve": [10.656, 1.86, 10.905, 1.86]}, {"time": 11.0333, "value": -1.67, "curve": [11.147, -4.78, 11.233, -28.85]}, {"time": 11.3333, "value": -28.85, "curve": [11.5, -28.85, 11.667, 1.86]}, {"time": 11.8333, "value": 1.86, "curve": [12.022, 1.86, 12.271, 1.86]}, {"time": 12.4, "value": -1.67, "curve": [12.513, -4.78, 12.6, -28.85]}, {"time": 12.7, "value": -28.85, "curve": [12.878, -28.85, 13.056, 1.86]}, {"time": 13.2333, "value": 1.86, "curve": [13.411, 1.86, 13.638, 1.86]}, {"time": 13.7667, "value": -1.67, "curve": [13.88, -4.78, 13.967, -28.85]}, {"time": 14.0667, "value": -28.85, "curve": [14.244, -28.85, 14.422, 1.86]}, {"time": 14.6, "value": 1.86, "curve": [14.778, 1.86, 15.005, 1.86]}, {"time": 15.1333, "value": -1.67}]}, "bird": {"translate": [{"y": 0.51, "curve": [0.041, 0, 0.093, 0, 0.041, 0.51, 0.093, 0]}, {"time": 0.1333, "curve": [0.255, 0, 0.378, 0, 0.255, 0, 0.378, 4.19]}, {"time": 0.5, "y": 4.19, "curve": [0.795, 0, 1.08, 0, 0.795, 4.19, 1.08, 0.51]}, {"time": 1.3667, "y": 0.51, "curve": [1.407, 0, 1.459, 0, 1.407, 0.51, 1.459, 0]}, {"time": 1.5, "curve": [1.622, 0, 1.778, 0, 1.622, 0, 1.778, 4.19]}, {"time": 1.9, "y": 4.19, "curve": [2.195, 0, 2.447, 0, 2.195, 4.19, 2.447, 0.51]}, {"time": 2.7333, "y": 0.51, "curve": [2.774, 0, 2.859, 0, 2.774, 0.51, 2.859, 0]}, {"time": 2.9, "curve": [3.022, 0, 3.145, 0, 3.022, 0, 3.145, 4.19]}, {"time": 3.2667, "y": 4.19, "curve": [3.562, 0, 3.847, 0, 3.562, 4.19, 3.847, 0.51]}, {"time": 4.1333, "y": 0.51, "curve": [4.174, 0, 4.226, 0, 4.174, 0.51, 4.226, 0]}, {"time": 4.2667, "curve": [4.389, 0, 4.511, 0, 4.389, 0, 4.511, 4.19]}, {"time": 4.6333, "y": 4.19, "curve": [4.928, 0, 5.213, 0, 4.928, 4.19, 5.213, 0.51]}, {"time": 5.5, "y": 0.51, "curve": [5.541, 0, 5.593, 0, 5.541, 0.51, 5.593, 0]}, {"time": 5.6333, "curve": [5.755, 0, 5.878, 0, 5.755, 0, 5.878, 4.19]}, {"time": 6, "y": 4.19, "curve": [6.295, 0, 6.58, 0, 6.295, 4.19, 6.58, 0.51]}, {"time": 6.8667, "y": 0.51, "curve": [6.907, 0, 6.959, 0, 6.907, 0.51, 6.959, 0]}, {"time": 7, "curve": [7.122, 0, 7.278, 0, 7.122, 0, 7.278, 4.19]}, {"time": 7.4, "y": 4.19, "curve": [7.695, 0, 7.98, 0, 7.695, 4.19, 7.98, 0.51]}, {"time": 8.2667, "y": 0.51, "curve": [8.307, 0, 8.359, 0, 8.307, 0.51, 8.359, 0]}, {"time": 8.4, "curve": [8.522, 0, 8.678, 0, 8.522, 0, 8.678, 4.19]}, {"time": 8.8, "y": 4.19, "curve": [9.095, 0, 9.347, 0, 9.095, 4.19, 9.347, 0.51]}, {"time": 9.6333, "y": 0.51, "curve": [9.674, 0, 9.759, 0, 9.674, 0.51, 9.759, 0]}, {"time": 9.8, "curve": [9.922, 0, 10.045, 0, 9.922, 0, 10.045, 4.19]}, {"time": 10.1667, "y": 4.19, "curve": [10.462, 0, 10.747, 0, 10.462, 4.19, 10.747, 0.51]}, {"time": 11.0333, "y": 0.51, "curve": [11.074, 0, 11.126, 0, 11.074, 0.51, 11.126, 0]}, {"time": 11.1667, "curve": [11.289, 0, 11.411, 0, 11.289, 0, 11.411, 4.19]}, {"time": 11.5333, "y": 4.19, "curve": [11.828, 0, 12.113, 0, 11.828, 4.19, 12.113, 0.51]}, {"time": 12.4, "y": 0.51, "curve": [12.441, 0, 12.493, 0, 12.441, 0.51, 12.493, 0]}, {"time": 12.5333, "curve": [12.655, 0, 12.778, 0, 12.655, 0, 12.778, 4.19]}, {"time": 12.9, "y": 4.19, "curve": [13.195, 0, 13.48, 0, 13.195, 4.19, 13.48, 0.51]}, {"time": 13.7667, "y": 0.51, "curve": [13.807, 0, 13.859, 0, 13.807, 0.51, 13.859, 0]}, {"time": 13.9, "curve": [14.022, 0, 14.178, 0, 14.022, 0, 14.178, 4.19]}, {"time": 14.3, "y": 4.19, "curve": [14.595, 0, 14.838, 0, 14.595, 4.19, 14.838, 0.51]}, {"time": 15.1333, "y": 0.51}]}, "smoke_1": {"translate": [{"x": -928.48}, {"time": 8.5667, "x": -2460.79}, {"time": 8.6, "x": -351.97}, {"time": 15.1, "x": -928.48}]}, "Asset 9": {"translate": [{"x": -1997.55}, {"time": 1.8667, "x": -2949.06}, {"time": 1.9, "x": -757.58}, {"time": 7.5333, "x": -1997.55, "curve": "stepped"}, {"time": 7.5667, "x": -1997.55}, {"time": 9.4333, "x": -2949.06}, {"time": 9.4667, "x": -757.58}, {"time": 15.1, "x": -1997.55}]}, "Asset 3": {"scale": [{}, {"time": 0.2, "y": 1.022}, {"time": 0.3333}, {"time": 0.5, "y": 1.022}, {"time": 0.6667}, {"time": 0.8667, "y": 1.022}, {"time": 1}, {"time": 1.1667, "y": 1.022}, {"time": 1.3}, {"time": 1.5, "y": 1.022}, {"time": 1.6667}, {"time": 1.8333, "y": 1.022}, {"time": 1.9667}, {"time": 2.1667, "y": 1.022}, {"time": 2.3333}, {"time": 2.5333, "y": 1.022}, {"time": 2.6667}, {"time": 2.8333, "y": 1.022}, {"time": 3}, {"time": 3.2, "y": 1.022}, {"time": 3.3333}, {"time": 3.5, "y": 1.022}, {"time": 3.6333}, {"time": 3.8333, "y": 1.022}, {"time": 4}, {"time": 4.1667, "y": 1.022}, {"time": 4.3}, {"time": 4.5, "y": 1.022}, {"time": 4.6667}, {"time": 4.8667, "y": 1.022}, {"time": 5}, {"time": 5.2, "y": 1.022}, {"time": 5.3333}, {"time": 5.5333, "y": 1.022}, {"time": 5.6667}, {"time": 5.8667, "y": 1.022}, {"time": 6}, {"time": 6.2, "y": 1.022}, {"time": 6.3333}, {"time": 6.5333, "y": 1.022}, {"time": 6.6667}, {"time": 6.8667, "y": 1.022}, {"time": 7}, {"time": 7.2, "y": 1.022}, {"time": 7.3333, "curve": "stepped"}, {"time": 7.5667}, {"time": 7.7667, "y": 1.022}, {"time": 7.9}, {"time": 8.0667, "y": 1.022}, {"time": 8.2333}, {"time": 8.4333, "y": 1.022}, {"time": 8.5667}, {"time": 8.7333, "y": 1.022}, {"time": 8.8667}, {"time": 9.0667, "y": 1.022}, {"time": 9.2333}, {"time": 9.4, "y": 1.022}, {"time": 9.5333}, {"time": 9.7333, "y": 1.022}, {"time": 9.9}, {"time": 10.1, "y": 1.022}, {"time": 10.2333}, {"time": 10.4, "y": 1.022}, {"time": 10.5667}, {"time": 10.7667, "y": 1.022}, {"time": 10.9}, {"time": 11.0667, "y": 1.022}, {"time": 11.2}, {"time": 11.4, "y": 1.022}, {"time": 11.5667}, {"time": 11.7333, "y": 1.022}, {"time": 11.8667}, {"time": 12.0667, "y": 1.022}, {"time": 12.2333}, {"time": 12.4333, "y": 1.022}, {"time": 12.5667}, {"time": 12.7667, "y": 1.022}, {"time": 12.9}, {"time": 13.1, "y": 1.022}, {"time": 13.2333}, {"time": 13.4333, "y": 1.022}, {"time": 13.5667}, {"time": 13.7667, "y": 1.022}, {"time": 13.9}, {"time": 14.1, "y": 1.022}, {"time": 14.2333}, {"time": 14.4333, "y": 1.022}, {"time": 14.5667}, {"time": 14.7667, "y": 1.022}, {"time": 14.9}]}, "Asset 10": {"translate": [{}, {"time": 7.5, "x": -2370}, {"time": 7.5333, "curve": "stepped"}, {"time": 7.5667}, {"time": 15.0667, "x": -2370}, {"time": 15.1}]}, "Asset 8": {"translate": [{}, {"time": 15.1, "x": 2489.32}, {"time": 15.1333}]}, "Asset 7": {"translate": [{"x": -1176.93}, {"time": 7.9, "x": -2447.91}, {"time": 7.9333, "x": -13.66}, {"time": 15.1333, "x": -1176.93}]}, "Asset 11": {"translate": [{"x": -13.66}, {"time": 15.1333, "x": -2436.33}]}, "bone10": {"rotate": [{"value": -7.66, "curve": [0.059, -9.24, 0.118, -10.43]}, {"time": 0.1667, "value": -10.21, "curve": [0.344, -9.44, 0.473, -1.4]}, {"time": 0.6333, "value": 5.68, "curve": [0.674, 7.46, 0.723, 5.39]}, {"time": 0.7667, "value": 5.39, "curve": [0.927, 5.39, 1.068, 1.74]}, {"time": 1.2333, "value": -2.81, "curve": [1.336, -5.64, 1.437, -10.63]}, {"time": 1.5333, "value": -10.21, "curve": [1.711, -9.44, 1.873, -1.4]}, {"time": 2.0333, "value": 5.68, "curve": [2.044, 6.15, 2.055, 6.33]}, {"time": 2.0667, "value": 6.37, "curve": [2.099, 6.49, 2.134, 5.39]}, {"time": 2.1667, "value": 5.39, "curve": [2.327, 5.39, 2.468, 1.74]}, {"time": 2.6333, "value": -2.81, "curve": [2.736, -5.64, 2.837, -10.63]}, {"time": 2.9333, "value": -10.21, "curve": [3.111, -9.44, 3.239, -1.4]}, {"time": 3.4, "value": 5.68, "curve": [3.44, 7.46, 3.523, 5.39]}, {"time": 3.5667, "value": 5.39, "curve": [3.727, 5.39, 3.835, 1.74]}, {"time": 4, "value": -2.81, "curve": [4.103, -5.64, 4.204, -10.63]}, {"time": 4.3, "value": -10.21, "curve": [4.478, -9.44, 4.639, -1.4]}, {"time": 4.8, "value": 5.68, "curve": [4.84, 7.46, 4.889, 5.39]}, {"time": 4.9333, "value": 5.39, "curve": [5.093, 5.39, 5.201, 1.74]}, {"time": 5.3667, "value": -2.81, "curve": [5.469, -5.64, 5.571, -10.63]}, {"time": 5.6667, "value": -10.21, "curve": [5.844, -9.44, 6.006, -1.4]}, {"time": 6.1667, "value": 5.68, "curve": [6.207, 7.46, 6.256, 5.39]}, {"time": 6.3, "value": 5.39, "curve": [6.46, 5.39, 6.568, 1.74]}, {"time": 6.7333, "value": -2.81, "curve": [6.836, -5.64, 6.971, -10.63]}, {"time": 7.0667, "value": -10.21, "curve": [7.244, -9.44, 7.373, -1.4]}, {"time": 7.5333, "value": 5.68, "curve": [7.574, 7.46, 7.623, 5.39]}, {"time": 7.6667, "value": 5.39, "curve": [7.827, 5.39, 7.968, 1.74]}, {"time": 8.1333, "value": -2.81, "curve": [8.236, -5.64, 8.337, -10.63]}, {"time": 8.4333, "value": -10.21, "curve": [8.611, -9.44, 8.737, -1.49]}, {"time": 8.9, "value": 5.68, "curve": [8.94, 7.46, 8.989, 5.39]}, {"time": 9.0333, "value": 5.39, "curve": [9.193, 5.39, 9.335, 1.74]}, {"time": 9.5, "value": -2.81, "curve": [9.603, -5.64, 9.704, -10.63]}, {"time": 9.8, "value": -10.21, "curve": [9.978, -9.44, 10.106, -1.4]}, {"time": 10.2667, "value": 5.68, "curve": [10.307, 7.46, 10.356, 5.39]}, {"time": 10.4, "value": 5.39, "curve": [10.56, 5.39, 10.701, 1.74]}, {"time": 10.8667, "value": -2.81, "curve": [10.969, -5.64, 11.071, -10.63]}, {"time": 11.1667, "value": -10.21, "curve": [11.344, -9.44, 11.473, -1.4]}, {"time": 11.6333, "value": 5.68, "curve": [11.674, 7.46, 11.756, 5.39]}, {"time": 11.8, "value": 5.39, "curve": [11.96, 5.39, 12.068, 1.74]}, {"time": 12.2333, "value": -2.81, "curve": [12.336, -5.64, 12.437, -10.63]}, {"time": 12.5333, "value": -10.21, "curve": [12.711, -9.44, 12.873, -1.4]}, {"time": 13.0333, "value": 5.68, "curve": [13.074, 7.46, 13.123, 5.39]}, {"time": 13.1667, "value": 5.39, "curve": [13.327, 5.39, 13.435, 1.74]}, {"time": 13.6, "value": -2.81, "curve": [13.703, -5.64, 13.804, -10.63]}, {"time": 13.9, "value": -10.21, "curve": [14.078, -9.44, 14.239, -1.4]}, {"time": 14.4, "value": 5.68, "curve": [14.44, 7.46, 14.489, 5.39]}, {"time": 14.5333, "value": 5.39, "curve": [14.693, 5.39, 14.801, 1.74]}, {"time": 14.9667, "value": -2.81, "curve": [15.017, -4.2, 15.076, -6.12]}, {"time": 15.1333, "value": -7.66}]}, "bone9": {"rotate": [{"value": 1.63, "curve": [0.033, 3.69, 0.066, 5.54]}, {"time": 0.1, "value": 6.5, "curve": [0.253, 10.88, 0.385, 11.28]}, {"time": 0.5, "value": 11.28, "curve": [0.541, 11.28, 0.587, 8.26]}, {"time": 0.6333, "value": 4.9, "curve": [0.71, -0.74, 0.823, 19.84]}, {"time": 0.9, "value": 17.64, "curve": [0.983, 15.27, 1.084, -12.8]}, {"time": 1.1667, "value": -12.14, "curve": [1.259, -11.42, 1.342, 2.94]}, {"time": 1.4667, "value": 6.5, "curve": [1.62, 10.88, 1.785, 11.28]}, {"time": 1.9, "value": 11.28, "curve": [1.941, 11.28, 1.987, 8.26]}, {"time": 2.0333, "value": 4.9, "curve": [2.044, 4.13, 2.055, 3.98]}, {"time": 2.0667, "value": 4.06, "curve": [2.14, 4.59, 2.233, 19.54]}, {"time": 2.3, "value": 17.64, "curve": [2.383, 15.27, 2.484, -12.8]}, {"time": 2.5667, "value": -12.14, "curve": [2.659, -11.42, 2.742, 2.94]}, {"time": 2.8667, "value": 6.5, "curve": [3.02, 10.88, 3.152, 11.28]}, {"time": 3.2667, "value": 11.28, "curve": [3.307, 11.28, 3.354, 8.26]}, {"time": 3.4, "value": 4.9, "curve": [3.477, -0.74, 3.623, 19.84]}, {"time": 3.7, "value": 17.64, "curve": [3.783, 15.27, 3.85, -12.8]}, {"time": 3.9333, "value": -12.14, "curve": [4.026, -11.42, 4.108, 2.94]}, {"time": 4.2333, "value": 6.5, "curve": [4.387, 10.88, 4.552, 11.28]}, {"time": 4.6667, "value": 11.28, "curve": [4.707, 11.28, 4.754, 8.26]}, {"time": 4.8, "value": 4.9, "curve": [4.877, -0.74, 4.99, 19.84]}, {"time": 5.0667, "value": 17.64, "curve": [5.15, 15.27, 5.217, -12.8]}, {"time": 5.3, "value": -12.14, "curve": [5.392, -11.42, 5.475, 2.94]}, {"time": 5.6, "value": 6.5, "curve": [5.753, 10.88, 5.919, 11.28]}, {"time": 6.0333, "value": 11.28, "curve": [6.074, 11.28, 6.121, 8.26]}, {"time": 6.1667, "value": 4.9, "curve": [6.244, -0.74, 6.356, 19.84]}, {"time": 6.4333, "value": 17.64, "curve": [6.516, 15.27, 6.584, -12.8]}, {"time": 6.6667, "value": -12.14, "curve": [6.759, -11.42, 6.875, 2.94]}, {"time": 7, "value": 6.5, "curve": [7.153, 10.88, 7.285, 11.28]}, {"time": 7.4, "value": 11.28, "curve": [7.441, 11.28, 7.487, 8.26]}, {"time": 7.5333, "value": 4.9, "curve": [7.61, -0.74, 7.723, 19.84]}, {"time": 7.8, "value": 17.64, "curve": [7.883, 15.27, 7.984, -12.8]}, {"time": 8.0667, "value": -12.14, "curve": [8.159, -11.42, 8.242, 2.94]}, {"time": 8.3667, "value": 6.5, "curve": [8.52, 10.88, 8.652, 11.28]}, {"time": 8.7667, "value": 11.28, "curve": [8.807, 11.28, 8.859, 7.88]}, {"time": 8.9, "value": 4.9, "curve": [8.977, -0.74, 9.09, 19.84]}, {"time": 9.1667, "value": 17.64, "curve": [9.25, 15.27, 9.317, -12.8]}, {"time": 9.4, "value": -12.14, "curve": [9.492, -11.42, 9.608, 2.94]}, {"time": 9.7333, "value": 6.5, "curve": [9.887, 10.88, 10.019, 11.28]}, {"time": 10.1333, "value": 11.28, "curve": [10.174, 11.28, 10.221, 8.26]}, {"time": 10.2667, "value": 4.9, "curve": [10.344, -0.74, 10.456, 19.84]}, {"time": 10.5333, "value": 17.64, "curve": [10.616, 15.27, 10.717, -12.8]}, {"time": 10.8, "value": -12.14, "curve": [10.892, -11.42, 10.975, 2.94]}, {"time": 11.1, "value": 6.5, "curve": [11.253, 10.88, 11.385, 11.28]}, {"time": 11.5, "value": 11.28, "curve": [11.541, 11.28, 11.587, 8.26]}, {"time": 11.6333, "value": 4.9, "curve": [11.71, -0.74, 11.856, 19.84]}, {"time": 11.9333, "value": 17.64, "curve": [12.016, 15.27, 12.084, -12.8]}, {"time": 12.1667, "value": -12.14, "curve": [12.259, -11.42, 12.342, 2.94]}, {"time": 12.4667, "value": 6.5, "curve": [12.62, 10.88, 12.785, 11.28]}, {"time": 12.9, "value": 11.28, "curve": [12.941, 11.28, 12.987, 8.26]}, {"time": 13.0333, "value": 4.9, "curve": [13.11, -0.74, 13.223, 19.84]}, {"time": 13.3, "value": 17.64, "curve": [13.383, 15.27, 13.45, -12.8]}, {"time": 13.5333, "value": -12.14, "curve": [13.626, -11.42, 13.708, 2.94]}, {"time": 13.8333, "value": 6.5, "curve": [13.987, 10.88, 14.152, 11.28]}, {"time": 14.2667, "value": 11.28, "curve": [14.307, 11.28, 14.354, 8.26]}, {"time": 14.4, "value": 4.9, "curve": [14.477, -0.74, 14.59, 19.84]}, {"time": 14.6667, "value": 17.64, "curve": [14.75, 15.27, 14.817, -12.8]}, {"time": 14.9, "value": -12.14, "curve": [14.967, -11.61, 15.048, -3.92]}, {"time": 15.1333, "value": 1.63}]}, "bone5": {"rotate": [{"value": -1.04, "curve": [0.033, -1.04, 0.067, 1.86]}, {"time": 0.1, "value": 1.86, "curve": [0.278, 1.86, 0.505, 1.86]}, {"time": 0.6333, "value": -1.67, "curve": [0.747, -4.78, 0.833, -28.85]}, {"time": 0.9333, "value": -28.85, "curve": [1.111, -28.85, 1.289, 1.86]}, {"time": 1.4667, "value": 1.86, "curve": [1.656, 1.86, 1.905, 1.86]}, {"time": 2.0333, "value": -1.67, "curve": [2.045, -1.98, 2.056, -2.48]}, {"time": 2.0667, "value": -3.16, "curve": [2.168, -9.33, 2.267, -28.85]}, {"time": 2.3667, "value": -28.85, "curve": [2.533, -28.85, 2.7, 1.86]}, {"time": 2.8667, "value": 1.86, "curve": [3.044, 1.86, 3.271, 1.86]}, {"time": 3.4, "value": -1.67, "curve": [3.513, -4.78, 3.622, -28.85]}, {"time": 3.7333, "value": -28.85, "curve": [3.9, -28.85, 4.067, 1.86]}, {"time": 4.2333, "value": 1.86, "curve": [4.422, 1.86, 4.671, 1.86]}, {"time": 4.8, "value": -1.67, "curve": [4.913, -4.78, 5, -28.85]}, {"time": 5.1, "value": -28.85, "curve": [5.267, -28.85, 5.433, 1.86]}, {"time": 5.6, "value": 1.86, "curve": [5.789, 1.86, 6.038, 1.86]}, {"time": 6.1667, "value": -1.67, "curve": [6.28, -4.78, 6.367, -28.85]}, {"time": 6.4667, "value": -28.85, "curve": [6.644, -28.85, 6.822, 1.86]}, {"time": 7, "value": 1.86, "curve": [7.178, 1.86, 7.405, 1.86]}, {"time": 7.5333, "value": -1.67, "curve": [7.647, -4.78, 7.733, -28.85]}, {"time": 7.8333, "value": -28.85, "curve": [8.011, -28.85, 8.189, 1.86]}, {"time": 8.3667, "value": 1.86, "curve": [8.544, 1.86, 8.771, 1.86]}, {"time": 8.9, "value": -1.67, "curve": [9.013, -4.78, 9.1, -28.85]}, {"time": 9.2, "value": -28.85, "curve": [9.378, -28.85, 9.556, 1.86]}, {"time": 9.7333, "value": 1.86, "curve": [9.911, 1.86, 10.138, 1.86]}, {"time": 10.2667, "value": -1.67, "curve": [10.38, -4.78, 10.467, -28.85]}, {"time": 10.5667, "value": -28.85, "curve": [10.744, -28.85, 10.922, 1.86]}, {"time": 11.1, "value": 1.86, "curve": [11.278, 1.86, 11.505, 1.86]}, {"time": 11.6333, "value": -1.67, "curve": [11.747, -4.78, 11.856, -28.85]}, {"time": 11.9667, "value": -28.85, "curve": [12.133, -28.85, 12.3, 1.86]}, {"time": 12.4667, "value": 1.86, "curve": [12.656, 1.86, 12.905, 1.86]}, {"time": 13.0333, "value": -1.67, "curve": [13.147, -4.78, 13.233, -28.85]}, {"time": 13.3333, "value": -28.85, "curve": [13.5, -28.85, 13.667, 1.86]}, {"time": 13.8333, "value": 1.86, "curve": [14.022, 1.86, 14.271, 1.86]}, {"time": 14.4, "value": -1.67, "curve": [14.513, -4.78, 14.6, -28.85]}, {"time": 14.7, "value": -28.85, "curve": [14.844, -28.85, 14.989, -1.04]}, {"time": 15.1333, "value": -1.04}]}, "bone13": {"rotate": [{"value": -7.66, "curve": [0.059, -9.24, 0.118, -10.43]}, {"time": 0.1667, "value": -10.21, "curve": [0.344, -9.44, 0.473, -1.4]}, {"time": 0.6333, "value": 5.68, "curve": [0.674, 7.46, 0.723, 5.39]}, {"time": 0.7667, "value": 5.39, "curve": [0.927, 5.39, 1.068, 1.74]}, {"time": 1.2333, "value": -2.81, "curve": [1.336, -5.64, 1.437, -10.63]}, {"time": 1.5333, "value": -10.21, "curve": [1.711, -9.44, 1.873, -1.4]}, {"time": 2.0333, "value": 5.68, "curve": [2.044, 6.15, 2.055, 6.33]}, {"time": 2.0667, "value": 6.37, "curve": [2.099, 6.49, 2.134, 5.39]}, {"time": 2.1667, "value": 5.39, "curve": [2.327, 5.39, 2.468, 1.74]}, {"time": 2.6333, "value": -2.81, "curve": [2.736, -5.64, 2.837, -10.63]}, {"time": 2.9333, "value": -10.21, "curve": [3.111, -9.44, 3.239, -1.4]}, {"time": 3.4, "value": 5.68, "curve": [3.44, 7.46, 3.523, 5.39]}, {"time": 3.5667, "value": 5.39, "curve": [3.727, 5.39, 3.835, 1.74]}, {"time": 4, "value": -2.81, "curve": [4.103, -5.64, 4.204, -10.63]}, {"time": 4.3, "value": -10.21, "curve": [4.478, -9.44, 4.639, -1.4]}, {"time": 4.8, "value": 5.68, "curve": [4.84, 7.46, 4.889, 5.39]}, {"time": 4.9333, "value": 5.39, "curve": [5.093, 5.39, 5.201, 1.74]}, {"time": 5.3667, "value": -2.81, "curve": [5.469, -5.64, 5.571, -10.63]}, {"time": 5.6667, "value": -10.21, "curve": [5.844, -9.44, 6.006, -1.4]}, {"time": 6.1667, "value": 5.68, "curve": [6.207, 7.46, 6.256, 5.39]}, {"time": 6.3, "value": 5.39, "curve": [6.46, 5.39, 6.568, 1.74]}, {"time": 6.7333, "value": -2.81, "curve": [6.836, -5.64, 6.971, -10.63]}, {"time": 7.0667, "value": -10.21, "curve": [7.244, -9.44, 7.373, -1.4]}, {"time": 7.5333, "value": 5.68, "curve": [7.574, 7.46, 7.623, 5.39]}, {"time": 7.6667, "value": 5.39, "curve": [7.827, 5.39, 7.968, 1.74]}, {"time": 8.1333, "value": -2.81, "curve": [8.236, -5.64, 8.337, -10.63]}, {"time": 8.4333, "value": -10.21, "curve": [8.611, -9.44, 8.737, -1.49]}, {"time": 8.9, "value": 5.68, "curve": [8.94, 7.46, 8.989, 5.39]}, {"time": 9.0333, "value": 5.39, "curve": [9.193, 5.39, 9.335, 1.74]}, {"time": 9.5, "value": -2.81, "curve": [9.603, -5.64, 9.704, -10.63]}, {"time": 9.8, "value": -10.21, "curve": [9.978, -9.44, 10.106, -1.4]}, {"time": 10.2667, "value": 5.68, "curve": [10.307, 7.46, 10.356, 5.39]}, {"time": 10.4, "value": 5.39, "curve": [10.56, 5.39, 10.701, 1.74]}, {"time": 10.8667, "value": -2.81, "curve": [10.969, -5.64, 11.071, -10.63]}, {"time": 11.1667, "value": -10.21, "curve": [11.344, -9.44, 11.473, -1.4]}, {"time": 11.6333, "value": 5.68, "curve": [11.674, 7.46, 11.756, 5.39]}, {"time": 11.8, "value": 5.39, "curve": [11.96, 5.39, 12.068, 1.74]}, {"time": 12.2333, "value": -2.81, "curve": [12.336, -5.64, 12.437, -10.63]}, {"time": 12.5333, "value": -10.21, "curve": [12.711, -9.44, 12.873, -1.4]}, {"time": 13.0333, "value": 5.68, "curve": [13.074, 7.46, 13.123, 5.39]}, {"time": 13.1667, "value": 5.39, "curve": [13.327, 5.39, 13.435, 1.74]}, {"time": 13.6, "value": -2.81, "curve": [13.703, -5.64, 13.804, -10.63]}, {"time": 13.9, "value": -10.21, "curve": [14.078, -9.44, 14.239, -1.4]}, {"time": 14.4, "value": 5.68, "curve": [14.44, 7.46, 14.489, 5.39]}, {"time": 14.5333, "value": 5.39, "curve": [14.693, 5.39, 14.801, 1.74]}, {"time": 14.9667, "value": -2.81, "curve": [15.017, -4.2, 15.076, -6.12]}, {"time": 15.1333, "value": -7.66}]}, "bone12": {"rotate": [{"value": 1.63, "curve": [0.033, 3.69, 0.066, 5.54]}, {"time": 0.1, "value": 6.5, "curve": [0.253, 10.88, 0.385, 11.28]}, {"time": 0.5, "value": 11.28, "curve": [0.541, 11.28, 0.587, 8.26]}, {"time": 0.6333, "value": 4.9, "curve": [0.71, -0.74, 0.823, 19.84]}, {"time": 0.9, "value": 17.64, "curve": [0.983, 15.27, 1.084, -12.8]}, {"time": 1.1667, "value": -12.14, "curve": [1.259, -11.42, 1.342, 2.94]}, {"time": 1.4667, "value": 6.5, "curve": [1.62, 10.88, 1.785, 11.28]}, {"time": 1.9, "value": 11.28, "curve": [1.941, 11.28, 1.987, 8.26]}, {"time": 2.0333, "value": 4.9, "curve": [2.044, 4.13, 2.055, 3.98]}, {"time": 2.0667, "value": 4.06, "curve": [2.14, 4.59, 2.233, 19.54]}, {"time": 2.3, "value": 17.64, "curve": [2.383, 15.27, 2.484, -12.8]}, {"time": 2.5667, "value": -12.14, "curve": [2.659, -11.42, 2.742, 2.94]}, {"time": 2.8667, "value": 6.5, "curve": [3.02, 10.88, 3.152, 11.28]}, {"time": 3.2667, "value": 11.28, "curve": [3.307, 11.28, 3.354, 8.26]}, {"time": 3.4, "value": 4.9, "curve": [3.477, -0.74, 3.623, 19.84]}, {"time": 3.7, "value": 17.64, "curve": [3.783, 15.27, 3.85, -12.8]}, {"time": 3.9333, "value": -12.14, "curve": [4.026, -11.42, 4.108, 2.94]}, {"time": 4.2333, "value": 6.5, "curve": [4.387, 10.88, 4.552, 11.28]}, {"time": 4.6667, "value": 11.28, "curve": [4.707, 11.28, 4.754, 8.26]}, {"time": 4.8, "value": 4.9, "curve": [4.877, -0.74, 4.99, 19.84]}, {"time": 5.0667, "value": 17.64, "curve": [5.15, 15.27, 5.217, -12.8]}, {"time": 5.3, "value": -12.14, "curve": [5.392, -11.42, 5.475, 2.94]}, {"time": 5.6, "value": 6.5, "curve": [5.753, 10.88, 5.919, 11.28]}, {"time": 6.0333, "value": 11.28, "curve": [6.074, 11.28, 6.121, 8.26]}, {"time": 6.1667, "value": 4.9, "curve": [6.244, -0.74, 6.356, 19.84]}, {"time": 6.4333, "value": 17.64, "curve": [6.516, 15.27, 6.584, -12.8]}, {"time": 6.6667, "value": -12.14, "curve": [6.759, -11.42, 6.875, 2.94]}, {"time": 7, "value": 6.5, "curve": [7.153, 10.88, 7.285, 11.28]}, {"time": 7.4, "value": 11.28, "curve": [7.441, 11.28, 7.487, 8.26]}, {"time": 7.5333, "value": 4.9, "curve": [7.61, -0.74, 7.723, 19.84]}, {"time": 7.8, "value": 17.64, "curve": [7.883, 15.27, 7.984, -12.8]}, {"time": 8.0667, "value": -12.14, "curve": [8.159, -11.42, 8.242, 2.94]}, {"time": 8.3667, "value": 6.5, "curve": [8.52, 10.88, 8.652, 11.28]}, {"time": 8.7667, "value": 11.28, "curve": [8.807, 11.28, 8.859, 7.88]}, {"time": 8.9, "value": 4.9, "curve": [8.977, -0.74, 9.09, 19.84]}, {"time": 9.1667, "value": 17.64, "curve": [9.25, 15.27, 9.317, -12.8]}, {"time": 9.4, "value": -12.14, "curve": [9.492, -11.42, 9.608, 2.94]}, {"time": 9.7333, "value": 6.5, "curve": [9.887, 10.88, 10.019, 11.28]}, {"time": 10.1333, "value": 11.28, "curve": [10.174, 11.28, 10.221, 8.26]}, {"time": 10.2667, "value": 4.9, "curve": [10.344, -0.74, 10.456, 19.84]}, {"time": 10.5333, "value": 17.64, "curve": [10.616, 15.27, 10.717, -12.8]}, {"time": 10.8, "value": -12.14, "curve": [10.892, -11.42, 10.975, 2.94]}, {"time": 11.1, "value": 6.5, "curve": [11.253, 10.88, 11.385, 11.28]}, {"time": 11.5, "value": 11.28, "curve": [11.541, 11.28, 11.587, 8.26]}, {"time": 11.6333, "value": 4.9, "curve": [11.71, -0.74, 11.856, 19.84]}, {"time": 11.9333, "value": 17.64, "curve": [12.016, 15.27, 12.084, -12.8]}, {"time": 12.1667, "value": -12.14, "curve": [12.259, -11.42, 12.342, 2.94]}, {"time": 12.4667, "value": 6.5, "curve": [12.62, 10.88, 12.785, 11.28]}, {"time": 12.9, "value": 11.28, "curve": [12.941, 11.28, 12.987, 8.26]}, {"time": 13.0333, "value": 4.9, "curve": [13.11, -0.74, 13.223, 19.84]}, {"time": 13.3, "value": 17.64, "curve": [13.383, 15.27, 13.45, -12.8]}, {"time": 13.5333, "value": -12.14, "curve": [13.626, -11.42, 13.708, 2.94]}, {"time": 13.8333, "value": 6.5, "curve": [13.987, 10.88, 14.152, 11.28]}, {"time": 14.2667, "value": 11.28, "curve": [14.307, 11.28, 14.354, 8.26]}, {"time": 14.4, "value": 4.9, "curve": [14.477, -0.74, 14.59, 19.84]}, {"time": 14.6667, "value": 17.64, "curve": [14.75, 15.27, 14.817, -12.8]}, {"time": 14.9, "value": -12.14, "curve": [14.967, -11.61, 15.048, -3.92]}, {"time": 15.1333, "value": 1.63}]}, "bone11": {"rotate": [{"value": -1.04, "curve": [0.033, -1.04, 0.067, 1.86]}, {"time": 0.1, "value": 1.86, "curve": [0.278, 1.86, 0.505, 1.86]}, {"time": 0.6333, "value": -1.67, "curve": [0.747, -4.78, 0.833, -28.85]}, {"time": 0.9333, "value": -28.85, "curve": [1.111, -28.85, 1.289, 1.86]}, {"time": 1.4667, "value": 1.86, "curve": [1.656, 1.86, 1.905, 1.86]}, {"time": 2.0333, "value": -1.67, "curve": [2.045, -1.98, 2.056, -2.48]}, {"time": 2.0667, "value": -3.16, "curve": [2.168, -9.33, 2.267, -28.85]}, {"time": 2.3667, "value": -28.85, "curve": [2.533, -28.85, 2.7, 1.86]}, {"time": 2.8667, "value": 1.86, "curve": [3.044, 1.86, 3.271, 1.86]}, {"time": 3.4, "value": -1.67, "curve": [3.513, -4.78, 3.622, -28.85]}, {"time": 3.7333, "value": -28.85, "curve": [3.9, -28.85, 4.067, 1.86]}, {"time": 4.2333, "value": 1.86, "curve": [4.422, 1.86, 4.671, 1.86]}, {"time": 4.8, "value": -1.67, "curve": [4.913, -4.78, 5, -28.85]}, {"time": 5.1, "value": -28.85, "curve": [5.267, -28.85, 5.433, 1.86]}, {"time": 5.6, "value": 1.86, "curve": [5.789, 1.86, 6.038, 1.86]}, {"time": 6.1667, "value": -1.67, "curve": [6.28, -4.78, 6.367, -28.85]}, {"time": 6.4667, "value": -28.85, "curve": [6.644, -28.85, 6.822, 1.86]}, {"time": 7, "value": 1.86, "curve": [7.178, 1.86, 7.405, 1.86]}, {"time": 7.5333, "value": -1.67, "curve": [7.647, -4.78, 7.733, -28.85]}, {"time": 7.8333, "value": -28.85, "curve": [8.011, -28.85, 8.189, 1.86]}, {"time": 8.3667, "value": 1.86, "curve": [8.544, 1.86, 8.771, 1.86]}, {"time": 8.9, "value": -1.67, "curve": [9.013, -4.78, 9.1, -28.85]}, {"time": 9.2, "value": -28.85, "curve": [9.378, -28.85, 9.556, 1.86]}, {"time": 9.7333, "value": 1.86, "curve": [9.911, 1.86, 10.138, 1.86]}, {"time": 10.2667, "value": -1.67, "curve": [10.38, -4.78, 10.467, -28.85]}, {"time": 10.5667, "value": -28.85, "curve": [10.744, -28.85, 10.922, 1.86]}, {"time": 11.1, "value": 1.86, "curve": [11.278, 1.86, 11.505, 1.86]}, {"time": 11.6333, "value": -1.67, "curve": [11.747, -4.78, 11.856, -28.85]}, {"time": 11.9667, "value": -28.85, "curve": [12.133, -28.85, 12.3, 1.86]}, {"time": 12.4667, "value": 1.86, "curve": [12.656, 1.86, 12.905, 1.86]}, {"time": 13.0333, "value": -1.67, "curve": [13.147, -4.78, 13.233, -28.85]}, {"time": 13.3333, "value": -28.85, "curve": [13.5, -28.85, 13.667, 1.86]}, {"time": 13.8333, "value": 1.86, "curve": [14.022, 1.86, 14.271, 1.86]}, {"time": 14.4, "value": -1.67, "curve": [14.513, -4.78, 14.6, -28.85]}, {"time": 14.7, "value": -28.85, "curve": [14.844, -28.85, 14.989, -1.04]}, {"time": 15.1333, "value": -1.04}]}, "bird2": {"translate": [{"y": 3.53, "curve": [0.214, 0, 0.423, 0, 0.214, 2.48, 0.423, 0.51]}, {"time": 0.6333, "y": 0.51, "curve": [0.674, 0, 0.726, 0, 0.674, 0.51, 0.726, 0]}, {"time": 0.7667, "curve": [0.889, 0, 1.045, 0, 0.889, 0, 1.045, 4.19]}, {"time": 1.1667, "y": 4.19, "curve": [1.462, 0, 1.747, 0, 1.462, 4.19, 1.747, 0.51]}, {"time": 2.0333, "y": 0.51, "curve": [2.044, 0, 2.055, 0, 2.044, 0.51, 2.055, 0.47]}, {"time": 2.0667, "y": 0.42, "curve": [2.1, 0, 2.137, 0, 2.1, 0.28, 2.137, 0]}, {"time": 2.1667, "curve": [2.289, 0, 2.445, 0, 2.289, 0, 2.445, 4.19]}, {"time": 2.5667, "y": 4.19, "curve": [2.862, 0, 3.113, 0, 2.862, 4.19, 3.113, 0.51]}, {"time": 3.4, "y": 0.51, "curve": [3.441, 0, 3.526, 0, 3.441, 0.51, 3.526, 0]}, {"time": 3.5667, "curve": [3.689, 0, 3.811, 0, 3.689, 0, 3.811, 4.19]}, {"time": 3.9333, "y": 4.19, "curve": [4.228, 0, 4.513, 0, 4.228, 4.19, 4.513, 0.51]}, {"time": 4.8, "y": 0.51, "curve": [4.841, 0, 4.893, 0, 4.841, 0.51, 4.893, 0]}, {"time": 4.9333, "curve": [5.055, 0, 5.178, 0, 5.055, 0, 5.178, 4.19]}, {"time": 5.3, "y": 4.19, "curve": [5.595, 0, 5.88, 0, 5.595, 4.19, 5.88, 0.51]}, {"time": 6.1667, "y": 0.51, "curve": [6.207, 0, 6.259, 0, 6.207, 0.51, 6.259, 0]}, {"time": 6.3, "curve": [6.422, 0, 6.545, 0, 6.422, 0, 6.545, 4.19]}, {"time": 6.6667, "y": 4.19, "curve": [6.962, 0, 7.247, 0, 6.962, 4.19, 7.247, 0.51]}, {"time": 7.5333, "y": 0.51, "curve": [7.574, 0, 7.626, 0, 7.574, 0.51, 7.626, 0]}, {"time": 7.6667, "curve": [7.789, 0, 7.945, 0, 7.789, 0, 7.945, 4.19]}, {"time": 8.0667, "y": 4.19, "curve": [8.362, 0, 8.605, 0, 8.362, 4.19, 8.605, 0.51]}, {"time": 8.9, "y": 0.51, "curve": [8.941, 0, 8.993, 0, 8.941, 0.51, 8.993, 0]}, {"time": 9.0333, "curve": [9.155, 0, 9.278, 0, 9.155, 0, 9.278, 4.19]}, {"time": 9.4, "y": 4.19, "curve": [9.695, 0, 9.98, 0, 9.695, 4.19, 9.98, 0.51]}, {"time": 10.2667, "y": 0.51, "curve": [10.307, 0, 10.359, 0, 10.307, 0.51, 10.359, 0]}, {"time": 10.4, "curve": [10.522, 0, 10.678, 0, 10.522, 0, 10.678, 4.19]}, {"time": 10.8, "y": 4.19, "curve": [11.095, 0, 11.347, 0, 11.095, 4.19, 11.347, 0.51]}, {"time": 11.6333, "y": 0.51, "curve": [11.674, 0, 11.759, 0, 11.674, 0.51, 11.759, 0]}, {"time": 11.8, "curve": [11.922, 0, 12.045, 0, 11.922, 0, 12.045, 4.19]}, {"time": 12.1667, "y": 4.19, "curve": [12.462, 0, 12.747, 0, 12.462, 4.19, 12.747, 0.51]}, {"time": 13.0333, "y": 0.51, "curve": [13.074, 0, 13.126, 0, 13.074, 0.51, 13.126, 0]}, {"time": 13.1667, "curve": [13.289, 0, 13.411, 0, 13.289, 0, 13.411, 4.19]}, {"time": 13.5333, "y": 4.19, "curve": [13.828, 0, 14.113, 0, 13.828, 4.19, 14.113, 0.51]}, {"time": 14.4, "y": 0.51, "curve": [14.441, 0, 14.493, 0, 14.441, 0.51, 14.493, 0]}, {"time": 14.5333, "curve": [14.655, 0, 14.778, 0, 14.655, 0, 14.778, 4.19]}, {"time": 14.9, "y": 4.19, "curve": [14.979, 0, 15.057, 0, 14.979, 4.19, 15.057, 3.91]}, {"time": 15.1333, "y": 3.53}]}, "bone16": {"rotate": [{"value": 0.89, "curve": [0.042, -0.21, 0.085, -1.49]}, {"time": 0.1333, "value": -2.81, "curve": [0.236, -5.64, 0.337, -10.63]}, {"time": 0.4333, "value": -10.21, "curve": [0.611, -9.44, 0.773, -1.4]}, {"time": 0.9333, "value": 5.68, "curve": [0.974, 7.46, 1.023, 5.39]}, {"time": 1.0667, "value": 5.39, "curve": [1.227, 5.39, 1.335, 1.74]}, {"time": 1.5, "value": -2.81, "curve": [1.603, -5.64, 1.737, -10.63]}, {"time": 1.8333, "value": -10.21, "curve": [2.011, -9.44, 2.139, -1.4]}, {"time": 2.3, "value": 5.68, "curve": [2.34, 7.46, 2.389, 5.39]}, {"time": 2.4333, "value": 5.39, "curve": [2.593, 5.39, 2.735, 1.74]}, {"time": 2.9, "value": -2.81, "curve": [3.003, -5.64, 3.104, -10.63]}, {"time": 3.2, "value": -10.21, "curve": [3.378, -9.44, 3.504, -1.49]}, {"time": 3.6667, "value": 5.68, "curve": [3.707, 7.46, 3.756, 5.39]}, {"time": 3.8, "value": 5.39, "curve": [3.96, 5.39, 4.101, 1.74]}, {"time": 4.2667, "value": -2.81, "curve": [4.369, -5.64, 4.471, -10.63]}, {"time": 4.5667, "value": -10.21, "curve": [4.744, -9.44, 4.873, -1.4]}, {"time": 5.0333, "value": 5.68, "curve": [5.074, 7.46, 5.123, 5.39]}, {"time": 5.1667, "value": 5.39, "curve": [5.327, 5.39, 5.468, 1.74]}, {"time": 5.6333, "value": -2.81, "curve": [5.736, -5.64, 5.837, -10.63]}, {"time": 5.9333, "value": -10.21, "curve": [6.111, -9.44, 6.239, -1.4]}, {"time": 6.4, "value": 5.68, "curve": [6.44, 7.46, 6.523, 5.39]}, {"time": 6.5667, "value": 5.39, "curve": [6.727, 5.39, 6.835, 1.74]}, {"time": 7, "value": -2.81, "curve": [7.103, -5.64, 7.204, -10.63]}, {"time": 7.3, "value": -10.21, "curve": [7.478, -9.44, 7.639, -1.4]}, {"time": 7.8, "value": 5.68, "curve": [7.84, 7.46, 7.889, 5.39]}, {"time": 7.9333, "value": 5.39, "curve": [8.093, 5.39, 8.201, 1.74]}, {"time": 8.3667, "value": -2.81, "curve": [8.469, -5.64, 8.571, -10.63]}, {"time": 8.6667, "value": -10.21, "curve": [8.844, -9.44, 9.006, -1.4]}, {"time": 9.1667, "value": 5.68, "curve": [9.207, 7.46, 9.256, 5.39]}, {"time": 9.3, "value": 5.39, "curve": [9.46, 5.39, 9.568, 1.74]}, {"time": 9.7333, "value": -2.81, "curve": [9.836, -5.64, 9.971, -10.63]}, {"time": 10.0667, "value": -10.21, "curve": [10.244, -9.44, 10.373, -1.4]}, {"time": 10.5333, "value": 5.68, "curve": [10.574, 7.46, 10.623, 5.39]}, {"time": 10.6667, "value": 5.39, "curve": [10.827, 5.39, 10.968, 1.74]}, {"time": 11.1333, "value": -2.81, "curve": [11.236, -5.64, 11.337, -10.63]}, {"time": 11.4333, "value": -10.21, "curve": [11.611, -9.44, 11.773, -1.4]}, {"time": 11.9333, "value": 5.68, "curve": [11.974, 7.46, 12.023, 5.39]}, {"time": 12.0667, "value": 5.39, "curve": [12.227, 5.39, 12.368, 1.74]}, {"time": 12.5333, "value": -2.81, "curve": [12.636, -5.64, 12.737, -10.63]}, {"time": 12.8333, "value": -10.21, "curve": [13.011, -9.44, 13.139, -1.4]}, {"time": 13.3, "value": 5.68, "curve": [13.34, 7.46, 13.423, 5.39]}, {"time": 13.4667, "value": 5.39, "curve": [13.627, 5.39, 13.735, 1.74]}, {"time": 13.9, "value": -2.81, "curve": [14.003, -5.64, 14.104, -10.63]}, {"time": 14.2, "value": -10.21, "curve": [14.378, -9.44, 14.539, -1.4]}, {"time": 14.7, "value": 5.68, "curve": [14.74, 7.46, 14.789, 5.39]}, {"time": 14.8333, "value": 5.39, "curve": [14.947, 5.39, 15.034, 3.59]}, {"time": 15.1333, "value": 0.89}]}, "bone15": {"rotate": [{"value": -7.06, "curve": [0.022, -10.12, 0.044, -12.32]}, {"time": 0.0667, "value": -12.14, "curve": [0.159, -11.42, 0.242, 2.94]}, {"time": 0.3667, "value": 6.5, "curve": [0.52, 10.88, 0.685, 11.28]}, {"time": 0.8, "value": 11.28, "curve": [0.841, 11.28, 0.887, 8.26]}, {"time": 0.9333, "value": 4.9, "curve": [1.01, -0.74, 1.123, 19.84]}, {"time": 1.2, "value": 17.64, "curve": [1.283, 15.27, 1.35, -12.8]}, {"time": 1.4333, "value": -12.14, "curve": [1.526, -11.42, 1.642, 2.94]}, {"time": 1.7667, "value": 6.5, "curve": [1.92, 10.88, 2.052, 11.28]}, {"time": 2.1667, "value": 11.28, "curve": [2.207, 11.28, 2.254, 8.26]}, {"time": 2.3, "value": 4.9, "curve": [2.377, -0.74, 2.49, 19.84]}, {"time": 2.5667, "value": 17.64, "curve": [2.65, 15.27, 2.75, -12.8]}, {"time": 2.8333, "value": -12.14, "curve": [2.926, -11.42, 3.008, 2.94]}, {"time": 3.1333, "value": 6.5, "curve": [3.287, 10.88, 3.419, 11.28]}, {"time": 3.5333, "value": 11.28, "curve": [3.574, 11.28, 3.626, 7.88]}, {"time": 3.6667, "value": 4.9, "curve": [3.744, -0.74, 3.856, 19.84]}, {"time": 3.9333, "value": 17.64, "curve": [4.016, 15.27, 4.084, -12.8]}, {"time": 4.1667, "value": -12.14, "curve": [4.259, -11.42, 4.375, 2.94]}, {"time": 4.5, "value": 6.5, "curve": [4.653, 10.88, 4.785, 11.28]}, {"time": 4.9, "value": 11.28, "curve": [4.941, 11.28, 4.987, 8.26]}, {"time": 5.0333, "value": 4.9, "curve": [5.11, -0.74, 5.223, 19.84]}, {"time": 5.3, "value": 17.64, "curve": [5.383, 15.27, 5.484, -12.8]}, {"time": 5.5667, "value": -12.14, "curve": [5.659, -11.42, 5.742, 2.94]}, {"time": 5.8667, "value": 6.5, "curve": [6.02, 10.88, 6.152, 11.28]}, {"time": 6.2667, "value": 11.28, "curve": [6.307, 11.28, 6.354, 8.26]}, {"time": 6.4, "value": 4.9, "curve": [6.477, -0.74, 6.623, 19.84]}, {"time": 6.7, "value": 17.64, "curve": [6.783, 15.27, 6.85, -12.8]}, {"time": 6.9333, "value": -12.14, "curve": [7.026, -11.42, 7.108, 2.94]}, {"time": 7.2333, "value": 6.5, "curve": [7.387, 10.88, 7.552, 11.28]}, {"time": 7.6667, "value": 11.28, "curve": [7.707, 11.28, 7.754, 8.26]}, {"time": 7.8, "value": 4.9, "curve": [7.877, -0.74, 7.99, 19.84]}, {"time": 8.0667, "value": 17.64, "curve": [8.15, 15.27, 8.217, -12.8]}, {"time": 8.3, "value": -12.14, "curve": [8.392, -11.42, 8.475, 2.94]}, {"time": 8.6, "value": 6.5, "curve": [8.753, 10.88, 8.919, 11.28]}, {"time": 9.0333, "value": 11.28, "curve": [9.074, 11.28, 9.121, 8.26]}, {"time": 9.1667, "value": 4.9, "curve": [9.244, -0.74, 9.356, 19.84]}, {"time": 9.4333, "value": 17.64, "curve": [9.516, 15.27, 9.584, -12.8]}, {"time": 9.6667, "value": -12.14, "curve": [9.759, -11.42, 9.875, 2.94]}, {"time": 10, "value": 6.5, "curve": [10.153, 10.88, 10.285, 11.28]}, {"time": 10.4, "value": 11.28, "curve": [10.441, 11.28, 10.487, 8.26]}, {"time": 10.5333, "value": 4.9, "curve": [10.61, -0.74, 10.723, 19.84]}, {"time": 10.8, "value": 17.64, "curve": [10.883, 15.27, 10.984, -12.8]}, {"time": 11.0667, "value": -12.14, "curve": [11.159, -11.42, 11.242, 2.94]}, {"time": 11.3667, "value": 6.5, "curve": [11.52, 10.88, 11.685, 11.28]}, {"time": 11.8, "value": 11.28, "curve": [11.841, 11.28, 11.887, 8.26]}, {"time": 11.9333, "value": 4.9, "curve": [12.01, -0.74, 12.123, 19.84]}, {"time": 12.2, "value": 17.64, "curve": [12.283, 15.27, 12.384, -12.8]}, {"time": 12.4667, "value": -12.14, "curve": [12.559, -11.42, 12.642, 2.94]}, {"time": 12.7667, "value": 6.5, "curve": [12.92, 10.88, 13.052, 11.28]}, {"time": 13.1667, "value": 11.28, "curve": [13.207, 11.28, 13.254, 8.26]}, {"time": 13.3, "value": 4.9, "curve": [13.377, -0.74, 13.523, 19.84]}, {"time": 13.6, "value": 17.64, "curve": [13.683, 15.27, 13.75, -12.8]}, {"time": 13.8333, "value": -12.14, "curve": [13.926, -11.42, 14.008, 2.94]}, {"time": 14.1333, "value": 6.5, "curve": [14.287, 10.88, 14.452, 11.28]}, {"time": 14.5667, "value": 11.28, "curve": [14.607, 11.28, 14.654, 8.26]}, {"time": 14.7, "value": 4.9, "curve": [14.777, -0.74, 14.89, 19.84]}, {"time": 14.9667, "value": 17.64, "curve": [15.027, 15.93, 15.079, 0.95]}, {"time": 15.1333, "value": -7.06}]}, "bone14": {"rotate": [{"value": -23.37, "curve": [0.122, -23.37, 0.244, 1.86]}, {"time": 0.3667, "value": 1.86, "curve": [0.556, 1.86, 0.805, 1.86]}, {"time": 0.9333, "value": -1.67, "curve": [1.047, -4.78, 1.133, -28.85]}, {"time": 1.2333, "value": -28.85, "curve": [1.411, -28.85, 1.589, 1.86]}, {"time": 1.7667, "value": 1.86, "curve": [1.944, 1.86, 2.171, 1.86]}, {"time": 2.3, "value": -1.67, "curve": [2.413, -4.78, 2.5, -28.85]}, {"time": 2.6, "value": -28.85, "curve": [2.778, -28.85, 2.956, 1.86]}, {"time": 3.1333, "value": 1.86, "curve": [3.311, 1.86, 3.538, 1.86]}, {"time": 3.6667, "value": -1.67, "curve": [3.78, -4.78, 3.867, -28.85]}, {"time": 3.9667, "value": -28.85, "curve": [4.144, -28.85, 4.322, 1.86]}, {"time": 4.5, "value": 1.86, "curve": [4.678, 1.86, 4.905, 1.86]}, {"time": 5.0333, "value": -1.67, "curve": [5.147, -4.78, 5.233, -28.85]}, {"time": 5.3333, "value": -28.85, "curve": [5.511, -28.85, 5.689, 1.86]}, {"time": 5.8667, "value": 1.86, "curve": [6.044, 1.86, 6.271, 1.86]}, {"time": 6.4, "value": -1.67, "curve": [6.513, -4.78, 6.622, -28.85]}, {"time": 6.7333, "value": -28.85, "curve": [6.9, -28.85, 7.067, 1.86]}, {"time": 7.2333, "value": 1.86, "curve": [7.422, 1.86, 7.671, 1.86]}, {"time": 7.8, "value": -1.67, "curve": [7.913, -4.78, 8, -28.85]}, {"time": 8.1, "value": -28.85, "curve": [8.267, -28.85, 8.433, 1.86]}, {"time": 8.6, "value": 1.86, "curve": [8.789, 1.86, 9.038, 1.86]}, {"time": 9.1667, "value": -1.67, "curve": [9.28, -4.78, 9.367, -28.85]}, {"time": 9.4667, "value": -28.85, "curve": [9.644, -28.85, 9.822, 1.86]}, {"time": 10, "value": 1.86, "curve": [10.178, 1.86, 10.405, 1.86]}, {"time": 10.5333, "value": -1.67, "curve": [10.647, -4.78, 10.733, -28.85]}, {"time": 10.8333, "value": -28.85, "curve": [11.011, -28.85, 11.189, 1.86]}, {"time": 11.3667, "value": 1.86, "curve": [11.556, 1.86, 11.805, 1.86]}, {"time": 11.9333, "value": -1.67, "curve": [12.047, -4.78, 12.156, -28.85]}, {"time": 12.2667, "value": -28.85, "curve": [12.433, -28.85, 12.6, 1.86]}, {"time": 12.7667, "value": 1.86, "curve": [12.944, 1.86, 13.171, 1.86]}, {"time": 13.3, "value": -1.67, "curve": [13.413, -4.78, 13.522, -28.85]}, {"time": 13.6333, "value": -28.85, "curve": [13.8, -28.85, 13.967, 1.86]}, {"time": 14.1333, "value": 1.86, "curve": [14.322, 1.86, 14.571, 1.86]}, {"time": 14.7, "value": -1.67, "curve": [14.813, -4.78, 14.9, -28.85]}, {"time": 15, "value": -28.85, "curve": [15.044, -28.85, 15.089, -23.37]}, {"time": 15.1333, "value": -23.37}]}, "bone19": {"rotate": [{"value": 0.89, "curve": [0.042, -0.21, 0.085, -1.49]}, {"time": 0.1333, "value": -2.81, "curve": [0.236, -5.64, 0.337, -10.63]}, {"time": 0.4333, "value": -10.21, "curve": [0.611, -9.44, 0.773, -1.4]}, {"time": 0.9333, "value": 5.68, "curve": [0.974, 7.46, 1.023, 5.39]}, {"time": 1.0667, "value": 5.39, "curve": [1.227, 5.39, 1.335, 1.74]}, {"time": 1.5, "value": -2.81, "curve": [1.603, -5.64, 1.737, -10.63]}, {"time": 1.8333, "value": -10.21, "curve": [2.011, -9.44, 2.139, -1.4]}, {"time": 2.3, "value": 5.68, "curve": [2.34, 7.46, 2.389, 5.39]}, {"time": 2.4333, "value": 5.39, "curve": [2.593, 5.39, 2.735, 1.74]}, {"time": 2.9, "value": -2.81, "curve": [3.003, -5.64, 3.104, -10.63]}, {"time": 3.2, "value": -10.21, "curve": [3.378, -9.44, 3.504, -1.49]}, {"time": 3.6667, "value": 5.68, "curve": [3.707, 7.46, 3.756, 5.39]}, {"time": 3.8, "value": 5.39, "curve": [3.96, 5.39, 4.101, 1.74]}, {"time": 4.2667, "value": -2.81, "curve": [4.369, -5.64, 4.471, -10.63]}, {"time": 4.5667, "value": -10.21, "curve": [4.744, -9.44, 4.873, -1.4]}, {"time": 5.0333, "value": 5.68, "curve": [5.074, 7.46, 5.123, 5.39]}, {"time": 5.1667, "value": 5.39, "curve": [5.327, 5.39, 5.468, 1.74]}, {"time": 5.6333, "value": -2.81, "curve": [5.736, -5.64, 5.837, -10.63]}, {"time": 5.9333, "value": -10.21, "curve": [6.111, -9.44, 6.239, -1.4]}, {"time": 6.4, "value": 5.68, "curve": [6.44, 7.46, 6.523, 5.39]}, {"time": 6.5667, "value": 5.39, "curve": [6.727, 5.39, 6.835, 1.74]}, {"time": 7, "value": -2.81, "curve": [7.103, -5.64, 7.204, -10.63]}, {"time": 7.3, "value": -10.21, "curve": [7.478, -9.44, 7.639, -1.4]}, {"time": 7.8, "value": 5.68, "curve": [7.84, 7.46, 7.889, 5.39]}, {"time": 7.9333, "value": 5.39, "curve": [8.093, 5.39, 8.201, 1.74]}, {"time": 8.3667, "value": -2.81, "curve": [8.469, -5.64, 8.571, -10.63]}, {"time": 8.6667, "value": -10.21, "curve": [8.844, -9.44, 9.006, -1.4]}, {"time": 9.1667, "value": 5.68, "curve": [9.207, 7.46, 9.256, 5.39]}, {"time": 9.3, "value": 5.39, "curve": [9.46, 5.39, 9.568, 1.74]}, {"time": 9.7333, "value": -2.81, "curve": [9.836, -5.64, 9.971, -10.63]}, {"time": 10.0667, "value": -10.21, "curve": [10.244, -9.44, 10.373, -1.4]}, {"time": 10.5333, "value": 5.68, "curve": [10.574, 7.46, 10.623, 5.39]}, {"time": 10.6667, "value": 5.39, "curve": [10.827, 5.39, 10.968, 1.74]}, {"time": 11.1333, "value": -2.81, "curve": [11.236, -5.64, 11.337, -10.63]}, {"time": 11.4333, "value": -10.21, "curve": [11.611, -9.44, 11.773, -1.4]}, {"time": 11.9333, "value": 5.68, "curve": [11.974, 7.46, 12.023, 5.39]}, {"time": 12.0667, "value": 5.39, "curve": [12.227, 5.39, 12.368, 1.74]}, {"time": 12.5333, "value": -2.81, "curve": [12.636, -5.64, 12.737, -10.63]}, {"time": 12.8333, "value": -10.21, "curve": [13.011, -9.44, 13.139, -1.4]}, {"time": 13.3, "value": 5.68, "curve": [13.34, 7.46, 13.423, 5.39]}, {"time": 13.4667, "value": 5.39, "curve": [13.627, 5.39, 13.735, 1.74]}, {"time": 13.9, "value": -2.81, "curve": [14.003, -5.64, 14.104, -10.63]}, {"time": 14.2, "value": -10.21, "curve": [14.378, -9.44, 14.539, -1.4]}, {"time": 14.7, "value": 5.68, "curve": [14.74, 7.46, 14.789, 5.39]}, {"time": 14.8333, "value": 5.39, "curve": [14.947, 5.39, 15.034, 3.59]}, {"time": 15.1333, "value": 0.89}]}, "bone18": {"rotate": [{"value": -7.06, "curve": [0.022, -10.12, 0.044, -12.32]}, {"time": 0.0667, "value": -12.14, "curve": [0.159, -11.42, 0.242, 2.94]}, {"time": 0.3667, "value": 6.5, "curve": [0.52, 10.88, 0.685, 11.28]}, {"time": 0.8, "value": 11.28, "curve": [0.841, 11.28, 0.887, 8.26]}, {"time": 0.9333, "value": 4.9, "curve": [1.01, -0.74, 1.123, 19.84]}, {"time": 1.2, "value": 17.64, "curve": [1.283, 15.27, 1.35, -12.8]}, {"time": 1.4333, "value": -12.14, "curve": [1.526, -11.42, 1.642, 2.94]}, {"time": 1.7667, "value": 6.5, "curve": [1.92, 10.88, 2.052, 11.28]}, {"time": 2.1667, "value": 11.28, "curve": [2.207, 11.28, 2.254, 8.26]}, {"time": 2.3, "value": 4.9, "curve": [2.377, -0.74, 2.49, 19.84]}, {"time": 2.5667, "value": 17.64, "curve": [2.65, 15.27, 2.75, -12.8]}, {"time": 2.8333, "value": -12.14, "curve": [2.926, -11.42, 3.008, 2.94]}, {"time": 3.1333, "value": 6.5, "curve": [3.287, 10.88, 3.419, 11.28]}, {"time": 3.5333, "value": 11.28, "curve": [3.574, 11.28, 3.626, 7.88]}, {"time": 3.6667, "value": 4.9, "curve": [3.744, -0.74, 3.856, 19.84]}, {"time": 3.9333, "value": 17.64, "curve": [4.016, 15.27, 4.084, -12.8]}, {"time": 4.1667, "value": -12.14, "curve": [4.259, -11.42, 4.375, 2.94]}, {"time": 4.5, "value": 6.5, "curve": [4.653, 10.88, 4.785, 11.28]}, {"time": 4.9, "value": 11.28, "curve": [4.941, 11.28, 4.987, 8.26]}, {"time": 5.0333, "value": 4.9, "curve": [5.11, -0.74, 5.223, 19.84]}, {"time": 5.3, "value": 17.64, "curve": [5.383, 15.27, 5.484, -12.8]}, {"time": 5.5667, "value": -12.14, "curve": [5.659, -11.42, 5.742, 2.94]}, {"time": 5.8667, "value": 6.5, "curve": [6.02, 10.88, 6.152, 11.28]}, {"time": 6.2667, "value": 11.28, "curve": [6.307, 11.28, 6.354, 8.26]}, {"time": 6.4, "value": 4.9, "curve": [6.477, -0.74, 6.623, 19.84]}, {"time": 6.7, "value": 17.64, "curve": [6.783, 15.27, 6.85, -12.8]}, {"time": 6.9333, "value": -12.14, "curve": [7.026, -11.42, 7.108, 2.94]}, {"time": 7.2333, "value": 6.5, "curve": [7.387, 10.88, 7.552, 11.28]}, {"time": 7.6667, "value": 11.28, "curve": [7.707, 11.28, 7.754, 8.26]}, {"time": 7.8, "value": 4.9, "curve": [7.877, -0.74, 7.99, 19.84]}, {"time": 8.0667, "value": 17.64, "curve": [8.15, 15.27, 8.217, -12.8]}, {"time": 8.3, "value": -12.14, "curve": [8.392, -11.42, 8.475, 2.94]}, {"time": 8.6, "value": 6.5, "curve": [8.753, 10.88, 8.919, 11.28]}, {"time": 9.0333, "value": 11.28, "curve": [9.074, 11.28, 9.121, 8.26]}, {"time": 9.1667, "value": 4.9, "curve": [9.244, -0.74, 9.356, 19.84]}, {"time": 9.4333, "value": 17.64, "curve": [9.516, 15.27, 9.584, -12.8]}, {"time": 9.6667, "value": -12.14, "curve": [9.759, -11.42, 9.875, 2.94]}, {"time": 10, "value": 6.5, "curve": [10.153, 10.88, 10.285, 11.28]}, {"time": 10.4, "value": 11.28, "curve": [10.441, 11.28, 10.487, 8.26]}, {"time": 10.5333, "value": 4.9, "curve": [10.61, -0.74, 10.723, 19.84]}, {"time": 10.8, "value": 17.64, "curve": [10.883, 15.27, 10.984, -12.8]}, {"time": 11.0667, "value": -12.14, "curve": [11.159, -11.42, 11.242, 2.94]}, {"time": 11.3667, "value": 6.5, "curve": [11.52, 10.88, 11.685, 11.28]}, {"time": 11.8, "value": 11.28, "curve": [11.841, 11.28, 11.887, 8.26]}, {"time": 11.9333, "value": 4.9, "curve": [12.01, -0.74, 12.123, 19.84]}, {"time": 12.2, "value": 17.64, "curve": [12.283, 15.27, 12.384, -12.8]}, {"time": 12.4667, "value": -12.14, "curve": [12.559, -11.42, 12.642, 2.94]}, {"time": 12.7667, "value": 6.5, "curve": [12.92, 10.88, 13.052, 11.28]}, {"time": 13.1667, "value": 11.28, "curve": [13.207, 11.28, 13.254, 8.26]}, {"time": 13.3, "value": 4.9, "curve": [13.377, -0.74, 13.523, 19.84]}, {"time": 13.6, "value": 17.64, "curve": [13.683, 15.27, 13.75, -12.8]}, {"time": 13.8333, "value": -12.14, "curve": [13.926, -11.42, 14.008, 2.94]}, {"time": 14.1333, "value": 6.5, "curve": [14.287, 10.88, 14.452, 11.28]}, {"time": 14.5667, "value": 11.28, "curve": [14.607, 11.28, 14.654, 8.26]}, {"time": 14.7, "value": 4.9, "curve": [14.777, -0.74, 14.89, 19.84]}, {"time": 14.9667, "value": 17.64, "curve": [15.027, 15.93, 15.079, 0.95]}, {"time": 15.1333, "value": -7.06}]}, "bone17": {"rotate": [{"value": -23.37, "curve": [0.122, -23.37, 0.244, 1.86]}, {"time": 0.3667, "value": 1.86, "curve": [0.556, 1.86, 0.805, 1.86]}, {"time": 0.9333, "value": -1.67, "curve": [1.047, -4.78, 1.133, -28.85]}, {"time": 1.2333, "value": -28.85, "curve": [1.411, -28.85, 1.589, 1.86]}, {"time": 1.7667, "value": 1.86, "curve": [1.944, 1.86, 2.171, 1.86]}, {"time": 2.3, "value": -1.67, "curve": [2.413, -4.78, 2.5, -28.85]}, {"time": 2.6, "value": -28.85, "curve": [2.778, -28.85, 2.956, 1.86]}, {"time": 3.1333, "value": 1.86, "curve": [3.311, 1.86, 3.538, 1.86]}, {"time": 3.6667, "value": -1.67, "curve": [3.78, -4.78, 3.867, -28.85]}, {"time": 3.9667, "value": -28.85, "curve": [4.144, -28.85, 4.322, 1.86]}, {"time": 4.5, "value": 1.86, "curve": [4.678, 1.86, 4.905, 1.86]}, {"time": 5.0333, "value": -1.67, "curve": [5.147, -4.78, 5.233, -28.85]}, {"time": 5.3333, "value": -28.85, "curve": [5.511, -28.85, 5.689, 1.86]}, {"time": 5.8667, "value": 1.86, "curve": [6.044, 1.86, 6.271, 1.86]}, {"time": 6.4, "value": -1.67, "curve": [6.513, -4.78, 6.622, -28.85]}, {"time": 6.7333, "value": -28.85, "curve": [6.9, -28.85, 7.067, 1.86]}, {"time": 7.2333, "value": 1.86, "curve": [7.422, 1.86, 7.671, 1.86]}, {"time": 7.8, "value": -1.67, "curve": [7.913, -4.78, 8, -28.85]}, {"time": 8.1, "value": -28.85, "curve": [8.267, -28.85, 8.433, 1.86]}, {"time": 8.6, "value": 1.86, "curve": [8.789, 1.86, 9.038, 1.86]}, {"time": 9.1667, "value": -1.67, "curve": [9.28, -4.78, 9.367, -28.85]}, {"time": 9.4667, "value": -28.85, "curve": [9.644, -28.85, 9.822, 1.86]}, {"time": 10, "value": 1.86, "curve": [10.178, 1.86, 10.405, 1.86]}, {"time": 10.5333, "value": -1.67, "curve": [10.647, -4.78, 10.733, -28.85]}, {"time": 10.8333, "value": -28.85, "curve": [11.011, -28.85, 11.189, 1.86]}, {"time": 11.3667, "value": 1.86, "curve": [11.556, 1.86, 11.805, 1.86]}, {"time": 11.9333, "value": -1.67, "curve": [12.047, -4.78, 12.156, -28.85]}, {"time": 12.2667, "value": -28.85, "curve": [12.433, -28.85, 12.6, 1.86]}, {"time": 12.7667, "value": 1.86, "curve": [12.944, 1.86, 13.171, 1.86]}, {"time": 13.3, "value": -1.67, "curve": [13.413, -4.78, 13.522, -28.85]}, {"time": 13.6333, "value": -28.85, "curve": [13.8, -28.85, 13.967, 1.86]}, {"time": 14.1333, "value": 1.86, "curve": [14.322, 1.86, 14.571, 1.86]}, {"time": 14.7, "value": -1.67, "curve": [14.813, -4.78, 14.9, -28.85]}, {"time": 15, "value": -28.85, "curve": [15.044, -28.85, 15.089, -23.37]}, {"time": 15.1333, "value": -23.37}]}, "bird3": {"translate": [{"y": 3.81, "curve": [0.023, 0, 0.045, 0, 0.023, 4.03, 0.045, 4.19]}, {"time": 0.0667, "y": 4.19, "curve": [0.362, 0, 0.647, 0, 0.362, 4.19, 0.647, 0.51]}, {"time": 0.9333, "y": 0.51, "curve": [0.974, 0, 1.026, 0, 0.974, 0.51, 1.026, 0]}, {"time": 1.0667, "curve": [1.189, 0, 1.311, 0, 1.189, 0, 1.311, 4.19]}, {"time": 1.4333, "y": 4.19, "curve": [1.728, 0, 2.013, 0, 1.728, 4.19, 2.013, 0.51]}, {"time": 2.3, "y": 0.51, "curve": [2.341, 0, 2.393, 0, 2.341, 0.51, 2.393, 0]}, {"time": 2.4333, "curve": [2.555, 0, 2.711, 0, 2.555, 0, 2.711, 4.19]}, {"time": 2.8333, "y": 4.19, "curve": [3.128, 0, 3.372, 0, 3.128, 4.19, 3.372, 0.51]}, {"time": 3.6667, "y": 0.51, "curve": [3.707, 0, 3.759, 0, 3.707, 0.51, 3.759, 0]}, {"time": 3.8, "curve": [3.922, 0, 4.045, 0, 3.922, 0, 4.045, 4.19]}, {"time": 4.1667, "y": 4.19, "curve": [4.462, 0, 4.747, 0, 4.462, 4.19, 4.747, 0.51]}, {"time": 5.0333, "y": 0.51, "curve": [5.074, 0, 5.126, 0, 5.074, 0.51, 5.126, 0]}, {"time": 5.1667, "curve": [5.289, 0, 5.445, 0, 5.289, 0, 5.445, 4.19]}, {"time": 5.5667, "y": 4.19, "curve": [5.862, 0, 6.113, 0, 5.862, 4.19, 6.113, 0.51]}, {"time": 6.4, "y": 0.51, "curve": [6.441, 0, 6.526, 0, 6.441, 0.51, 6.526, 0]}, {"time": 6.5667, "curve": [6.689, 0, 6.811, 0, 6.689, 0, 6.811, 4.19]}, {"time": 6.9333, "y": 4.19, "curve": [7.228, 0, 7.513, 0, 7.228, 4.19, 7.513, 0.51]}, {"time": 7.8, "y": 0.51, "curve": [7.841, 0, 7.893, 0, 7.841, 0.51, 7.893, 0]}, {"time": 7.9333, "curve": [8.055, 0, 8.178, 0, 8.055, 0, 8.178, 4.19]}, {"time": 8.3, "y": 4.19, "curve": [8.595, 0, 8.88, 0, 8.595, 4.19, 8.88, 0.51]}, {"time": 9.1667, "y": 0.51, "curve": [9.207, 0, 9.259, 0, 9.207, 0.51, 9.259, 0]}, {"time": 9.3, "curve": [9.422, 0, 9.545, 0, 9.422, 0, 9.545, 4.19]}, {"time": 9.6667, "y": 4.19, "curve": [9.962, 0, 10.247, 0, 9.962, 4.19, 10.247, 0.51]}, {"time": 10.5333, "y": 0.51, "curve": [10.574, 0, 10.626, 0, 10.574, 0.51, 10.626, 0]}, {"time": 10.6667, "curve": [10.789, 0, 10.945, 0, 10.789, 0, 10.945, 4.19]}, {"time": 11.0667, "y": 4.19, "curve": [11.362, 0, 11.647, 0, 11.362, 4.19, 11.647, 0.51]}, {"time": 11.9333, "y": 0.51, "curve": [11.974, 0, 12.026, 0, 11.974, 0.51, 12.026, 0]}, {"time": 12.0667, "curve": [12.189, 0, 12.345, 0, 12.189, 0, 12.345, 4.19]}, {"time": 12.4667, "y": 4.19, "curve": [12.762, 0, 13.013, 0, 12.762, 4.19, 13.013, 0.51]}, {"time": 13.3, "y": 0.51, "curve": [13.341, 0, 13.426, 0, 13.341, 0.51, 13.426, 0]}, {"time": 13.4667, "curve": [13.589, 0, 13.711, 0, 13.589, 0, 13.711, 4.19]}, {"time": 13.8333, "y": 4.19, "curve": [14.128, 0, 14.413, 0, 14.128, 4.19, 14.413, 0.51]}, {"time": 14.7, "y": 0.51, "curve": [14.741, 0, 14.793, 0, 14.741, 0.51, 14.793, 0]}, {"time": 14.8333, "curve": [14.933, 0, 15.034, 0, 14.933, 0, 15.034, 2.79]}, {"time": 15.1333, "y": 3.81}]}}}}}