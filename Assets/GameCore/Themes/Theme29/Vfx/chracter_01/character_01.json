{"skeleton": {"hash": "loU3K2J1om0", "spine": "4.0.64", "x": -248.06, "y": -18.14, "width": 544.62, "height": 612.42, "images": "./images/", "audio": "D:/Vuong/project/tiles hop/bg_29/character_01"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "bone", "parent": "all", "x": 125.73, "y": 222.43}, {"name": "bone34", "parent": "bone", "x": -21.8, "y": 9.79}, {"name": "leg_04", "parent": "bone34", "length": 140.69, "rotation": -138.92, "x": 8.97, "y": -0.17}, {"name": "leg_05", "parent": "leg_04", "length": 190.88, "rotation": -4.47, "x": 140.82, "y": 1.45, "color": "abe323ff"}, {"name": "leg_06", "parent": "bone", "length": 122.01, "rotation": -50.37, "x": -1, "y": -28.56, "color": "abe323ff"}, {"name": "leg_03", "parent": "leg_06", "length": 144.17, "rotation": -129.49, "x": 119.03, "y": -0.79}, {"name": "bone2", "parent": "bone", "length": 46.05, "rotation": 16.7, "x": 6, "y": 8.12}, {"name": "bone3", "parent": "bone2", "length": 74.92, "rotation": 54.41, "x": 45.74, "y": 0.09}, {"name": "bone4", "parent": "bone3", "length": 9.6, "rotation": -30.1, "x": 75.42, "y": -0.49}, {"name": "head", "parent": "bone4", "length": 47.47, "rotation": 2.11, "x": 14.91, "y": 7.07}, {"name": "head2", "parent": "head", "x": 16.43, "y": 42.25}, {"name": "hair_05", "parent": "head2", "length": 31.35, "rotation": 45, "x": 6.09, "y": 14.05}, {"name": "hair_06", "parent": "hair_05", "length": 22.81, "rotation": 51.28, "x": 31.96, "y": 0.43}, {"name": "hair_07", "parent": "hair_06", "length": 34.86, "rotation": 38.57, "x": 22.81}, {"name": "head3", "parent": "head2", "length": 18.48, "rotation": 11.42, "x": 9.19, "y": 12.83}, {"name": "head4", "parent": "head3", "length": 17.21, "rotation": 16.59, "x": 18.48}, {"name": "hair_04", "parent": "head2", "length": 22.23, "rotation": 89.3, "x": -5.77, "y": 5.95}, {"name": "hair_010", "parent": "head2", "length": 28.18, "rotation": 120.3, "x": -29.79, "y": 27.34}, {"name": "hair_011", "parent": "hair_010", "length": 16.92, "rotation": -17.5, "x": 28.18}, {"name": "hair_012", "parent": "hair_011", "length": 17.84, "rotation": -96.24, "x": 16.92}, {"name": "hair_013", "parent": "hair_012", "length": 30.34, "rotation": 83.11, "x": 17.84}, {"name": "head5", "parent": "head", "x": 57.44, "y": 2.35}, {"name": "hair_01", "parent": "head5", "length": 23.62, "rotation": 16.5, "x": 11.32, "y": -11.56}, {"name": "hair_02", "parent": "hair_01", "length": 22.54, "rotation": 72.85, "x": 23.62}, {"name": "hair_03", "parent": "hair_02", "length": 31.85, "rotation": 30.42, "x": 22.54}, {"name": "hair_014", "parent": "head5", "length": 17.88, "rotation": 91.88, "x": 39.8, "y": 4.12}, {"name": "hair_015", "parent": "hair_014", "length": 11.17, "rotation": 11.98, "x": 17.88}, {"name": "hair_016", "parent": "hair_015", "length": 10.85, "rotation": -24.32, "x": 11.17}, {"name": "hair_017", "parent": "head5", "length": 14.48, "rotation": 7.79, "x": 8.37, "y": 0.71}, {"name": "hair_018", "parent": "hair_017", "length": 22.25, "rotation": 79.83, "x": 14.48}, {"name": "hair_019", "parent": "hair_018", "length": 24.59, "rotation": 49.81, "x": 22.4, "y": 0.18}, {"name": "hair_020", "parent": "hair_019", "length": 21.64, "rotation": 9.43, "x": 24.59}, {"name": "hair_021", "parent": "head5", "length": 17.62, "rotation": -128.54, "x": -40.76, "y": -23.03}, {"name": "hair_022", "parent": "hair_021", "length": 20.05, "rotation": 42.79, "x": 17.66, "y": 0.47}, {"name": "hair_023", "parent": "all", "length": 28.15, "rotation": -160.06, "x": 249.26, "y": 295.26, "color": "ff3f00ff"}, {"name": "bone33", "parent": "bone3", "rotation": -71.11, "x": 78.17, "y": 21.71}, {"name": "bone5", "parent": "bone33", "rotation": 71.11, "x": 4.8, "y": -9.8}, {"name": "bone6", "parent": "bone5", "length": 55.57, "rotation": 105.44, "x": -0.97, "y": 3.2}, {"name": "bone7", "parent": "bone6", "length": 81.58, "rotation": -38.6, "x": 57.04, "y": 0.31, "color": "abe323ff"}, {"name": "hand_03", "parent": "bone3", "length": 52.24, "rotation": -131.13, "x": -21.42, "y": -16.05}, {"name": "bone28", "parent": "bone2", "rotation": -16.7, "x": -14.02, "y": 26.74}, {"name": "bone10", "parent": "bone28", "x": -4.18, "y": 24.87}, {"name": "bone9", "parent": "bone28", "x": -11.94, "y": 11.57}, {"name": "bone8", "parent": "bone28", "x": 12.44, "y": 2.86}, {"name": "bone15", "parent": "bone3", "x": 62.64, "y": 20.17}, {"name": "bone11", "parent": "bone3", "x": 44.23, "y": 23.14}, {"name": "bone12", "parent": "bone11", "length": 37.55, "rotation": 160.75, "x": -7.21, "y": 6.53}, {"name": "bone13", "parent": "bone11", "length": 51.69, "rotation": 134.59, "x": -5.95, "y": 12.9}, {"name": "bone14", "parent": "bone11", "length": 61.94, "rotation": 108.11, "x": -4.25, "y": 19.43}, {"name": "bone16", "parent": "bone15", "length": 74.39, "rotation": 116.24, "x": -0.3, "y": 16.46}, {"name": "bone17", "parent": "bone15", "length": 51.37, "rotation": 141.4, "x": -3.82, "y": 6.96}, {"name": "bone18", "parent": "bone15", "length": 55.82, "rotation": 123.46, "x": -3.19, "y": 12.46}, {"name": "eye_01", "parent": "head", "x": 29.03, "y": -10.29}, {"name": "bone19", "parent": "bone", "length": 36.24, "rotation": -135.23, "x": -4.53, "y": -3.37, "color": "8d00a6ff"}, {"name": "bone20", "parent": "bone", "length": 28.26, "rotation": -172.41, "x": -17.39, "y": 16.55, "color": "8d00a6ff"}, {"name": "bone21", "parent": "bone", "length": 33.66, "rotation": 177.17, "x": -35.86, "y": 33.98}, {"name": "bone22", "parent": "bone", "length": 40.22, "rotation": -95.33, "x": 12.94, "y": -9.34, "color": "8d00a6ff"}, {"name": "bone23", "parent": "bone", "length": 43.51, "rotation": -71.65, "x": 22.69, "y": -22, "scaleX": 0.9996, "scaleY": 0.9996, "color": "8d00a6ff"}, {"name": "bone24", "parent": "bone", "length": 40.09, "rotation": -42.69, "x": 37.83, "y": -36.1, "color": "8d00a6ff"}, {"name": "skirt_02", "parent": "bone", "length": 45.06, "rotation": -167.03, "x": -24.66, "y": 19.65, "color": "36116fff"}, {"name": "bone25", "parent": "bone", "length": 61.94, "rotation": -135.15, "x": -9.94, "y": 2.87, "color": "36116fff"}, {"name": "bone26", "parent": "bone", "length": 58.36, "rotation": -99.98, "x": 2.01, "y": -12.54, "color": "36116fff"}, {"name": "bone27", "parent": "bone", "length": 67.04, "rotation": -74.49, "x": 3.85, "y": -7.71, "color": "36116fff"}, {"name": "hair_024", "parent": "hair_020", "length": 13.37, "rotation": 23.07, "x": 21.8, "y": 0.04}, {"name": "target", "parent": "all", "x": 62.87, "y": 373.93, "color": "ff3f00ff"}, {"name": "target2", "parent": "all", "x": 207.96, "y": 178.89, "color": "ff3f00ff"}, {"name": "target3", "parent": "all", "x": 54.92, "y": 101.78, "color": "ff3f00ff"}, {"name": "bone29", "parent": "bone28", "x": 5.8, "y": 18.29}, {"name": "bone30", "parent": "bone33", "rotation": 71.11, "x": 4.8, "y": -9.8}, {"name": "bone31", "parent": "bone30", "length": 55.57, "rotation": 102.57, "x": -0.97, "y": 3.2}, {"name": "target4", "parent": "bone30", "rotation": 68.51, "x": -13.68, "y": 58.81, "color": "ff3f00ff"}, {"name": "bone32", "parent": "target4", "length": 81.58}, {"name": "target6", "parent": "all", "x": 193.09, "y": 158.26, "color": "ff3f00ff"}, {"name": "bone35", "parent": "bone", "x": -21.8, "y": 9.79}, {"name": "leg_08", "parent": "all", "rotation": -143.39, "x": -148.56, "y": 28.72, "color": "ff3f00ff"}, {"name": "leg_09", "parent": "bone35", "length": 140.69, "rotation": -138.92, "x": 8.97, "y": -0.17}, {"name": "leg_011", "parent": "bone35", "rotation": -143.39, "x": -97.48, "y": -92.21, "color": "ff3f00ff"}, {"name": "leg_010", "parent": "leg_011", "length": 178.49, "rotation": -0.23, "x": 0.04, "y": -0.56}, {"name": "leg_012", "parent": "all", "rotation": -143.39, "x": -130.63, "y": 13.73}, {"name": "hair_08", "parent": "hair_04", "length": 23.1, "rotation": 1.01, "x": 22.31, "y": 0.2}, {"name": "hair_09", "parent": "hair_08", "length": 23.23, "rotation": 7.82, "x": 23.1}, {"name": "hair_026", "parent": "hair_09", "length": 21.9, "rotation": 12.7, "x": 23.23}, {"name": "hair_027", "parent": "hair_026", "length": 20.88, "rotation": 16.18, "x": 21.9}, {"name": "hair_028", "parent": "hair_027", "length": 16.1, "rotation": 9.87, "x": 20.88}, {"name": "vfx", "parent": "root", "x": -63.7, "y": 191.54, "scaleX": 1.2032, "scaleY": 1.2032}, {"name": "glow (1)", "parent": "vfx", "x": 2.17, "y": -17.87}, {"name": "VFX", "parent": "all", "x": 289.19, "y": 137.68}, {"name": "VFX2", "parent": "VFX", "rotation": 28.94, "x": -91, "y": 140.44}, {"name": "VFX3", "parent": "VFX", "x": 10.13, "y": -112.25}, {"name": "glow", "parent": "VFX2", "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "VFX4", "parent": "all", "x": 289.19, "y": 137.68}, {"name": "VFX5", "parent": "VFX4", "rotation": 28.94, "x": -91, "y": 140.44}, {"name": "glow2", "parent": "VFX5", "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "VFX6", "parent": "VFX4", "x": 10.13, "y": -112.25}, {"name": "VFX7", "parent": "all", "rotation": -18.13, "x": 109.15, "y": 199.39}, {"name": "VFX8", "parent": "VFX7", "rotation": 28.94, "x": -91, "y": 140.44}, {"name": "glow3", "parent": "VFX8", "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "VFX9", "parent": "VFX7", "x": 10.13, "y": -112.25}, {"name": "glow5", "parent": "VFX8", "rotation": 18.13, "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "glow6", "parent": "VFX3", "rotation": 28.94, "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "glow7", "parent": "VFX", "rotation": 28.94, "x": -91, "y": 140.44, "scaleX": 0.2717, "scaleY": 0.2717}, {"name": "shadow", "parent": "root", "x": 135.1, "y": 235.89, "scaleX": 1.793, "scaleY": 1.793}], "slots": [{"name": "lighting_03", "bone": "VFX8", "blend": "additive"}, {"name": "hair_07", "bone": "hair_021", "attachment": "hair_07"}, {"name": "hair_06", "bone": "hair_010", "attachment": "hair_06"}, {"name": "skirt_02", "bone": "skirt_02", "attachment": "skirt_02"}, {"name": "hand_04", "bone": "hand_03", "attachment": "hand_04"}, {"name": "hand_03", "bone": "hand_03", "attachment": "hand_03"}, {"name": "leg_04", "bone": "leg_06", "attachment": "leg_04"}, {"name": "leg_03", "bone": "leg_03", "attachment": "leg_03"}, {"name": "obj_02", "bone": "bone15", "attachment": "obj_02"}, {"name": "leg_A2", "bone": "leg_09", "attachment": "leg_A"}, {"name": "leg_B2", "bone": "leg_010", "attachment": "leg_B"}, {"name": "obj_01", "bone": "bone15", "attachment": "obj_01"}, {"name": "skirt", "bone": "all", "attachment": "skirt"}, {"name": "body_01", "bone": "bone2", "attachment": "body_01"}, {"name": "body_2", "bone": "all", "attachment": "body_2"}, {"name": "hair_05", "bone": "hair_05", "attachment": "hair_05"}, {"name": "hair_04", "bone": "hair_04", "attachment": "hair_04"}, {"name": "hair_03", "bone": "hair_014", "attachment": "hair_03"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "hair_02", "bone": "hair_017", "attachment": "hair_02"}, {"name": "hair_01", "bone": "hair_01", "attachment": "hair_01"}, {"name": "eye_02", "bone": "eye_01", "attachment": "eye_02"}, {"name": "eye_01", "bone": "eye_01", "attachment": "eye_01"}, {"name": "hand_06", "bone": "bone31", "attachment": "hand_06"}, {"name": "hand_01", "bone": "bone32", "attachment": "hand_01"}, {"name": "Asset 1", "bone": "glow (1)", "attachment": "Asset 1"}, {"name": "glow (1)", "bone": "glow (1)", "color": "2eb5f984", "attachment": "glow (1)", "blend": "additive"}, {"name": "lighting_01", "bone": "VFX2", "blend": "additive"}, {"name": "lighting_02", "bone": "VFX5", "blend": "additive"}, {"name": "glow (1)2", "bone": "glow", "blend": "additive"}, {"name": "glow (1)14", "bone": "glow7", "blend": "additive"}, {"name": "glow (1)6", "bone": "glow3", "blend": "additive"}, {"name": "glow (1)4", "bone": "glow2", "blend": "additive"}, {"name": "glow (1)10", "bone": "glow5", "blend": "additive"}, {"name": "glow (1)3", "bone": "glow", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)15", "bone": "glow7", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)7", "bone": "glow3", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)5", "bone": "glow2", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)11", "bone": "glow5", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)13", "bone": "glow6", "color": "00d6ffff", "blend": "additive"}, {"name": "glow (1)12", "bone": "glow6", "blend": "additive"}, {"name": "shadow", "bone": "shadow", "color": "fc000000", "attachment": "shadow", "blend": "additive"}], "ik": [{"name": "hair_023", "order": 12, "bones": ["hair_021", "hair_022"], "target": "hair_023", "stretch": true}, {"name": "leg_08", "order": 8, "bones": ["leg_04", "leg_05"], "target": "leg_08", "bendPositive": false}, {"name": "leg_011", "order": 10, "bones": ["leg_09"], "target": "leg_011", "compress": true, "stretch": true}, {"name": "leg_081", "order": 11, "bones": ["leg_010"], "target": "leg_08", "compress": true, "stretch": true}, {"name": "target", "bones": ["bone6", "bone7"], "target": "target", "bendPositive": false}, {"name": "target2", "order": 1, "bones": ["hand_03"], "target": "target2"}, {"name": "target3", "order": 2, "bones": ["leg_06", "leg_03"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 4, "bones": ["bone31"], "target": "target4", "compress": true, "stretch": true}, {"name": "target5", "order": 5, "bones": ["bone32"], "target": "target", "compress": true, "stretch": true}, {"name": "target6", "order": 7, "bones": ["bone24"], "target": "target6"}], "transform": [{"name": "bone7", "order": 3, "bones": ["target4"], "target": "bone7", "mixRotate": 0, "mixX": 0.3, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_05", "order": 9, "bones": ["leg_011"], "target": "leg_05", "rotation": 0.5, "x": 0.02, "y": -0.25, "mixRotate": 0, "mixX": 0.8, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_06", "order": 6, "bones": ["target6"], "target": "leg_06", "rotation": 50.37, "x": 70.31, "y": 30.45, "mixRotate": 0, "mixX": 1.725, "mixY": 1.1688, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"Asset 1": {"Asset 1": {"x": 78.89, "y": 98.1, "width": 377, "height": 509}}, "body_01": {"body_01": {"x": 35.21, "y": 23.54, "rotation": -16.7, "width": 130, "height": 155}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.7084, 0.12029, 0.84355, 0.26185, 0.95088, 0.41697, 1, 0.56046, 0.99954, 0.68983, 0.99931, 0.75646, 0.94484, 0.81529, 0.8964, 0.86761, 0.81689, 0.92371, 0.73378, 0.98234, 0.66299, 0.99169, 0.66099, 0.92785, 0.65762, 0.82081, 0.6187, 0.66392, 0.54036, 0.54025, 0.47234, 0.42324, 0.40091, 0.34836, 0.38563, 0.41175, 0.42529, 0.49901, 0.47858, 0.58148, 0.5282, 0.65857, 0.4871, 0.67981, 0.38629, 0.73192, 0.26913, 0.79249, 0.21608, 0.81991, 0.11409, 0.77958, 0.04886, 0.7249, 0.014, 0.65662, 1e-05, 0.43719, 0.02891, 0.30899, 0.11394, 0.19599, 0.19311, 0.12162, 0.27741, 0.07227, 0.4013, 0, 0.50368, 0, 0.37512, 0.32031, 0.33212, 0.27787, 0.26612, 0.25934, 0.20833, 0.27052, 0.13301, 0.31113, 0.06401, 0.36527, 0.78197, 0.88273, 0.85009, 0.8255, 0.91916, 0.77104, 0.11708, 0.25218, 0.19508, 0.21706, 0.35808, 0.22389, 0.46008, 0.30096, 0.52808, 0.39169, 0.72019, 0.81653, 0.66519, 0.63799, 0.59219, 0.50531, 0.26711, 0.20135, 0.34667, 0.41549, 0.37717, 0.51684, 0.43817, 0.60904, 0.22687, 0.7338, 0.33537, 0.67526], "triangles": [50, 2, 3, 3, 43, 50, 13, 50, 49, 48, 47, 0, 48, 0, 1, 47, 48, 15, 51, 48, 1, 51, 1, 2, 15, 48, 51, 51, 14, 15, 50, 51, 2, 14, 51, 50, 50, 13, 14, 52, 31, 32, 45, 30, 31, 52, 45, 31, 46, 32, 33, 52, 32, 46, 44, 30, 45, 37, 45, 52, 37, 52, 46, 38, 45, 37, 44, 45, 38, 36, 37, 46, 34, 46, 33, 47, 34, 0, 47, 46, 34, 35, 36, 46, 39, 44, 38, 47, 35, 46, 16, 35, 47, 15, 16, 47, 16, 53, 35, 53, 36, 35, 37, 36, 53, 53, 38, 37, 29, 30, 44, 29, 44, 39, 40, 29, 39, 17, 53, 16, 28, 29, 40, 54, 53, 17, 53, 40, 39, 53, 39, 38, 40, 53, 54, 54, 28, 40, 54, 17, 18, 55, 18, 19, 54, 18, 55, 57, 27, 28, 21, 55, 19, 57, 54, 55, 20, 21, 19, 27, 56, 26, 28, 54, 57, 22, 57, 55, 22, 55, 21, 57, 56, 27, 3, 4, 43, 5, 43, 4, 25, 26, 56, 23, 56, 57, 23, 57, 22, 6, 43, 5, 42, 49, 50, 24, 25, 56, 24, 56, 23, 43, 42, 50, 42, 43, 6, 7, 42, 6, 41, 49, 42, 8, 41, 42, 8, 42, 7, 11, 12, 49, 11, 49, 41, 9, 11, 41, 9, 41, 8, 10, 11, 9, 12, 13, 49], "vertices": [2, 43, 11.53, 3.34, 0.39135, 69, 1.56, 9.92, 0.60865, 3, 43, 16.93, -2.46, 0.03173, 69, 6.96, 4.12, 0.9157, 45, 0.31, 19.55, 0.05257, 2, 69, 11.25, -2.24, 0.65582, 45, 4.61, 13.19, 0.34418, 2, 69, 13.22, -8.12, 0.30571, 45, 6.57, 7.3, 0.69429, 2, 42, 19, 4.86, 0.67429, 45, 6.55, 2, 0.32571, 1, 42, 18.99, 2.13, 1, 1, 42, 16.81, -0.28, 1, 1, 42, 14.87, -2.43, 1, 1, 42, 11.69, -4.73, 1, 1, 42, 8.37, -7.13, 1, 1, 42, 5.53, -7.52, 1, 1, 42, 5.45, -4.9, 1, 3, 42, 5.32, -0.51, 0.88, 69, -0.48, -18.8, 0.07672, 45, -7.12, -3.37, 0.04328, 3, 43, 7.94, -18.95, 2e-05, 69, -2.03, -12.36, 0.42073, 45, -8.68, 3.06, 0.57925, 4, 44, 12.57, -0.58, 0.00056, 43, 4.81, -13.88, 0.04026, 69, -5.17, -7.29, 0.74786, 45, -11.81, 8.13, 0.21131, 4, 44, 9.85, 4.22, 0.0338, 43, 2.09, -9.08, 0.31049, 69, -7.89, -2.5, 0.62917, 45, -14.53, 12.93, 0.02653, 4, 44, 7, 7.29, 0.24095, 43, -0.77, -6.01, 0.57449, 69, -10.74, 0.57, 0.18401, 45, -17.39, 16, 0.00055, 3, 44, 6.38, 4.69, 0.63161, 43, -1.38, -8.61, 0.3343, 69, -11.36, -2.03, 0.03409, 1, 42, -3.97, 12.68, 1, 1, 42, -1.84, 9.3, 1, 1, 42, 0.14, 6.14, 1, 1, 42, -1.5, 5.27, 1, 1, 42, -5.53, 3.13, 1, 1, 42, -10.22, 0.65, 1, 1, 42, -12.34, -0.47, 1, 1, 42, -16.42, 1.18, 1, 1, 42, -19.03, 3.42, 1, 1, 42, -20.43, 6.22, 1, 2, 44, -9.04, 3.64, 0.86912, 43, -16.81, -9.65, 0.13088, 2, 44, -7.88, 8.9, 0.6743, 43, -15.65, -4.4, 0.3257, 2, 44, -4.48, 13.53, 0.41034, 43, -12.25, 0.24, 0.58966, 2, 44, -1.32, 16.58, 0.21304, 43, -9.08, 3.29, 0.78696, 2, 44, 2.06, 18.61, 0.07677, 43, -5.71, 5.31, 0.92323, 3, 44, 7.01, 21.57, 0.0009, 43, -0.76, 8.27, 0.98806, 69, -10.73, 14.86, 0.01105, 2, 43, 3.34, 8.27, 0.92171, 69, -6.63, 14.86, 0.07829, 3, 44, 5.96, 8.44, 0.26078, 43, -1.8, -4.86, 0.65461, 69, -11.78, 1.72, 0.08461, 3, 44, 4.24, 10.18, 0.24513, 43, -3.52, -3.12, 0.7365, 69, -13.5, 3.46, 0.01836, 3, 44, 1.6, 10.94, 0.31341, 43, -6.16, -2.36, 0.68592, 69, -16.14, 4.22, 0.00067, 2, 44, -0.71, 10.48, 0.41967, 43, -8.47, -2.82, 0.58033, 2, 44, -3.72, 8.81, 0.5923, 43, -11.49, -4.48, 0.4077, 2, 44, -6.48, 6.59, 0.74439, 43, -14.25, -6.7, 0.25561, 1, 42, 10.29, -3.05, 1, 1, 42, 13.02, -0.7, 1, 1, 42, 15.78, 1.53, 1, 2, 44, -4.36, 11.23, 0.469, 43, -12.12, -2.07, 0.531, 3, 44, -1.24, 12.67, 0.28837, 43, -9, -0.63, 0.71126, 69, -18.98, 5.96, 0.00036, 3, 44, 5.28, 12.39, 0.19757, 43, -2.48, -0.91, 0.77726, 69, -12.46, 5.68, 0.02517, 4, 44, 9.36, 9.23, 0.19251, 43, 1.6, -4.07, 0.54452, 69, -8.38, 2.52, 0.26253, 45, -15.02, 17.94, 0.00044, 4, 44, 12.08, 5.51, 0.03775, 43, 4.32, -7.79, 0.28151, 69, -5.66, -1.2, 0.65174, 45, -12.3, 14.22, 0.029, 4, 42, 7.82, -0.33, 0.83863, 43, 12, -25.21, 0, 69, 2.03, -18.62, 0.10072, 45, -4.62, -3.2, 0.06065, 3, 43, 9.8, -17.89, 2e-05, 69, -0.17, -11.3, 0.43517, 45, -6.82, 4.12, 0.56481, 4, 44, 14.65, 0.85, 0.02021, 43, 6.88, -12.45, 0.15078, 69, -3.09, -5.86, 0.55217, 45, -9.74, 9.56, 0.27684, 3, 44, 1.64, 13.31, 0.24234, 43, -6.12, 0.02, 0.7572, 69, -16.1, 6.6, 0.00046, 4, 42, -7.12, 16.11, 0.03139, 44, 4.83, 4.53, 0.62136, 43, -2.94, -8.76, 0.31713, 69, -12.91, -2.18, 0.03012, 4, 42, -5.9, 11.95, 0.8397, 44, 6.05, 0.38, 0.10283, 43, -1.72, -12.92, 0.05248, 69, -11.69, -6.33, 0.00499, 4, 42, -3.46, 8.17, 0.98392, 44, 8.49, -3.4, 0.01031, 43, 0.72, -16.7, 0.00526, 69, -9.25, -10.11, 0.0005, 4, 42, -11.91, 3.06, 0.9764, 44, 0.03, -8.52, 0.01514, 43, -7.73, -21.81, 0.00773, 69, -17.71, -15.23, 0.00073, 4, 42, -7.57, 5.46, 0.95829, 44, 4.37, -6.12, 0.02676, 43, -3.39, -19.41, 0.01366, 69, -13.37, -12.83, 0.0013], "hull": 35, "edges": [54, 52, 52, 50, 50, 48, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 68, 0, 0, 2, 2, 4, 4, 6, 66, 68, 66, 64, 64, 62, 60, 58, 54, 56, 58, 56, 62, 60, 18, 20, 32, 70, 70, 72, 74, 76, 76, 78, 78, 80, 74, 72, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 6, 8, 8, 10, 40, 42, 46, 48, 42, 44, 44, 46], "width": 40, "height": 41}}, "eye_01": {"eye_01": {"x": -3.43, "y": 12.6, "rotation": -43.12, "width": 19, "height": 14}}, "eye_02": {"eye_02": {"x": 1.07, "y": -6.01, "rotation": -43.12, "width": 11, "height": 9}}, "glow (1)": {"glow (1)": {"x": 12.82, "y": 39.42, "scaleX": 2.6283, "scaleY": 2.6283, "width": 128, "height": 128}}, "glow (1)2": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)3": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "glow (1)4": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)5": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "glow (1)6": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)7": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "glow (1)10": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)11": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "glow (1)12": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)13": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "glow (1)14": {"glow (1)": {"scaleX": 0.5978, "scaleY": 0.5978, "width": 128, "height": 128}}, "glow (1)15": {"glow (1)": {"scaleX": 1.3395, "scaleY": 1.3395, "width": 128, "height": 128}}, "hair_01": {"hair_01": {"type": "mesh", "uvs": [0.62847, 0.08164, 0.77141, 0.19313, 0.89386, 0.29877, 1, 0.48294, 1, 0.64485, 1, 0.80518, 0.83981, 1, 0.76672, 1, 0.65383, 0.86491, 0.72611, 0.74946, 0.76299, 0.66569, 0.76509, 0.57325, 0.71799, 0.49461, 0.62135, 0.40501, 0.50539, 0.31956, 0.26175, 0.27377, 0, 0.26083, 0, 0, 0.32133, 0, 0.44168, 0], "triangles": [1, 13, 0, 14, 15, 19, 15, 18, 19, 13, 14, 0, 14, 19, 0, 18, 15, 17, 15, 16, 17, 11, 3, 4, 3, 11, 2, 11, 12, 2, 12, 1, 2, 12, 13, 1, 5, 9, 10, 10, 4, 5, 10, 11, 4, 6, 7, 9, 8, 9, 7, 5, 6, 9], "vertices": [2, 25, 36.68, -5.05, 0.05306, 26, 9.64, -11.51, 0.94694, 2, 25, 24.25, -8.39, 0.59417, 26, -2.78, -8.1, 0.40583, 2, 25, 13.14, -10.84, 0.98216, 26, -13.6, -4.58, 0.01784, 3, 23, 43.03, -5.68, 0.16857, 24, 32.07, -3.38, 0.03493, 25, -0.73, -9.08, 0.7965, 3, 23, 36.17, -13.01, 0.34, 24, 23.41, -8.45, 0.53963, 25, -8.14, -2.3, 0.12037, 3, 23, 29.37, -20.27, 0.32571, 24, 14.84, -13.48, 0.67428, 25, -15.47, 4.42, 0, 1, 23, 12.23, -20.76, 1, 1, 23, 8.17, -16.97, 1, 1, 23, 7.64, -4.99, 1, 4, 23, 16.54, -3.52, 0.218, 24, 7.29, 6.22, 0.70235, 25, 1.13, 17.44, 0.07436, 26, -9.63, 25.88, 0.00529, 4, 23, 22.13, -1.64, 0.06308, 24, 13.19, 6.43, 0.72202, 25, 3.07, 11.86, 0.21014, 26, -10.78, 20.09, 0.00476, 4, 23, 26.17, 2.43, 0.04635, 24, 18.21, 9.19, 0.38804, 25, 7.19, 7.87, 0.55884, 26, -9.25, 14.56, 0.00678, 4, 23, 26.89, 8.44, 0.05784, 24, 20.61, 14.75, 0.13268, 25, 13.2, 7.22, 0.76726, 26, -4.4, 10.96, 0.04222, 4, 23, 25.32, 17.51, 0.069, 24, 21.69, 23.89, 0.03446, 25, 22.26, 8.89, 0.44409, 26, 4.26, 7.81, 0.45244, 4, 23, 22.51, 27.4, 0.05001, 24, 21.8, 34.18, 0.00394, 25, 32.12, 11.81, 0.0499, 26, 14.24, 5.34, 0.89616, 3, 23, 10.93, 42.13, 0.09, 25, 46.72, 23.55, 0.00274, 26, 32.77, 8.07, 0.90727, 2, 23, -3.04, 56.31, 0.13203, 26, 52.02, 13.15, 0.86797, 2, 23, 8.02, 68.12, 0.01567, 26, 56.78, -2.31, 0.98433, 2, 23, 25.84, 51.43, 0.00427, 26, 33.44, -9.49, 0.99573, 1, 26, 24.69, -12.18, 1], "hull": 20, "edges": [12, 14, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 30, 32, 38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 16, 14, 34, 36, 36, 38, 10, 12], "width": 76, "height": 62}}, "hair_02": {"hair_02": {"type": "mesh", "uvs": [0.71302, 0.01425, 0.85415, 0.16156, 0.95325, 0.32186, 0.99999, 0.51726, 1, 0.66597, 0.96501, 0.80871, 0.86951, 0.99999, 0.72338, 0.8056, 0.75888, 0.65283, 0.73497, 0.46086, 0.65047, 0.34276, 0.54721, 0.28601, 0.46364, 0.2968, 0.37563, 0.31005, 0.24665, 0.3681, 0.1327, 0.48266, 0, 0.61553, 0, 0.33245, 0.08355, 0.27148, 0.21048, 0.14164, 0.32156, 0.0347, 0.41259, 1e-05, 0.56122, 1e-05], "triangles": [15, 18, 14, 15, 16, 17, 15, 17, 18, 13, 20, 21, 13, 21, 12, 19, 20, 13, 14, 19, 13, 18, 19, 14, 22, 12, 21, 11, 12, 22, 10, 22, 0, 11, 22, 10, 10, 0, 1, 9, 10, 1, 9, 1, 2, 8, 9, 2, 8, 2, 3, 8, 3, 4, 5, 8, 4, 6, 8, 5, 7, 8, 6], "vertices": [2, 31, 28.24, -4.15, 0.78426, 32, 0.46, -7.25, 0.21574, 1, 31, 15.79, -8.89, 1, 1, 31, 5.2, -10.63, 1, 2, 30, 21.79, -5.06, 0.28248, 31, -3.69, -8.08, 0.71752, 2, 30, 16.83, -9.1, 0.76291, 31, -8.54, -3.91, 0.23709, 3, 23, 19.96, -8.51, 0.45714, 30, 10.23, -10.71, 0.53754, 31, -11.3, 2.29, 0.00531, 1, 23, 8.55, -9.1, 1, 1, 23, 5.41, 5.29, 1, 4, 23, 12.05, 8.07, 0.36571, 30, 4.65, 6.79, 0.48577, 31, 4.95, 10.88, 0.14796, 32, -3.09, 20.25, 0.00055, 3, 30, 9.8, 13.54, 0.08353, 31, 12.5, 7, 0.78076, 32, -1.18, 11.97, 0.13571, 2, 31, 20.92, 9, 0.09038, 32, 5.78, 6.83, 0.90962, 1, 32, 14.33, 4.31, 1, 2, 32, 21.27, 4.7, 0.90463, 33, -2.51, 5.18, 0.09537, 2, 32, 28.58, 5.2, 0.03866, 33, 4.79, 4.48, 0.96134, 2, 33, 15.76, 5.08, 0.81694, 65, -3.58, 7.01, 0.18306, 2, 33, 25.93, 8.3, 0.0048, 65, 7.03, 5.98, 0.9952, 1, 65, 19.38, 4.76, 1, 1, 65, 12.74, -5.44, 1, 1, 65, 5.5, -3.86, 1, 1, 33, 17.03, -5.03, 1, 2, 32, 32.96, -6.68, 0.02189, 33, 7.16, -7.96, 0.97811, 2, 32, 25.39, -8.1, 0.50786, 33, -0.54, -8.12, 0.49214, 2, 31, 36.92, 5, 0.02137, 32, 13.05, -7.98, 0.97863], "hull": 23, "edges": [14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 40, 38, 38, 36, 32, 34, 36, 34, 44, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 40, 42, 42, 44, 20, 22, 22, 24], "width": 83, "height": 43}}, "hair_03": {"hair_03": {"type": "mesh", "uvs": [0.176, 0.10117, 0.29624, 0.2452, 0.42371, 0.34732, 0.55386, 0.44446, 0.72952, 0.57327, 0.84962, 0.681, 0.9929, 0.86416, 1, 1, 0.91253, 0.99091, 0.72504, 0.81087, 0.6064, 0.69201, 0.4541, 0.57029, 0.30784, 0.46316, 0.16625, 0.35241, 0.06751, 0.18563, 0, 0.04436, 1e-05, 1e-05, 0.09824, 0], "triangles": [5, 9, 10, 8, 6, 7, 8, 9, 6, 9, 5, 6, 13, 14, 1, 14, 0, 1, 0, 14, 15, 0, 15, 17, 17, 15, 16, 12, 2, 11, 11, 2, 3, 13, 1, 12, 12, 1, 2, 5, 10, 4, 4, 10, 3, 10, 11, 3], "vertices": [1, 29, 6.61, -2.5, 1, 2, 28, 10.46, -3.05, 0.52589, 29, 0.6, -3.07, 0.47411, 1, 28, 5.31, -2.37, 1, 2, 27, 18.45, -1.81, 0.36601, 28, 0.18, -1.88, 0.63399, 1, 27, 11.59, -2.65, 1, 1, 23, 42.35, 10.66, 1, 1, 23, 41.46, 3.21, 1, 1, 23, 38.56, -0.21, 1, 1, 23, 36.79, 1.86, 1, 1, 23, 36.6, 10.17, 1, 1, 27, 11.52, 2.82, 1, 2, 27, 17.7, 3.32, 0.48015, 28, 0.51, 3.28, 0.51985, 1, 28, 6.23, 2.79, 1, 2, 28, 11.91, 2.12, 0.41102, 29, -0.2, 2.23, 0.58898, 1, 29, 6.08, 1.84, 1, 1, 29, 11.14, 1.08, 1, 1, 29, 12.37, 0.29, 1, 1, 29, 10.73, -2.27, 1], "hull": 18, "edges": [6, 8, 14, 12, 14, 16, 20, 22, 30, 32, 32, 34, 6, 4, 4, 2, 22, 24, 24, 26, 34, 0, 0, 2, 30, 28, 28, 26, 12, 10, 10, 8, 16, 18, 18, 20], "width": 31, "height": 33}}, "hair_04": {"hair_04": {"type": "mesh", "uvs": [0.08998, 0.02637, 0.18111, 0.0119, 0.25555, 0.00568, 0.32347, 0, 0.40002, 0.00361, 0.48647, 0.01325, 0.56949, 0.04722, 0.64572, 0.09935, 0.70854, 0.16632, 0.75743, 0.23061, 0.8085, 0.31297, 0.8529, 0.43959, 0.88149, 0.4956, 0.92386, 0.56159, 0.95628, 0.63294, 0.98831, 0.7088, 0.93729, 0.8252, 0.75894, 1, 0.61572, 1, 0.52734, 0.87321, 0.48644, 0.78985, 0.4447, 0.70475, 0.39314, 0.54682, 0.35643, 0.47736, 0.3157, 0.40027, 0.25155, 0.30495, 0.18609, 0.23439, 0.14318, 0.18813, 0.08612, 0.14935, 0.00656, 0.09527, 0, 0.06819, 0.91449, 0.77266, 0.82281, 0.89082, 0.7367, 0.90513, 0.66539, 0.85869], "triangles": [28, 29, 0, 29, 30, 0, 25, 26, 2, 27, 1, 26, 26, 1, 2, 28, 0, 27, 27, 0, 1, 6, 23, 5, 4, 5, 24, 5, 23, 24, 4, 24, 25, 4, 25, 3, 3, 25, 2, 10, 22, 9, 9, 22, 8, 8, 22, 7, 6, 7, 23, 22, 23, 7, 17, 18, 33, 18, 34, 33, 18, 19, 34, 19, 20, 34, 11, 34, 21, 34, 20, 21, 10, 21, 22, 17, 32, 16, 17, 33, 32, 33, 34, 32, 31, 32, 12, 32, 34, 12, 34, 11, 12, 31, 12, 13, 21, 10, 11, 32, 31, 16, 16, 31, 15, 31, 14, 15, 31, 13, 14], "vertices": [2, 84, 25.61, -3.93, 0.49133, 85, 3.98, -4.68, 0.50867, 2, 84, 15.04, -6.99, 0.98889, 85, -6.95, -5.89, 0.01111, 3, 83, 30.5, -6.91, 0.3356, 84, 6.33, -9.03, 0.6591, 85, -15.89, -6.4, 0.0053, 2, 83, 23.38, -10.91, 0.64178, 84, -1.62, -10.89, 0.35822, 3, 82, 41.08, -11.02, 0.0763, 83, 15, -14.67, 0.68869, 84, -10.72, -12.17, 0.23501, 2, 82, 32.49, -16.88, 0.22184, 83, 5.33, -18.51, 0.77816, 4, 18, 71.3, -16.53, 0.00904, 81, 48.69, -17.6, 0.00728, 82, 22.96, -20.92, 0.54219, 83, -4.86, -20.35, 0.4415, 4, 18, 61.94, -20.37, 0.01696, 81, 39.26, -21.27, 0.01366, 82, 13.12, -23.27, 0.82314, 83, -14.98, -20.48, 0.14624, 4, 18, 52.75, -22.18, 0.06236, 81, 30.04, -22.92, 0.08565, 82, 3.76, -23.65, 0.77176, 83, -24.19, -18.79, 0.08023, 4, 18, 44.85, -22.92, 0.11489, 81, 22.13, -23.51, 0.16895, 82, -4.16, -23.16, 0.71231, 83, -31.81, -16.57, 0.00385, 4, 12, 17.49, 41.34, 0.0099, 18, 35.67, -22.83, 0.43988, 81, 12.95, -23.27, 0.32989, 82, -13.22, -21.67, 0.22033, 4, 12, 14.2, 30.02, 0.07456, 18, 24.32, -19.67, 0.71805, 81, 1.66, -19.91, 0.14162, 82, -23.95, -16.81, 0.06577, 4, 12, 13.53, 24.28, 0.09495, 18, 18.57, -19.07, 0.80578, 81, -4.08, -19.21, 0.08224, 82, -29.53, -15.33, 0.01703, 3, 12, 13.49, 16.81, 0.33875, 18, 11.1, -19.13, 0.66116, 81, -11.55, -19.13, 9e-05, 2, 12, 12.29, 9.83, 0.61803, 18, 4.1, -18.01, 0.38197, 2, 12, 10.79, 2.61, 0.88517, 18, -3.14, -16.6, 0.11483, 2, 12, -0.29, -0.26, 0.99719, 18, -6.14, -5.56, 0.00281, 3, 18, -2.42, 20.02, 0.59772, 81, -24.37, 20.26, 0.4022, 82, -44.27, 26.53, 9e-05, 3, 18, 9.18, 32.71, 0.25392, 81, -12.55, 32.74, 0.73105, 82, -30.86, 37.28, 0.01503, 4, 18, 24.1, 33.44, 0.06385, 81, 2.38, 33.2, 0.85303, 82, -16, 35.72, 0.08287, 83, -30.42, 43.47, 0.00026, 4, 18, 32.52, 32.4, 0.03227, 81, 10.78, 32.01, 0.75583, 82, -7.85, 33.39, 0.1987, 83, -22.97, 39.41, 0.01321, 4, 18, 41.11, 31.33, 3e-05, 81, 19.35, 30.79, 0.65661, 82, 0.48, 31.02, 0.31692, 83, -15.37, 35.26, 0.02643, 4, 81, 33.12, 26.27, 0.26013, 82, 13.51, 24.67, 0.55193, 83, -4.05, 26.2, 0.18724, 84, -17.63, 32.39, 0.00071, 4, 81, 40.34, 25.51, 0.14765, 82, 20.55, 22.93, 0.41417, 83, 2.43, 22.95, 0.39865, 84, -12.3, 27.47, 0.03953, 4, 81, 48.35, 24.66, 0.02283, 82, 28.37, 20.99, 0.2613, 83, 9.64, 19.35, 0.63326, 84, -6.39, 22, 0.08261, 4, 81, 59.38, 24.81, 0.00019, 82, 39.33, 19.64, 0.03361, 83, 20.03, 15.62, 0.51379, 84, 2.55, 15.53, 0.45242, 5, 81, 69.04, 26.49, 7e-05, 82, 49.12, 19.99, 0.01331, 83, 29.65, 13.81, 0.20737, 84, 11.29, 11.1, 0.67362, 85, -7.55, 12.58, 0.10562, 3, 83, 35.97, 12.62, 0.00651, 84, 17.02, 8.2, 0.81863, 85, -2.4, 8.74, 0.17486, 3, 83, 43.53, 12.73, 0.00379, 84, 24.32, 6.2, 0.4767, 85, 4.45, 5.52, 0.51951, 1, 85, 13.99, 1.03, 1, 1, 85, 14.79, -1.21, 1, 2, 12, 0.7, 4.79, 0.71124, 18, -1.08, -6.48, 0.28876, 2, 18, -0.9, 8.25, 0.85823, 81, -23.06, 8.46, 0.14177, 3, 18, 5.2, 16.68, 0.55337, 81, -16.82, 16.78, 0.44571, 82, -37.26, 22.06, 0.00092, 3, 18, 13.81, 20.4, 0.25964, 81, -8.13, 20.35, 0.72623, 82, -28.17, 24.41, 0.01413], "hull": 31, "edges": [30, 32, 32, 34, 34, 36, 36, 38, 58, 60, 24, 26, 42, 44, 18, 20, 48, 50, 26, 28, 28, 30, 44, 46, 46, 48, 14, 16, 16, 18, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 50, 52, 52, 54, 54, 56, 56, 58, 2, 0, 0, 60, 38, 40, 40, 42, 20, 22, 22, 24], "width": 120, "height": 83}}, "hair_05": {"hair_05": {"type": "mesh", "uvs": [0.27847, 1e-05, 0.4139, 0, 0.53192, 0.05429, 0.65042, 0.10476, 0.75538, 0.23109, 0.80169, 0.3278, 0.82216, 0.51267, 0.81031, 0.74303, 0.87602, 0.59631, 0.92473, 0.44721, 0.9363, 0.30232, 1, 0.3511, 1, 0.46344, 0.83348, 0.92435, 0.7224, 1, 0.69554, 1, 0.696, 0.73374, 0.70924, 0.55046, 0.68478, 0.36314, 0.58452, 0.23418, 0.49863, 0.16067, 0.40573, 0.12139, 0.2811, 0.11914, 0.13231, 0.168, 0, 0.21882, 0, 0.15273, 0.09198, 0.04255], "triangles": [13, 14, 16, 16, 14, 15, 13, 16, 7, 13, 7, 8, 16, 17, 7, 24, 25, 23, 25, 26, 23, 22, 23, 0, 20, 21, 2, 21, 1, 2, 1, 21, 0, 23, 26, 0, 21, 22, 0, 18, 4, 5, 18, 19, 4, 20, 2, 19, 19, 3, 4, 19, 2, 3, 7, 17, 6, 17, 18, 6, 18, 5, 6, 12, 8, 9, 12, 13, 8, 9, 11, 12, 9, 10, 11], "vertices": [1, 15, 19.85, -5.19, 1, 1, 15, 9.16, -5.57, 1, 2, 14, 24.31, -2.33, 0.37005, 15, -0.27, -2.76, 0.62995, 1, 14, 15.3, -6.2, 1, 1, 14, 4.24, -6.03, 1, 2, 13, 33.82, -3.87, 0.44418, 14, -2.19, -4.15, 0.55582, 1, 13, 23.16, -5.84, 1, 1, 12, 16.78, 17.18, 1, 2, 16, 18.24, 3.46, 0.44915, 17, 0.77, 3.39, 0.55085, 1, 17, 10.19, 2.54, 1, 1, 17, 18.44, 4.4, 1, 1, 17, 17.39, -1.28, 1, 1, 17, 11.22, -3.39, 1, 1, 12, 10.93, 8.25, 1, 1, 12, 1.53, 11.05, 1, 1, 12, -0.02, 12.5, 1, 1, 12, 10.56, 23.74, 1, 1, 13, 20.68, 3, 1, 2, 13, 31.47, 5.29, 0.1271, 14, 3.49, 3.42, 0.8729, 1, 14, 14.37, 2.89, 1, 2, 14, 22.3, 4.07, 0.37865, 15, 2.14, 3.5, 0.62135, 1, 15, 9.55, 1.49, 1, 1, 15, 19.4, 1.7, 1, 1, 15, 31.04, 4.95, 1, 1, 15, 41.39, 8.27, 1, 1, 15, 41.52, 4.44, 1, 1, 15, 34.49, -2.21, 1], "hull": 27, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 48, 50, 50, 52, 44, 46, 46, 48, 0, 52, 4, 6, 36, 38, 38, 40, 40, 42, 42, 44, 0, 2, 2, 4, 6, 8, 8, 10, 20, 22, 16, 14, 14, 12, 12, 10, 16, 18, 18, 20, 34, 36, 30, 32, 32, 34], "width": 79, "height": 58}}, "hair_06": {"hair_06": {"type": "mesh", "uvs": [0.26196, 0.07098, 0.39159, 0.16751, 0.51009, 0.33347, 0.59103, 0.47857, 0.37022, 0.56706, 0.49558, 0.61131, 0.59103, 0.63678, 0.71924, 0.67164, 0.84033, 0.73332, 1, 0.8674, 1, 1, 0.85901, 1, 0.48719, 0.87144, 0.27208, 0.82585, 0.17078, 0.69712, 0.1195, 0.60997, 0.17363, 0.496, 0.18645, 0.31768, 0.11665, 0.1903, 0, 0.05355, 0, 0, 0.03703, 0], "triangles": [11, 8, 9, 1, 18, 0, 19, 21, 18, 18, 21, 0, 19, 20, 21, 17, 1, 2, 1, 17, 18, 4, 17, 2, 16, 17, 4, 4, 15, 16, 4, 2, 3, 13, 4, 5, 13, 14, 4, 4, 14, 15, 12, 5, 6, 12, 13, 5, 12, 6, 7, 12, 7, 8, 8, 11, 12, 11, 9, 10], "vertices": [1, 22, 20.78, -7.21, 1, 1, 22, 10.32, -8.84, 1, 2, 21, 24.16, -3.9, 0.30298, 22, -3.11, -6.74, 0.69702, 2, 21, 19.99, -14.23, 0.88112, 22, -13.87, -3.83, 0.11888, 3, 19, 35.08, -7.86, 0.0032, 20, 8.94, -5.42, 0.57251, 21, 6.25, -7.35, 0.42429, 3, 19, 26.53, -7.26, 0.55279, 20, 0.61, -7.42, 0.44548, 21, 9.15, -15.41, 0.00173, 2, 19, 20.18, -7.35, 0.95957, 20, -5.42, -9.41, 0.04043, 1, 19, 11.64, -7.41, 1, 1, 12, -28.92, 30.15, 1, 1, 12, -27.7, 16.51, 1, 1, 19, -11.95, 8.86, 1, 1, 19, -3.31, 11.43, 1, 2, 19, 21.99, 9.84, 0.94465, 20, -8.86, 7.53, 0.05535, 2, 19, 36.07, 10.8, 0.08495, 20, 4.28, 12.68, 0.91505, 2, 20, 14.56, 9.06, 0.99187, 21, -8.75, -3.34, 0.00813, 2, 20, 20.6, 5.99, 0.71129, 21, -6.35, 3, 0.28871, 3, 20, 22.07, -2.37, 0.001, 21, 1.8, 5.37, 0.96594, 22, 3.41, 16.57, 0.03305, 2, 21, 11.58, 12.59, 0.22577, 22, 11.75, 7.73, 0.77423, 2, 21, 15.29, 21.6, 0.00076, 22, 21.14, 5.13, 0.99924, 1, 22, 33.04, 4.29, 1, 1, 22, 35.71, 1.81, 1, 1, 22, 34.1, 0.07, 1], "hull": 22, "edges": [20, 18, 18, 16, 14, 12, 8, 6, 2, 0, 38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 20, 22, 24, 22, 8, 10, 10, 12, 14, 16, 6, 4, 2, 4, 40, 42, 0, 42], "width": 64, "height": 68}}, "hair_07": {"hair_07": {"type": "mesh", "uvs": [0.52739, 0.12978, 0.48864, 0.25325, 0.4774, 0.30309, 0.51025, 0.31362, 0.69522, 0.28542, 0.8544, 0.26115, 0.99615, 0.26934, 1, 0.32975, 0.93671, 0.45307, 1, 0.65326, 0.90828, 0.80443, 0.7272, 0.92694, 0.50578, 0.96729, 0.24049, 1, 0.19049, 1, 0.12759, 0.93795, 0.061, 0.80793, 0, 0.72376, 0, 0.6463, 0.2182, 0.6183, 0.33266, 0.5795, 0.26709, 0.54366, 0.22497, 0.46923, 0.23737, 0.3185, 0.31626, 0.1717, 0.42164, 0, 0.60321, 0, 0.43935, 0.57615, 0.52931, 0.54389, 0.67895, 0.50633, 0.84027, 0.45969], "triangles": [0, 1, 25, 0, 25, 26, 1, 24, 25, 29, 3, 4, 10, 11, 29, 29, 4, 30, 7, 5, 6, 13, 20, 12, 20, 27, 12, 13, 15, 19, 13, 19, 20, 13, 14, 15, 12, 28, 11, 15, 16, 19, 19, 16, 18, 16, 17, 18, 7, 8, 5, 30, 4, 5, 30, 5, 8, 10, 29, 30, 28, 29, 11, 12, 27, 28, 28, 27, 2, 9, 30, 8, 9, 10, 30, 28, 2, 3, 28, 3, 29, 27, 20, 2, 23, 2, 20, 22, 23, 21, 2, 23, 24, 23, 20, 21, 2, 24, 1], "vertices": [1, 11, 21.98, -28.4, 1, 2, 34, 8.28, 5.86, 0.91258, 35, -2.72, 10.47, 0.08742, 2, 34, 10.71, 4.88, 0.65013, 35, -1.72, 8.05, 0.34987, 2, 34, 11.41, 7.13, 0.24329, 35, 0.38, 9.12, 0.75671, 2, 35, 9.41, 18.51, 0.07429, 36, -8.52, -19.58, 0.92571, 2, 35, 17.18, 26.59, 0.39998, 36, -19.64, -18.16, 0.60002, 2, 35, 25.05, 32.64, 0.20571, 36, -29.19, -15.44, 0.79429, 2, 35, 27.2, 30.5, 0.5472, 36, -28.74, -12.44, 0.4528, 2, 35, 27.75, 22.93, 0.52523, 36, -23, -7.48, 0.47477, 2, 35, 37.57, 18.09, 0.80436, 36, -24.97, 3.28, 0.19564, 2, 35, 37.5, 8.17, 0.6399, 36, -16.96, 9.13, 0.3601, 2, 35, 31.7, -4.66, 0.10838, 36, -3.2, 12.13, 0.89162, 1, 36, 12.34, 10.47, 1, 1, 36, 30.78, 7.72, 1, 1, 36, 34.18, 6.9, 1, 1, 36, 37.74, 2.86, 1, 2, 34, 33.54, -26.19, 6e-05, 36, 40.75, -4.55, 0.99994, 1, 36, 43.92, -9.64, 1, 2, 34, 25.15, -29.8, 2e-05, 36, 43.02, -13.41, 0.99998, 2, 34, 24.97, -14.46, 0.09055, 36, 27.84, -11.2, 0.90945, 3, 34, 23.67, -6.32, 0.57935, 35, -0.63, -9.05, 0.0312, 36, 19.59, -11.21, 0.38945, 3, 34, 21.52, -10.75, 0.53469, 35, -5.3, -10.62, 0.41714, 36, 23.64, -14.03, 0.04816, 3, 34, 17.58, -13.4, 0.55662, 35, -9.95, -9.66, 0.42857, 36, 25.64, -18.34, 0.01481, 3, 34, 10.13, -11.93, 0.68855, 35, -14.12, -3.32, 0.31143, 36, 23.03, -25.46, 2e-05, 1, 11, 10.14, -19.31, 1, 1, 11, 21.43, -18.57, 1, 1, 11, 30.33, -27.65, 1, 3, 34, 24.1, 1.14, 0.12266, 35, 4.99, -4.13, 0.2782, 36, 12.29, -9.63, 0.59914, 2, 35, 8.79, 1.15, 0.45714, 36, 5.79, -9.73, 0.54286, 2, 35, 15.62, 9.31, 0.3294, 36, -4.83, -9.11, 0.6706, 2, 35, 22.79, 18.34, 0.23148, 36, -16.36, -8.74, 0.76852], "hull": 27, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 34, 36, 34, 32, 32, 30, 30, 28, 26, 28, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 50, 52, 0, 52, 2, 0, 22, 24, 24, 26, 54, 56, 56, 58, 58, 60, 60, 16, 40, 54, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 70, "height": 50}}, "hand_01": {"hand_01": {"x": 46.42, "y": -5.08, "rotation": -137.16, "width": 90, "height": 99}}, "hand_03": {"hand_03": {"x": -2.95, "y": -0.19, "rotation": 60.02, "width": 31, "height": 31}}, "hand_04": {"hand_04": {"x": 25.22, "y": 2.61, "rotation": 60.02, "width": 62, "height": 81}}, "hand_06": {"hand_06": {"x": 32.94, "y": -2.54, "rotation": -173.98, "width": 84, "height": 45}}, "head": {"head": {"x": 42.44, "y": 17.36, "rotation": -43.12, "width": 95, "height": 109}}, "leg_03": {"leg_03": {"x": 84.43, "y": 10.7, "rotation": 179.86, "width": 194, "height": 77}}, "leg_04": {"leg_04": {"x": 61.92, "y": -1.41, "rotation": 50.37, "width": 103, "height": 120}}, "leg_A2": {"leg_A": {"type": "mesh", "uvs": [1, 0.26014, 0.85828, 0.43072, 0.71771, 0.57628, 0.55259, 0.69863, 0.4103, 0.78327, 0.26241, 0.88318, 0.17347, 0.96021, 0.09585, 1, 0, 1, 1e-05, 0.79117, 0.03162, 0.73602, 0.2022, 0.52704, 0.31699, 0.41991, 0.45318, 0.25939, 0.56648, 0.13728, 0.72141, 1e-05, 1, 0, 0.06174, 0.70424], "triangles": [15, 16, 0, 1, 15, 0, 14, 15, 1, 2, 14, 1, 13, 14, 2, 3, 12, 13, 2, 3, 13, 17, 10, 11, 4, 12, 3, 11, 12, 4, 5, 17, 11, 4, 5, 11, 6, 17, 5, 10, 17, 6, 9, 10, 6, 7, 8, 9, 6, 7, 9], "vertices": [1, 77, -1.85, 25.02, 1, 1, 77, 26.66, 27.54, 1, 1, 77, 53.07, 27.89, 1, 1, 77, 80.23, 23.86, 1, 1, 77, 101.97, 18.49, 1, 1, 77, 125.52, 14, 1, 1, 77, 141.03, 12.84, 1, 1, 77, 152.39, 9.33, 1, 1, 77, 162.5, 0.51, 1, 1, 77, 145.9, -18.53, 1, 1, 77, 138.18, -20.65, 1, 1, 77, 103.56, -24.02, 1, 1, 77, 82.93, -23.24, 1, 1, 77, 55.79, -25.35, 1, 1, 77, 34.13, -26.06, 1, 1, 77, 6.86, -24.33, 1, 1, 77, -22.54, 1.3, 1, 1, 77, 132.47, -20.78, 1], "hull": 17, "edges": [16, 18, 30, 32, 14, 16, 0, 32, 28, 30, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 14, 12, 12, 10, 6, 4, 4, 2, 2, 0, 6, 8, 8, 10], "width": 140, "height": 121}}, "leg_B2": {"leg_B": {"type": "mesh", "uvs": [0.99999, 0.15283, 0.90951, 0.2233, 0.75562, 0.26912, 0.62675, 0.33947, 0.52469, 0.39817, 0.44353, 0.45994, 0.45681, 0.50135, 0.45283, 0.59416, 0.37848, 0.64699, 0.37449, 0.73409, 0.3028, 0.7498, 0.2935, 0.78693, 0.31873, 0.84404, 0.35591, 1, 0.25836, 1, 0.01979, 0.97455, 0.02028, 0.89747, 0, 0.89032, 0, 0.76318, 0.01859, 0.68036, 0.07311, 0.65466, 0.08772, 0.60469, 0.09701, 0.54329, 0.13551, 0.49474, 0.22314, 0.37051, 0.28023, 0.3391, 0.35325, 0.3748, 0.4158, 0.25628, 0.48749, 0.15919, 0.58284, 0.08491, 0.71208, 0.04319, 0.84751, 1e-05, 1, 0, 0.23341, 0.5457, 0.35688, 0.55569, 0.25609, 0.57632, 0.29606, 0.57737, 0.33212, 0.57213, 0.01245, 0.83789, 0.05818, 0.88654, 0.06182, 0.9382, 0.31747, 0.96131, 0.3102, 0.87435], "triangles": [0, 31, 32, 14, 41, 13, 13, 42, 12, 15, 40, 14, 41, 14, 42, 15, 16, 40, 42, 14, 39, 13, 41, 42, 16, 39, 40, 42, 39, 11, 11, 39, 20, 40, 39, 14, 39, 16, 38, 16, 17, 38, 17, 18, 38, 11, 20, 10, 35, 10, 20, 39, 38, 20, 19, 20, 18, 42, 11, 12, 20, 38, 18, 21, 35, 20, 9, 10, 8, 8, 10, 36, 22, 23, 33, 21, 33, 35, 10, 35, 36, 37, 8, 36, 37, 34, 8, 7, 34, 6, 34, 37, 36, 36, 35, 33, 34, 36, 33, 6, 34, 5, 23, 24, 33, 5, 34, 26, 34, 33, 26, 26, 24, 25, 26, 33, 24, 5, 26, 4, 26, 27, 4, 4, 27, 3, 27, 28, 3, 28, 29, 3, 3, 29, 2, 29, 30, 2, 1, 30, 31, 1, 2, 30, 1, 31, 0, 8, 34, 7, 21, 22, 33], "vertices": [1, 77, 133.02, 11.72, 1, 1, 79, 10.95, 11.29, 1, 1, 79, 36.42, 1.44, 1, 1, 79, 60.78, -2.72, 1, 1, 79, 80.35, -5.64, 1, 1, 79, 97.35, -6.03, 1, 1, 79, 99.45, 0.61, 1, 2, 79, 108.8, 12.05, 0.44, 80, -63.51, -10.4, 0.56, 2, 79, 124.02, 11.21, 0.37714, 80, -48.45, -11.3, 0.62286, 2, 79, 132.82, 21.92, 0.14, 80, -39.69, -0.63, 0.86, 2, 79, 144.15, 16.61, 0.32857, 80, -28.49, -5.98, 0.67143, 2, 79, 148.95, 20.4, 0.32571, 80, -23.72, -2.21, 0.67429, 2, 79, 150.9, 30.26, 0.32857, 80, -21.75, 7.65, 0.67143, 1, 80, -12.06, 31.3, 1, 1, 80, 1.16, 21.3, 1, 1, 80, 31.08, -6.4, 1, 1, 80, 23.74, -16.16, 1, 1, 80, 25.81, -19.15, 1, 1, 80, 13.8, -35.32, 1, 1, 80, 3.47, -43.96, 1, 1, 80, -6.35, -41.64, 1, 2, 79, 159.91, -23.85, 0.20571, 80, -13.05, -46.5, 0.79429, 2, 79, 152.82, -30.73, 0.38571, 80, -20.1, -53.36, 0.61429, 2, 79, 142.93, -33, 0.32857, 80, -29.9, -55.59, 0.67143, 2, 79, 119.12, -39.92, 0.85714, 80, -53.51, -62.41, 0.14286, 2, 79, 108.3, -38.11, 0.77143, 80, -64.21, -60.55, 0.22857, 1, 79, 101.67, -26.11, 1, 1, 79, 81.84, -34.85, 1, 1, 79, 62.79, -39.93, 1, 1, 79, 42.66, -39.69, 1, 1, 79, 20.97, -31.83, 1, 1, 79, -1.72, -23.53, 1, 1, 77, 115.69, -6.27, 1, 2, 79, 134.32, -16.52, 0.95331, 80, -38.36, -39.06, 0.04669, 1, 79, 118.32, -2.65, 1, 1, 79, 134.11, -10.29, 1, 1, 79, 128.73, -6.09, 1, 1, 79, 123.28, -3.08, 1, 1, 80, 19.17, -24.54, 1, 1, 80, 17.57, -13.66, 1, 1, 80, 21.95, -6.72, 1, 2, 79, 162.2, 45.1, 0.04071, 80, -10.51, 22.44, 0.95929, 2, 79, 154.95, 33.26, 0.50326, 80, -17.73, 10.63, 0.49674], "hull": 33, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 6, 4, 4, 2, 0, 64, 2, 0, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 36, 38, 66, 52, 68, 10, 66, 70, 70, 72, 72, 74, 74, 68, 6, 8, 8, 10, 34, 36, 34, 32, 32, 30, 26, 28, 30, 28], "width": 171, "height": 159}}, "lighting_01": {"lighting_01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 90, 31.51, -3.58, 0.97968, 89, 12.5, -491.28, 0.02032, 2, 90, -0.55, -3.56, 0.98313, 89, -19.54, -491.23, 0.01687, 2, 90, -32.49, -3.57, 0.97983, 89, -51.5, -491.28, 0.02017, 2, 90, -32.32, 18.63, 0.96967, 89, -51.37, -469.15, 0.03033, 2, 90, -32.02, 40.79, 0.95244, 89, -51.16, -447.12, 0.04756, 2, 90, -31.6, 62.91, 0.92796, 89, -50.86, -425.19, 0.07204, 2, 90, -31.05, 84.99, 0.8963, 89, -50.47, -403.35, 0.1037, 2, 90, -30.38, 107.03, 0.8578, 89, -49.99, -381.61, 0.1422, 2, 90, -29.61, 129.03, 0.81309, 89, -49.44, -359.95, 0.18691, 2, 90, -28.74, 151, 0.76302, 89, -48.82, -338.36, 0.23698, 2, 90, -27.8, 172.95, 0.70854, 89, -48.15, -316.83, 0.29146, 2, 90, -26.8, 194.88, 0.65065, 89, -47.44, -295.35, 0.34934, 2, 90, -25.76, 216.79, 0.59038, 89, -46.69, -273.89, 0.40962, 2, 90, -24.69, 238.69, 0.52871, 89, -45.93, -252.46, 0.47129, 2, 90, -23.62, 260.6, 0.46666, 89, -45.16, -231.03, 0.53334, 2, 90, -22.56, 282.5, 0.4052, 89, -44.41, -209.59, 0.5948, 2, 90, -21.52, 304.42, 0.34532, 89, -43.67, -188.14, 0.65468, 2, 90, -20.53, 326.35, 0.28798, 89, -42.96, -166.65, 0.71202, 2, 90, -19.6, 348.3, 0.23415, 89, -42.29, -145.11, 0.76585, 2, 90, -18.74, 370.28, 0.18476, 89, -41.68, -123.51, 0.81524, 2, 90, -17.98, 392.28, 0.14072, 89, -41.14, -101.84, 0.85928, 2, 90, -17.33, 414.33, 0.10281, 89, -40.67, -80.09, 0.89719, 2, 90, -16.79, 436.41, 0.07161, 89, -40.29, -58.24, 0.92839, 2, 90, -16.37, 458.53, 0.04744, 89, -39.99, -36.31, 0.95256, 2, 90, -16.07, 480.69, 0.03036, 89, -39.78, -14.28, 0.96964, 2, 90, -15.9, 502.89, 0.02023, 89, -39.65, 7.85, 0.97977, 2, 90, 16.16, 502.87, 0.01685, 89, -7.61, 7.8, 0.98315, 2, 90, 48.1, 502.89, 0.02016, 89, 24.35, 7.85, 0.97984, 2, 90, 47.93, 480.69, 0.03025, 89, 24.22, -14.28, 0.96975, 2, 90, 47.63, 458.53, 0.0473, 89, 24.01, -36.31, 0.9527, 2, 90, 47.22, 436.41, 0.07146, 89, 23.71, -58.25, 0.92854, 2, 90, 46.68, 414.33, 0.10268, 89, 23.33, -80.09, 0.89732, 2, 90, 46.02, 392.28, 0.14062, 89, 22.86, -101.84, 0.85938, 2, 90, 45.26, 370.28, 0.18469, 89, 22.32, -123.51, 0.81531, 2, 90, 44.4, 348.3, 0.2341, 89, 21.71, -145.11, 0.7659, 2, 90, 43.47, 326.35, 0.28795, 89, 21.04, -166.65, 0.71205, 2, 90, 42.48, 304.42, 0.3453, 89, 20.33, -188.14, 0.6547, 2, 90, 41.44, 282.5, 0.40519, 89, 19.59, -209.59, 0.59481, 2, 90, 40.38, 260.6, 0.46665, 89, 18.84, -231.03, 0.53335, 2, 90, 39.31, 238.69, 0.5287, 89, 18.07, -252.46, 0.4713, 2, 90, 38.24, 216.79, 0.59036, 89, 17.31, -273.89, 0.40964, 2, 90, 37.2, 194.88, 0.65063, 89, 16.56, -295.35, 0.34937, 2, 90, 36.2, 172.95, 0.7085, 89, 15.85, -316.83, 0.2915, 2, 90, 35.26, 151, 0.76294, 89, 15.18, -338.36, 0.23706, 2, 90, 34.39, 129.03, 0.81296, 89, 14.56, -359.95, 0.18704, 2, 90, 33.62, 107.02, 0.8576, 89, 14.01, -381.61, 0.1424, 2, 90, 32.96, 84.99, 0.89604, 89, 13.54, -403.36, 0.10396, 2, 90, 32.41, 62.91, 0.92765, 89, 13.15, -425.2, 0.07235, 2, 90, 31.99, 40.79, 0.95212, 89, 12.84, -447.13, 0.04788, 2, 90, 31.69, 18.63, 0.96941, 89, 12.63, -469.16, 0.03059, 2, 90, -0.76, 18.77, 0.99526, 89, -19.69, -468.81, 0.00474, 2, 90, -0.49, 40.95, 0.97983, 89, -19.5, -446.76, 0.02017, 2, 90, -0.06, 63.06, 0.95465, 89, -19.19, -424.83, 0.04535, 2, 90, 0.53, 85.13, 0.92077, 89, -18.77, -403.03, 0.07923, 2, 90, 1.25, 107.15, 0.87928, 89, -18.26, -381.32, 0.12072, 2, 90, 2.08, 129.13, 0.83126, 89, -17.67, -359.71, 0.16874, 2, 90, 3, 151.09, 0.77776, 89, -17, -338.16, 0.22224, 2, 90, 4, 173.01, 0.71985, 89, -16.29, -316.68, 0.28015, 2, 90, 5.06, 194.92, 0.65855, 89, -15.53, -295.24, 0.34145, 2, 90, 6.16, 216.81, 0.59491, 89, -14.75, -273.83, 0.40509, 2, 90, 7.29, 238.7, 0.52993, 89, -13.95, -252.44, 0.47007, 2, 90, 8.42, 260.59, 0.46461, 89, -13.14, -231.06, 0.53539, 2, 90, 9.54, 282.47, 0.39991, 89, -12.34, -209.67, 0.60009, 2, 90, 10.63, 304.37, 0.33681, 89, -11.56, -188.25, 0.66319, 2, 90, 11.67, 326.28, 0.27628, 89, -10.81, -166.8, 0.72372, 2, 90, 12.66, 348.21, 0.21926, 89, -10.11, -145.31, 0.78074, 2, 90, 13.57, 370.17, 0.16667, 89, -9.46, -123.75, 0.83333, 2, 90, 14.38, 392.16, 0.11952, 89, -8.88, -102.12, 0.88048, 2, 90, 15.09, 414.19, 0.07874, 89, -8.38, -80.41, 0.92126, 2, 90, 15.67, 436.25, 0.04529, 89, -7.96, -58.6, 0.95471, 2, 90, 16.1, 458.37, 0.02028, 89, -7.65, -36.67, 0.97972, 2, 90, 16.37, 480.54, 0.0047, 89, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 90, 31.51, -3.58, 0.97968, 89, 12.5, -491.28, 0.02032, 2, 90, -0.55, -3.56, 0.98313, 89, -19.54, -491.23, 0.01687, 2, 90, -32.49, -3.57, 0.97983, 89, -51.5, -491.28, 0.02017, 2, 90, -32.32, 18.63, 0.96967, 89, -51.37, -469.15, 0.03033, 2, 90, -32.02, 40.79, 0.95244, 89, -51.16, -447.12, 0.04756, 2, 90, -31.6, 62.91, 0.92796, 89, -50.86, -425.19, 0.07204, 2, 90, -31.05, 84.99, 0.8963, 89, -50.47, -403.35, 0.1037, 2, 90, -30.38, 107.03, 0.8578, 89, -49.99, -381.61, 0.1422, 2, 90, -29.61, 129.03, 0.81309, 89, -49.44, -359.95, 0.18691, 2, 90, -28.74, 151, 0.76302, 89, -48.82, -338.36, 0.23698, 2, 90, -27.8, 172.95, 0.70854, 89, -48.15, -316.83, 0.29146, 2, 90, -26.8, 194.88, 0.65065, 89, -47.44, -295.35, 0.34934, 2, 90, -25.76, 216.79, 0.59038, 89, -46.69, -273.89, 0.40962, 2, 90, -24.69, 238.69, 0.52871, 89, -45.93, -252.46, 0.47129, 2, 90, -23.62, 260.6, 0.46666, 89, -45.16, -231.03, 0.53334, 2, 90, -22.56, 282.5, 0.4052, 89, -44.41, -209.59, 0.5948, 2, 90, -21.52, 304.42, 0.34532, 89, -43.67, -188.14, 0.65468, 2, 90, -20.53, 326.35, 0.28798, 89, -42.96, -166.65, 0.71202, 2, 90, -19.6, 348.3, 0.23415, 89, -42.29, -145.11, 0.76585, 2, 90, -18.74, 370.28, 0.18476, 89, -41.68, -123.51, 0.81524, 2, 90, -17.98, 392.28, 0.14072, 89, -41.14, -101.84, 0.85928, 2, 90, -17.33, 414.33, 0.10281, 89, -40.67, -80.09, 0.89719, 2, 90, -16.79, 436.41, 0.07161, 89, -40.29, -58.24, 0.92839, 2, 90, -16.37, 458.53, 0.04744, 89, -39.99, -36.31, 0.95256, 2, 90, -16.07, 480.69, 0.03036, 89, -39.78, -14.28, 0.96964, 2, 90, -15.9, 502.89, 0.02023, 89, -39.65, 7.85, 0.97977, 2, 90, 16.16, 502.87, 0.01685, 89, -7.61, 7.8, 0.98315, 2, 90, 48.1, 502.89, 0.02016, 89, 24.35, 7.85, 0.97984, 2, 90, 47.93, 480.69, 0.03025, 89, 24.22, -14.28, 0.96975, 2, 90, 47.63, 458.53, 0.0473, 89, 24.01, -36.31, 0.9527, 2, 90, 47.22, 436.41, 0.07146, 89, 23.71, -58.25, 0.92854, 2, 90, 46.68, 414.33, 0.10268, 89, 23.33, -80.09, 0.89732, 2, 90, 46.02, 392.28, 0.14062, 89, 22.86, -101.84, 0.85938, 2, 90, 45.26, 370.28, 0.18469, 89, 22.32, -123.51, 0.81531, 2, 90, 44.4, 348.3, 0.2341, 89, 21.71, -145.11, 0.7659, 2, 90, 43.47, 326.35, 0.28795, 89, 21.04, -166.65, 0.71205, 2, 90, 42.48, 304.42, 0.3453, 89, 20.33, -188.14, 0.6547, 2, 90, 41.44, 282.5, 0.40519, 89, 19.59, -209.59, 0.59481, 2, 90, 40.38, 260.6, 0.46665, 89, 18.84, -231.03, 0.53335, 2, 90, 39.31, 238.69, 0.5287, 89, 18.07, -252.46, 0.4713, 2, 90, 38.24, 216.79, 0.59036, 89, 17.31, -273.89, 0.40964, 2, 90, 37.2, 194.88, 0.65063, 89, 16.56, -295.35, 0.34937, 2, 90, 36.2, 172.95, 0.7085, 89, 15.85, -316.83, 0.2915, 2, 90, 35.26, 151, 0.76294, 89, 15.18, -338.36, 0.23706, 2, 90, 34.39, 129.03, 0.81296, 89, 14.56, -359.95, 0.18704, 2, 90, 33.62, 107.02, 0.8576, 89, 14.01, -381.61, 0.1424, 2, 90, 32.96, 84.99, 0.89604, 89, 13.54, -403.36, 0.10396, 2, 90, 32.41, 62.91, 0.92765, 89, 13.15, -425.2, 0.07235, 2, 90, 31.99, 40.79, 0.95212, 89, 12.84, -447.13, 0.04788, 2, 90, 31.69, 18.63, 0.96941, 89, 12.63, -469.16, 0.03059, 2, 90, -0.76, 18.77, 0.99526, 89, -19.69, -468.81, 0.00474, 2, 90, -0.49, 40.95, 0.97983, 89, -19.5, -446.76, 0.02017, 2, 90, -0.06, 63.06, 0.95465, 89, -19.19, -424.83, 0.04535, 2, 90, 0.53, 85.13, 0.92077, 89, -18.77, -403.03, 0.07923, 2, 90, 1.25, 107.15, 0.87928, 89, -18.26, -381.32, 0.12072, 2, 90, 2.08, 129.13, 0.83126, 89, -17.67, -359.71, 0.16874, 2, 90, 3, 151.09, 0.77776, 89, -17, -338.16, 0.22224, 2, 90, 4, 173.01, 0.71985, 89, -16.29, -316.68, 0.28015, 2, 90, 5.06, 194.92, 0.65855, 89, -15.53, -295.24, 0.34145, 2, 90, 6.16, 216.81, 0.59491, 89, -14.75, -273.83, 0.40509, 2, 90, 7.29, 238.7, 0.52993, 89, -13.95, -252.44, 0.47007, 2, 90, 8.42, 260.59, 0.46461, 89, -13.14, -231.06, 0.53539, 2, 90, 9.54, 282.47, 0.39991, 89, -12.34, -209.67, 0.60009, 2, 90, 10.63, 304.37, 0.33681, 89, -11.56, -188.25, 0.66319, 2, 90, 11.67, 326.28, 0.27628, 89, -10.81, -166.8, 0.72372, 2, 90, 12.66, 348.21, 0.21926, 89, -10.11, -145.31, 0.78074, 2, 90, 13.57, 370.17, 0.16667, 89, -9.46, -123.75, 0.83333, 2, 90, 14.38, 392.16, 0.11952, 89, -8.88, -102.12, 0.88048, 2, 90, 15.09, 414.19, 0.07874, 89, -8.38, -80.41, 0.92126, 2, 90, 15.67, 436.25, 0.04529, 89, -7.96, -58.6, 0.95471, 2, 90, 16.1, 458.37, 0.02028, 89, -7.65, -36.67, 0.97972, 2, 90, 16.37, 480.54, 0.0047, 89, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 90, 31.51, -3.58, 0.97968, 89, 12.5, -491.28, 0.02032, 2, 90, -0.55, -3.56, 0.98313, 89, -19.54, -491.23, 0.01687, 2, 90, -32.49, -3.57, 0.97983, 89, -51.5, -491.28, 0.02017, 2, 90, -32.32, 18.63, 0.96967, 89, -51.37, -469.15, 0.03033, 2, 90, -32.02, 40.79, 0.95244, 89, -51.16, -447.12, 0.04756, 2, 90, -31.6, 62.91, 0.92796, 89, -50.86, -425.19, 0.07204, 2, 90, -31.05, 84.99, 0.8963, 89, -50.47, -403.35, 0.1037, 2, 90, -30.38, 107.03, 0.8578, 89, -49.99, -381.61, 0.1422, 2, 90, -29.61, 129.03, 0.81309, 89, -49.44, -359.95, 0.18691, 2, 90, -28.74, 151, 0.76302, 89, -48.82, -338.36, 0.23698, 2, 90, -27.8, 172.95, 0.70854, 89, -48.15, -316.83, 0.29146, 2, 90, -26.8, 194.88, 0.65065, 89, -47.44, -295.35, 0.34934, 2, 90, -25.76, 216.79, 0.59038, 89, -46.69, -273.89, 0.40962, 2, 90, -24.69, 238.69, 0.52871, 89, -45.93, -252.46, 0.47129, 2, 90, -23.62, 260.6, 0.46666, 89, -45.16, -231.03, 0.53334, 2, 90, -22.56, 282.5, 0.4052, 89, -44.41, -209.59, 0.5948, 2, 90, -21.52, 304.42, 0.34532, 89, -43.67, -188.14, 0.65468, 2, 90, -20.53, 326.35, 0.28798, 89, -42.96, -166.65, 0.71202, 2, 90, -19.6, 348.3, 0.23415, 89, -42.29, -145.11, 0.76585, 2, 90, -18.74, 370.28, 0.18476, 89, -41.68, -123.51, 0.81524, 2, 90, -17.98, 392.28, 0.14072, 89, -41.14, -101.84, 0.85928, 2, 90, -17.33, 414.33, 0.10281, 89, -40.67, -80.09, 0.89719, 2, 90, -16.79, 436.41, 0.07161, 89, -40.29, -58.24, 0.92839, 2, 90, -16.37, 458.53, 0.04744, 89, -39.99, -36.31, 0.95256, 2, 90, -16.07, 480.69, 0.03036, 89, -39.78, -14.28, 0.96964, 2, 90, -15.9, 502.89, 0.02023, 89, -39.65, 7.85, 0.97977, 2, 90, 16.16, 502.87, 0.01685, 89, -7.61, 7.8, 0.98315, 2, 90, 48.1, 502.89, 0.02016, 89, 24.35, 7.85, 0.97984, 2, 90, 47.93, 480.69, 0.03025, 89, 24.22, -14.28, 0.96975, 2, 90, 47.63, 458.53, 0.0473, 89, 24.01, -36.31, 0.9527, 2, 90, 47.22, 436.41, 0.07146, 89, 23.71, -58.25, 0.92854, 2, 90, 46.68, 414.33, 0.10268, 89, 23.33, -80.09, 0.89732, 2, 90, 46.02, 392.28, 0.14062, 89, 22.86, -101.84, 0.85938, 2, 90, 45.26, 370.28, 0.18469, 89, 22.32, -123.51, 0.81531, 2, 90, 44.4, 348.3, 0.2341, 89, 21.71, -145.11, 0.7659, 2, 90, 43.47, 326.35, 0.28795, 89, 21.04, -166.65, 0.71205, 2, 90, 42.48, 304.42, 0.3453, 89, 20.33, -188.14, 0.6547, 2, 90, 41.44, 282.5, 0.40519, 89, 19.59, -209.59, 0.59481, 2, 90, 40.38, 260.6, 0.46665, 89, 18.84, -231.03, 0.53335, 2, 90, 39.31, 238.69, 0.5287, 89, 18.07, -252.46, 0.4713, 2, 90, 38.24, 216.79, 0.59036, 89, 17.31, -273.89, 0.40964, 2, 90, 37.2, 194.88, 0.65063, 89, 16.56, -295.35, 0.34937, 2, 90, 36.2, 172.95, 0.7085, 89, 15.85, -316.83, 0.2915, 2, 90, 35.26, 151, 0.76294, 89, 15.18, -338.36, 0.23706, 2, 90, 34.39, 129.03, 0.81296, 89, 14.56, -359.95, 0.18704, 2, 90, 33.62, 107.02, 0.8576, 89, 14.01, -381.61, 0.1424, 2, 90, 32.96, 84.99, 0.89604, 89, 13.54, -403.36, 0.10396, 2, 90, 32.41, 62.91, 0.92765, 89, 13.15, -425.2, 0.07235, 2, 90, 31.99, 40.79, 0.95212, 89, 12.84, -447.13, 0.04788, 2, 90, 31.69, 18.63, 0.96941, 89, 12.63, -469.16, 0.03059, 2, 90, -0.76, 18.77, 0.99526, 89, -19.69, -468.81, 0.00474, 2, 90, -0.49, 40.95, 0.97983, 89, -19.5, -446.76, 0.02017, 2, 90, -0.06, 63.06, 0.95465, 89, -19.19, -424.83, 0.04535, 2, 90, 0.53, 85.13, 0.92077, 89, -18.77, -403.03, 0.07923, 2, 90, 1.25, 107.15, 0.87928, 89, -18.26, -381.32, 0.12072, 2, 90, 2.08, 129.13, 0.83126, 89, -17.67, -359.71, 0.16874, 2, 90, 3, 151.09, 0.77776, 89, -17, -338.16, 0.22224, 2, 90, 4, 173.01, 0.71985, 89, -16.29, -316.68, 0.28015, 2, 90, 5.06, 194.92, 0.65855, 89, -15.53, -295.24, 0.34145, 2, 90, 6.16, 216.81, 0.59491, 89, -14.75, -273.83, 0.40509, 2, 90, 7.29, 238.7, 0.52993, 89, -13.95, -252.44, 0.47007, 2, 90, 8.42, 260.59, 0.46461, 89, -13.14, -231.06, 0.53539, 2, 90, 9.54, 282.47, 0.39991, 89, -12.34, -209.67, 0.60009, 2, 90, 10.63, 304.37, 0.33681, 89, -11.56, -188.25, 0.66319, 2, 90, 11.67, 326.28, 0.27628, 89, -10.81, -166.8, 0.72372, 2, 90, 12.66, 348.21, 0.21926, 89, -10.11, -145.31, 0.78074, 2, 90, 13.57, 370.17, 0.16667, 89, -9.46, -123.75, 0.83333, 2, 90, 14.38, 392.16, 0.11952, 89, -8.88, -102.12, 0.88048, 2, 90, 15.09, 414.19, 0.07874, 89, -8.38, -80.41, 0.92126, 2, 90, 15.67, 436.25, 0.04529, 89, -7.96, -58.6, 0.95471, 2, 90, 16.1, 458.37, 0.02028, 89, -7.65, -36.67, 0.97972, 2, 90, 16.37, 480.54, 0.0047, 89, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 90, 31.51, -3.58, 0.97968, 89, 12.5, -491.28, 0.02032, 2, 90, -0.55, -3.56, 0.98313, 89, -19.54, -491.23, 0.01687, 2, 90, -32.49, -3.57, 0.97983, 89, -51.5, -491.28, 0.02017, 2, 90, -32.32, 18.63, 0.96967, 89, -51.37, -469.15, 0.03033, 2, 90, -32.02, 40.79, 0.95244, 89, -51.16, -447.12, 0.04756, 2, 90, -31.6, 62.91, 0.92796, 89, -50.86, -425.19, 0.07204, 2, 90, -31.05, 84.99, 0.8963, 89, -50.47, -403.35, 0.1037, 2, 90, -30.38, 107.03, 0.8578, 89, -49.99, -381.61, 0.1422, 2, 90, -29.61, 129.03, 0.81309, 89, -49.44, -359.95, 0.18691, 2, 90, -28.74, 151, 0.76302, 89, -48.82, -338.36, 0.23698, 2, 90, -27.8, 172.95, 0.70854, 89, -48.15, -316.83, 0.29146, 2, 90, -26.8, 194.88, 0.65065, 89, -47.44, -295.35, 0.34934, 2, 90, -25.76, 216.79, 0.59038, 89, -46.69, -273.89, 0.40962, 2, 90, -24.69, 238.69, 0.52871, 89, -45.93, -252.46, 0.47129, 2, 90, -23.62, 260.6, 0.46666, 89, -45.16, -231.03, 0.53334, 2, 90, -22.56, 282.5, 0.4052, 89, -44.41, -209.59, 0.5948, 2, 90, -21.52, 304.42, 0.34532, 89, -43.67, -188.14, 0.65468, 2, 90, -20.53, 326.35, 0.28798, 89, -42.96, -166.65, 0.71202, 2, 90, -19.6, 348.3, 0.23415, 89, -42.29, -145.11, 0.76585, 2, 90, -18.74, 370.28, 0.18476, 89, -41.68, -123.51, 0.81524, 2, 90, -17.98, 392.28, 0.14072, 89, -41.14, -101.84, 0.85928, 2, 90, -17.33, 414.33, 0.10281, 89, -40.67, -80.09, 0.89719, 2, 90, -16.79, 436.41, 0.07161, 89, -40.29, -58.24, 0.92839, 2, 90, -16.37, 458.53, 0.04744, 89, -39.99, -36.31, 0.95256, 2, 90, -16.07, 480.69, 0.03036, 89, -39.78, -14.28, 0.96964, 2, 90, -15.9, 502.89, 0.02023, 89, -39.65, 7.85, 0.97977, 2, 90, 16.16, 502.87, 0.01685, 89, -7.61, 7.8, 0.98315, 2, 90, 48.1, 502.89, 0.02016, 89, 24.35, 7.85, 0.97984, 2, 90, 47.93, 480.69, 0.03025, 89, 24.22, -14.28, 0.96975, 2, 90, 47.63, 458.53, 0.0473, 89, 24.01, -36.31, 0.9527, 2, 90, 47.22, 436.41, 0.07146, 89, 23.71, -58.25, 0.92854, 2, 90, 46.68, 414.33, 0.10268, 89, 23.33, -80.09, 0.89732, 2, 90, 46.02, 392.28, 0.14062, 89, 22.86, -101.84, 0.85938, 2, 90, 45.26, 370.28, 0.18469, 89, 22.32, -123.51, 0.81531, 2, 90, 44.4, 348.3, 0.2341, 89, 21.71, -145.11, 0.7659, 2, 90, 43.47, 326.35, 0.28795, 89, 21.04, -166.65, 0.71205, 2, 90, 42.48, 304.42, 0.3453, 89, 20.33, -188.14, 0.6547, 2, 90, 41.44, 282.5, 0.40519, 89, 19.59, -209.59, 0.59481, 2, 90, 40.38, 260.6, 0.46665, 89, 18.84, -231.03, 0.53335, 2, 90, 39.31, 238.69, 0.5287, 89, 18.07, -252.46, 0.4713, 2, 90, 38.24, 216.79, 0.59036, 89, 17.31, -273.89, 0.40964, 2, 90, 37.2, 194.88, 0.65063, 89, 16.56, -295.35, 0.34937, 2, 90, 36.2, 172.95, 0.7085, 89, 15.85, -316.83, 0.2915, 2, 90, 35.26, 151, 0.76294, 89, 15.18, -338.36, 0.23706, 2, 90, 34.39, 129.03, 0.81296, 89, 14.56, -359.95, 0.18704, 2, 90, 33.62, 107.02, 0.8576, 89, 14.01, -381.61, 0.1424, 2, 90, 32.96, 84.99, 0.89604, 89, 13.54, -403.36, 0.10396, 2, 90, 32.41, 62.91, 0.92765, 89, 13.15, -425.2, 0.07235, 2, 90, 31.99, 40.79, 0.95212, 89, 12.84, -447.13, 0.04788, 2, 90, 31.69, 18.63, 0.96941, 89, 12.63, -469.16, 0.03059, 2, 90, -0.76, 18.77, 0.99526, 89, -19.69, -468.81, 0.00474, 2, 90, -0.49, 40.95, 0.97983, 89, -19.5, -446.76, 0.02017, 2, 90, -0.06, 63.06, 0.95465, 89, -19.19, -424.83, 0.04535, 2, 90, 0.53, 85.13, 0.92077, 89, -18.77, -403.03, 0.07923, 2, 90, 1.25, 107.15, 0.87928, 89, -18.26, -381.32, 0.12072, 2, 90, 2.08, 129.13, 0.83126, 89, -17.67, -359.71, 0.16874, 2, 90, 3, 151.09, 0.77776, 89, -17, -338.16, 0.22224, 2, 90, 4, 173.01, 0.71985, 89, -16.29, -316.68, 0.28015, 2, 90, 5.06, 194.92, 0.65855, 89, -15.53, -295.24, 0.34145, 2, 90, 6.16, 216.81, 0.59491, 89, -14.75, -273.83, 0.40509, 2, 90, 7.29, 238.7, 0.52993, 89, -13.95, -252.44, 0.47007, 2, 90, 8.42, 260.59, 0.46461, 89, -13.14, -231.06, 0.53539, 2, 90, 9.54, 282.47, 0.39991, 89, -12.34, -209.67, 0.60009, 2, 90, 10.63, 304.37, 0.33681, 89, -11.56, -188.25, 0.66319, 2, 90, 11.67, 326.28, 0.27628, 89, -10.81, -166.8, 0.72372, 2, 90, 12.66, 348.21, 0.21926, 89, -10.11, -145.31, 0.78074, 2, 90, 13.57, 370.17, 0.16667, 89, -9.46, -123.75, 0.83333, 2, 90, 14.38, 392.16, 0.11952, 89, -8.88, -102.12, 0.88048, 2, 90, 15.09, 414.19, 0.07874, 89, -8.38, -80.41, 0.92126, 2, 90, 15.67, 436.25, 0.04529, 89, -7.96, -58.6, 0.95471, 2, 90, 16.1, 458.37, 0.02028, 89, -7.65, -36.67, 0.97972, 2, 90, 16.37, 480.54, 0.0047, 89, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}}, "lighting_02": {"lighting_01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 95, 31.51, -3.58, 0.97968, 93, 12.5, -491.28, 0.02032, 2, 95, -0.55, -3.56, 0.98313, 93, -19.54, -491.23, 0.01687, 2, 95, -32.49, -3.57, 0.97983, 93, -51.5, -491.28, 0.02017, 2, 95, -32.32, 18.63, 0.96967, 93, -51.37, -469.15, 0.03033, 2, 95, -32.02, 40.79, 0.95244, 93, -51.16, -447.12, 0.04756, 2, 95, -31.6, 62.91, 0.92796, 93, -50.86, -425.19, 0.07204, 2, 95, -31.05, 84.99, 0.8963, 93, -50.47, -403.35, 0.1037, 2, 95, -30.38, 107.03, 0.8578, 93, -49.99, -381.61, 0.1422, 2, 95, -29.61, 129.03, 0.81309, 93, -49.44, -359.95, 0.18691, 2, 95, -28.74, 151, 0.76302, 93, -48.82, -338.36, 0.23698, 2, 95, -27.8, 172.95, 0.70854, 93, -48.15, -316.83, 0.29146, 2, 95, -26.8, 194.88, 0.65065, 93, -47.44, -295.35, 0.34934, 2, 95, -25.76, 216.79, 0.59038, 93, -46.69, -273.89, 0.40962, 2, 95, -24.69, 238.69, 0.52871, 93, -45.93, -252.46, 0.47129, 2, 95, -23.62, 260.6, 0.46666, 93, -45.16, -231.03, 0.53334, 2, 95, -22.56, 282.5, 0.4052, 93, -44.41, -209.59, 0.5948, 2, 95, -21.52, 304.42, 0.34532, 93, -43.67, -188.14, 0.65468, 2, 95, -20.53, 326.35, 0.28798, 93, -42.96, -166.65, 0.71202, 2, 95, -19.6, 348.3, 0.23415, 93, -42.29, -145.11, 0.76585, 2, 95, -18.74, 370.28, 0.18476, 93, -41.68, -123.51, 0.81524, 2, 95, -17.98, 392.28, 0.14072, 93, -41.14, -101.84, 0.85928, 2, 95, -17.33, 414.33, 0.10281, 93, -40.67, -80.09, 0.89719, 2, 95, -16.79, 436.41, 0.07161, 93, -40.29, -58.24, 0.92839, 2, 95, -16.37, 458.53, 0.04744, 93, -39.99, -36.31, 0.95256, 2, 95, -16.07, 480.69, 0.03036, 93, -39.78, -14.28, 0.96964, 2, 95, -15.9, 502.89, 0.02023, 93, -39.65, 7.85, 0.97977, 2, 95, 16.16, 502.87, 0.01685, 93, -7.61, 7.8, 0.98315, 2, 95, 48.1, 502.89, 0.02016, 93, 24.35, 7.85, 0.97984, 2, 95, 47.93, 480.69, 0.03025, 93, 24.22, -14.28, 0.96975, 2, 95, 47.63, 458.53, 0.0473, 93, 24.01, -36.31, 0.9527, 2, 95, 47.22, 436.41, 0.07146, 93, 23.71, -58.25, 0.92854, 2, 95, 46.68, 414.33, 0.10268, 93, 23.33, -80.09, 0.89732, 2, 95, 46.02, 392.28, 0.14062, 93, 22.86, -101.84, 0.85938, 2, 95, 45.26, 370.28, 0.18469, 93, 22.32, -123.51, 0.81531, 2, 95, 44.4, 348.3, 0.2341, 93, 21.71, -145.11, 0.7659, 2, 95, 43.47, 326.35, 0.28795, 93, 21.04, -166.65, 0.71205, 2, 95, 42.48, 304.42, 0.3453, 93, 20.33, -188.14, 0.6547, 2, 95, 41.44, 282.5, 0.40519, 93, 19.59, -209.59, 0.59481, 2, 95, 40.38, 260.6, 0.46665, 93, 18.84, -231.03, 0.53335, 2, 95, 39.31, 238.69, 0.5287, 93, 18.07, -252.46, 0.4713, 2, 95, 38.24, 216.79, 0.59036, 93, 17.31, -273.89, 0.40964, 2, 95, 37.2, 194.88, 0.65063, 93, 16.56, -295.35, 0.34937, 2, 95, 36.2, 172.95, 0.7085, 93, 15.85, -316.83, 0.2915, 2, 95, 35.26, 151, 0.76294, 93, 15.18, -338.36, 0.23706, 2, 95, 34.39, 129.03, 0.81296, 93, 14.56, -359.95, 0.18704, 2, 95, 33.62, 107.02, 0.8576, 93, 14.01, -381.61, 0.1424, 2, 95, 32.96, 84.99, 0.89604, 93, 13.54, -403.36, 0.10396, 2, 95, 32.41, 62.91, 0.92765, 93, 13.15, -425.2, 0.07235, 2, 95, 31.99, 40.79, 0.95212, 93, 12.84, -447.13, 0.04788, 2, 95, 31.69, 18.63, 0.96941, 93, 12.63, -469.16, 0.03059, 2, 95, -0.76, 18.77, 0.99526, 93, -19.69, -468.81, 0.00474, 2, 95, -0.49, 40.95, 0.97983, 93, -19.5, -446.76, 0.02017, 2, 95, -0.06, 63.06, 0.95465, 93, -19.19, -424.83, 0.04535, 2, 95, 0.53, 85.13, 0.92077, 93, -18.77, -403.03, 0.07923, 2, 95, 1.25, 107.15, 0.87928, 93, -18.26, -381.32, 0.12072, 2, 95, 2.08, 129.13, 0.83126, 93, -17.67, -359.71, 0.16874, 2, 95, 3, 151.09, 0.77776, 93, -17, -338.16, 0.22224, 2, 95, 4, 173.01, 0.71985, 93, -16.29, -316.68, 0.28015, 2, 95, 5.06, 194.92, 0.65855, 93, -15.53, -295.24, 0.34145, 2, 95, 6.16, 216.81, 0.59491, 93, -14.75, -273.83, 0.40509, 2, 95, 7.29, 238.7, 0.52993, 93, -13.95, -252.44, 0.47007, 2, 95, 8.42, 260.59, 0.46461, 93, -13.14, -231.06, 0.53539, 2, 95, 9.54, 282.47, 0.39991, 93, -12.34, -209.67, 0.60009, 2, 95, 10.63, 304.37, 0.33681, 93, -11.56, -188.25, 0.66319, 2, 95, 11.67, 326.28, 0.27628, 93, -10.81, -166.8, 0.72372, 2, 95, 12.66, 348.21, 0.21926, 93, -10.11, -145.31, 0.78074, 2, 95, 13.57, 370.17, 0.16667, 93, -9.46, -123.75, 0.83333, 2, 95, 14.38, 392.16, 0.11952, 93, -8.88, -102.12, 0.88048, 2, 95, 15.09, 414.19, 0.07874, 93, -8.38, -80.41, 0.92126, 2, 95, 15.67, 436.25, 0.04529, 93, -7.96, -58.6, 0.95471, 2, 95, 16.1, 458.37, 0.02028, 93, -7.65, -36.67, 0.97972, 2, 95, 16.37, 480.54, 0.0047, 93, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 95, 31.51, -3.58, 0.97968, 93, 12.5, -491.28, 0.02032, 2, 95, -0.55, -3.56, 0.98313, 93, -19.54, -491.23, 0.01687, 2, 95, -32.49, -3.57, 0.97983, 93, -51.5, -491.28, 0.02017, 2, 95, -32.32, 18.63, 0.96967, 93, -51.37, -469.15, 0.03033, 2, 95, -32.02, 40.79, 0.95244, 93, -51.16, -447.12, 0.04756, 2, 95, -31.6, 62.91, 0.92796, 93, -50.86, -425.19, 0.07204, 2, 95, -31.05, 84.99, 0.8963, 93, -50.47, -403.35, 0.1037, 2, 95, -30.38, 107.03, 0.8578, 93, -49.99, -381.61, 0.1422, 2, 95, -29.61, 129.03, 0.81309, 93, -49.44, -359.95, 0.18691, 2, 95, -28.74, 151, 0.76302, 93, -48.82, -338.36, 0.23698, 2, 95, -27.8, 172.95, 0.70854, 93, -48.15, -316.83, 0.29146, 2, 95, -26.8, 194.88, 0.65065, 93, -47.44, -295.35, 0.34934, 2, 95, -25.76, 216.79, 0.59038, 93, -46.69, -273.89, 0.40962, 2, 95, -24.69, 238.69, 0.52871, 93, -45.93, -252.46, 0.47129, 2, 95, -23.62, 260.6, 0.46666, 93, -45.16, -231.03, 0.53334, 2, 95, -22.56, 282.5, 0.4052, 93, -44.41, -209.59, 0.5948, 2, 95, -21.52, 304.42, 0.34532, 93, -43.67, -188.14, 0.65468, 2, 95, -20.53, 326.35, 0.28798, 93, -42.96, -166.65, 0.71202, 2, 95, -19.6, 348.3, 0.23415, 93, -42.29, -145.11, 0.76585, 2, 95, -18.74, 370.28, 0.18476, 93, -41.68, -123.51, 0.81524, 2, 95, -17.98, 392.28, 0.14072, 93, -41.14, -101.84, 0.85928, 2, 95, -17.33, 414.33, 0.10281, 93, -40.67, -80.09, 0.89719, 2, 95, -16.79, 436.41, 0.07161, 93, -40.29, -58.24, 0.92839, 2, 95, -16.37, 458.53, 0.04744, 93, -39.99, -36.31, 0.95256, 2, 95, -16.07, 480.69, 0.03036, 93, -39.78, -14.28, 0.96964, 2, 95, -15.9, 502.89, 0.02023, 93, -39.65, 7.85, 0.97977, 2, 95, 16.16, 502.87, 0.01685, 93, -7.61, 7.8, 0.98315, 2, 95, 48.1, 502.89, 0.02016, 93, 24.35, 7.85, 0.97984, 2, 95, 47.93, 480.69, 0.03025, 93, 24.22, -14.28, 0.96975, 2, 95, 47.63, 458.53, 0.0473, 93, 24.01, -36.31, 0.9527, 2, 95, 47.22, 436.41, 0.07146, 93, 23.71, -58.25, 0.92854, 2, 95, 46.68, 414.33, 0.10268, 93, 23.33, -80.09, 0.89732, 2, 95, 46.02, 392.28, 0.14062, 93, 22.86, -101.84, 0.85938, 2, 95, 45.26, 370.28, 0.18469, 93, 22.32, -123.51, 0.81531, 2, 95, 44.4, 348.3, 0.2341, 93, 21.71, -145.11, 0.7659, 2, 95, 43.47, 326.35, 0.28795, 93, 21.04, -166.65, 0.71205, 2, 95, 42.48, 304.42, 0.3453, 93, 20.33, -188.14, 0.6547, 2, 95, 41.44, 282.5, 0.40519, 93, 19.59, -209.59, 0.59481, 2, 95, 40.38, 260.6, 0.46665, 93, 18.84, -231.03, 0.53335, 2, 95, 39.31, 238.69, 0.5287, 93, 18.07, -252.46, 0.4713, 2, 95, 38.24, 216.79, 0.59036, 93, 17.31, -273.89, 0.40964, 2, 95, 37.2, 194.88, 0.65063, 93, 16.56, -295.35, 0.34937, 2, 95, 36.2, 172.95, 0.7085, 93, 15.85, -316.83, 0.2915, 2, 95, 35.26, 151, 0.76294, 93, 15.18, -338.36, 0.23706, 2, 95, 34.39, 129.03, 0.81296, 93, 14.56, -359.95, 0.18704, 2, 95, 33.62, 107.02, 0.8576, 93, 14.01, -381.61, 0.1424, 2, 95, 32.96, 84.99, 0.89604, 93, 13.54, -403.36, 0.10396, 2, 95, 32.41, 62.91, 0.92765, 93, 13.15, -425.2, 0.07235, 2, 95, 31.99, 40.79, 0.95212, 93, 12.84, -447.13, 0.04788, 2, 95, 31.69, 18.63, 0.96941, 93, 12.63, -469.16, 0.03059, 2, 95, -0.76, 18.77, 0.99526, 93, -19.69, -468.81, 0.00474, 2, 95, -0.49, 40.95, 0.97983, 93, -19.5, -446.76, 0.02017, 2, 95, -0.06, 63.06, 0.95465, 93, -19.19, -424.83, 0.04535, 2, 95, 0.53, 85.13, 0.92077, 93, -18.77, -403.03, 0.07923, 2, 95, 1.25, 107.15, 0.87928, 93, -18.26, -381.32, 0.12072, 2, 95, 2.08, 129.13, 0.83126, 93, -17.67, -359.71, 0.16874, 2, 95, 3, 151.09, 0.77776, 93, -17, -338.16, 0.22224, 2, 95, 4, 173.01, 0.71985, 93, -16.29, -316.68, 0.28015, 2, 95, 5.06, 194.92, 0.65855, 93, -15.53, -295.24, 0.34145, 2, 95, 6.16, 216.81, 0.59491, 93, -14.75, -273.83, 0.40509, 2, 95, 7.29, 238.7, 0.52993, 93, -13.95, -252.44, 0.47007, 2, 95, 8.42, 260.59, 0.46461, 93, -13.14, -231.06, 0.53539, 2, 95, 9.54, 282.47, 0.39991, 93, -12.34, -209.67, 0.60009, 2, 95, 10.63, 304.37, 0.33681, 93, -11.56, -188.25, 0.66319, 2, 95, 11.67, 326.28, 0.27628, 93, -10.81, -166.8, 0.72372, 2, 95, 12.66, 348.21, 0.21926, 93, -10.11, -145.31, 0.78074, 2, 95, 13.57, 370.17, 0.16667, 93, -9.46, -123.75, 0.83333, 2, 95, 14.38, 392.16, 0.11952, 93, -8.88, -102.12, 0.88048, 2, 95, 15.09, 414.19, 0.07874, 93, -8.38, -80.41, 0.92126, 2, 95, 15.67, 436.25, 0.04529, 93, -7.96, -58.6, 0.95471, 2, 95, 16.1, 458.37, 0.02028, 93, -7.65, -36.67, 0.97972, 2, 95, 16.37, 480.54, 0.0047, 93, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 95, 31.51, -3.58, 0.97968, 93, 12.5, -491.28, 0.02032, 2, 95, -0.55, -3.56, 0.98313, 93, -19.54, -491.23, 0.01687, 2, 95, -32.49, -3.57, 0.97983, 93, -51.5, -491.28, 0.02017, 2, 95, -32.32, 18.63, 0.96967, 93, -51.37, -469.15, 0.03033, 2, 95, -32.02, 40.79, 0.95244, 93, -51.16, -447.12, 0.04756, 2, 95, -31.6, 62.91, 0.92796, 93, -50.86, -425.19, 0.07204, 2, 95, -31.05, 84.99, 0.8963, 93, -50.47, -403.35, 0.1037, 2, 95, -30.38, 107.03, 0.8578, 93, -49.99, -381.61, 0.1422, 2, 95, -29.61, 129.03, 0.81309, 93, -49.44, -359.95, 0.18691, 2, 95, -28.74, 151, 0.76302, 93, -48.82, -338.36, 0.23698, 2, 95, -27.8, 172.95, 0.70854, 93, -48.15, -316.83, 0.29146, 2, 95, -26.8, 194.88, 0.65065, 93, -47.44, -295.35, 0.34934, 2, 95, -25.76, 216.79, 0.59038, 93, -46.69, -273.89, 0.40962, 2, 95, -24.69, 238.69, 0.52871, 93, -45.93, -252.46, 0.47129, 2, 95, -23.62, 260.6, 0.46666, 93, -45.16, -231.03, 0.53334, 2, 95, -22.56, 282.5, 0.4052, 93, -44.41, -209.59, 0.5948, 2, 95, -21.52, 304.42, 0.34532, 93, -43.67, -188.14, 0.65468, 2, 95, -20.53, 326.35, 0.28798, 93, -42.96, -166.65, 0.71202, 2, 95, -19.6, 348.3, 0.23415, 93, -42.29, -145.11, 0.76585, 2, 95, -18.74, 370.28, 0.18476, 93, -41.68, -123.51, 0.81524, 2, 95, -17.98, 392.28, 0.14072, 93, -41.14, -101.84, 0.85928, 2, 95, -17.33, 414.33, 0.10281, 93, -40.67, -80.09, 0.89719, 2, 95, -16.79, 436.41, 0.07161, 93, -40.29, -58.24, 0.92839, 2, 95, -16.37, 458.53, 0.04744, 93, -39.99, -36.31, 0.95256, 2, 95, -16.07, 480.69, 0.03036, 93, -39.78, -14.28, 0.96964, 2, 95, -15.9, 502.89, 0.02023, 93, -39.65, 7.85, 0.97977, 2, 95, 16.16, 502.87, 0.01685, 93, -7.61, 7.8, 0.98315, 2, 95, 48.1, 502.89, 0.02016, 93, 24.35, 7.85, 0.97984, 2, 95, 47.93, 480.69, 0.03025, 93, 24.22, -14.28, 0.96975, 2, 95, 47.63, 458.53, 0.0473, 93, 24.01, -36.31, 0.9527, 2, 95, 47.22, 436.41, 0.07146, 93, 23.71, -58.25, 0.92854, 2, 95, 46.68, 414.33, 0.10268, 93, 23.33, -80.09, 0.89732, 2, 95, 46.02, 392.28, 0.14062, 93, 22.86, -101.84, 0.85938, 2, 95, 45.26, 370.28, 0.18469, 93, 22.32, -123.51, 0.81531, 2, 95, 44.4, 348.3, 0.2341, 93, 21.71, -145.11, 0.7659, 2, 95, 43.47, 326.35, 0.28795, 93, 21.04, -166.65, 0.71205, 2, 95, 42.48, 304.42, 0.3453, 93, 20.33, -188.14, 0.6547, 2, 95, 41.44, 282.5, 0.40519, 93, 19.59, -209.59, 0.59481, 2, 95, 40.38, 260.6, 0.46665, 93, 18.84, -231.03, 0.53335, 2, 95, 39.31, 238.69, 0.5287, 93, 18.07, -252.46, 0.4713, 2, 95, 38.24, 216.79, 0.59036, 93, 17.31, -273.89, 0.40964, 2, 95, 37.2, 194.88, 0.65063, 93, 16.56, -295.35, 0.34937, 2, 95, 36.2, 172.95, 0.7085, 93, 15.85, -316.83, 0.2915, 2, 95, 35.26, 151, 0.76294, 93, 15.18, -338.36, 0.23706, 2, 95, 34.39, 129.03, 0.81296, 93, 14.56, -359.95, 0.18704, 2, 95, 33.62, 107.02, 0.8576, 93, 14.01, -381.61, 0.1424, 2, 95, 32.96, 84.99, 0.89604, 93, 13.54, -403.36, 0.10396, 2, 95, 32.41, 62.91, 0.92765, 93, 13.15, -425.2, 0.07235, 2, 95, 31.99, 40.79, 0.95212, 93, 12.84, -447.13, 0.04788, 2, 95, 31.69, 18.63, 0.96941, 93, 12.63, -469.16, 0.03059, 2, 95, -0.76, 18.77, 0.99526, 93, -19.69, -468.81, 0.00474, 2, 95, -0.49, 40.95, 0.97983, 93, -19.5, -446.76, 0.02017, 2, 95, -0.06, 63.06, 0.95465, 93, -19.19, -424.83, 0.04535, 2, 95, 0.53, 85.13, 0.92077, 93, -18.77, -403.03, 0.07923, 2, 95, 1.25, 107.15, 0.87928, 93, -18.26, -381.32, 0.12072, 2, 95, 2.08, 129.13, 0.83126, 93, -17.67, -359.71, 0.16874, 2, 95, 3, 151.09, 0.77776, 93, -17, -338.16, 0.22224, 2, 95, 4, 173.01, 0.71985, 93, -16.29, -316.68, 0.28015, 2, 95, 5.06, 194.92, 0.65855, 93, -15.53, -295.24, 0.34145, 2, 95, 6.16, 216.81, 0.59491, 93, -14.75, -273.83, 0.40509, 2, 95, 7.29, 238.7, 0.52993, 93, -13.95, -252.44, 0.47007, 2, 95, 8.42, 260.59, 0.46461, 93, -13.14, -231.06, 0.53539, 2, 95, 9.54, 282.47, 0.39991, 93, -12.34, -209.67, 0.60009, 2, 95, 10.63, 304.37, 0.33681, 93, -11.56, -188.25, 0.66319, 2, 95, 11.67, 326.28, 0.27628, 93, -10.81, -166.8, 0.72372, 2, 95, 12.66, 348.21, 0.21926, 93, -10.11, -145.31, 0.78074, 2, 95, 13.57, 370.17, 0.16667, 93, -9.46, -123.75, 0.83333, 2, 95, 14.38, 392.16, 0.11952, 93, -8.88, -102.12, 0.88048, 2, 95, 15.09, 414.19, 0.07874, 93, -8.38, -80.41, 0.92126, 2, 95, 15.67, 436.25, 0.04529, 93, -7.96, -58.6, 0.95471, 2, 95, 16.1, 458.37, 0.02028, 93, -7.65, -36.67, 0.97972, 2, 95, 16.37, 480.54, 0.0047, 93, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 95, 31.51, -3.58, 0.97968, 93, 12.5, -491.28, 0.02032, 2, 95, -0.55, -3.56, 0.98313, 93, -19.54, -491.23, 0.01687, 2, 95, -32.49, -3.57, 0.97983, 93, -51.5, -491.28, 0.02017, 2, 95, -32.32, 18.63, 0.96967, 93, -51.37, -469.15, 0.03033, 2, 95, -32.02, 40.79, 0.95244, 93, -51.16, -447.12, 0.04756, 2, 95, -31.6, 62.91, 0.92796, 93, -50.86, -425.19, 0.07204, 2, 95, -31.05, 84.99, 0.8963, 93, -50.47, -403.35, 0.1037, 2, 95, -30.38, 107.03, 0.8578, 93, -49.99, -381.61, 0.1422, 2, 95, -29.61, 129.03, 0.81309, 93, -49.44, -359.95, 0.18691, 2, 95, -28.74, 151, 0.76302, 93, -48.82, -338.36, 0.23698, 2, 95, -27.8, 172.95, 0.70854, 93, -48.15, -316.83, 0.29146, 2, 95, -26.8, 194.88, 0.65065, 93, -47.44, -295.35, 0.34934, 2, 95, -25.76, 216.79, 0.59038, 93, -46.69, -273.89, 0.40962, 2, 95, -24.69, 238.69, 0.52871, 93, -45.93, -252.46, 0.47129, 2, 95, -23.62, 260.6, 0.46666, 93, -45.16, -231.03, 0.53334, 2, 95, -22.56, 282.5, 0.4052, 93, -44.41, -209.59, 0.5948, 2, 95, -21.52, 304.42, 0.34532, 93, -43.67, -188.14, 0.65468, 2, 95, -20.53, 326.35, 0.28798, 93, -42.96, -166.65, 0.71202, 2, 95, -19.6, 348.3, 0.23415, 93, -42.29, -145.11, 0.76585, 2, 95, -18.74, 370.28, 0.18476, 93, -41.68, -123.51, 0.81524, 2, 95, -17.98, 392.28, 0.14072, 93, -41.14, -101.84, 0.85928, 2, 95, -17.33, 414.33, 0.10281, 93, -40.67, -80.09, 0.89719, 2, 95, -16.79, 436.41, 0.07161, 93, -40.29, -58.24, 0.92839, 2, 95, -16.37, 458.53, 0.04744, 93, -39.99, -36.31, 0.95256, 2, 95, -16.07, 480.69, 0.03036, 93, -39.78, -14.28, 0.96964, 2, 95, -15.9, 502.89, 0.02023, 93, -39.65, 7.85, 0.97977, 2, 95, 16.16, 502.87, 0.01685, 93, -7.61, 7.8, 0.98315, 2, 95, 48.1, 502.89, 0.02016, 93, 24.35, 7.85, 0.97984, 2, 95, 47.93, 480.69, 0.03025, 93, 24.22, -14.28, 0.96975, 2, 95, 47.63, 458.53, 0.0473, 93, 24.01, -36.31, 0.9527, 2, 95, 47.22, 436.41, 0.07146, 93, 23.71, -58.25, 0.92854, 2, 95, 46.68, 414.33, 0.10268, 93, 23.33, -80.09, 0.89732, 2, 95, 46.02, 392.28, 0.14062, 93, 22.86, -101.84, 0.85938, 2, 95, 45.26, 370.28, 0.18469, 93, 22.32, -123.51, 0.81531, 2, 95, 44.4, 348.3, 0.2341, 93, 21.71, -145.11, 0.7659, 2, 95, 43.47, 326.35, 0.28795, 93, 21.04, -166.65, 0.71205, 2, 95, 42.48, 304.42, 0.3453, 93, 20.33, -188.14, 0.6547, 2, 95, 41.44, 282.5, 0.40519, 93, 19.59, -209.59, 0.59481, 2, 95, 40.38, 260.6, 0.46665, 93, 18.84, -231.03, 0.53335, 2, 95, 39.31, 238.69, 0.5287, 93, 18.07, -252.46, 0.4713, 2, 95, 38.24, 216.79, 0.59036, 93, 17.31, -273.89, 0.40964, 2, 95, 37.2, 194.88, 0.65063, 93, 16.56, -295.35, 0.34937, 2, 95, 36.2, 172.95, 0.7085, 93, 15.85, -316.83, 0.2915, 2, 95, 35.26, 151, 0.76294, 93, 15.18, -338.36, 0.23706, 2, 95, 34.39, 129.03, 0.81296, 93, 14.56, -359.95, 0.18704, 2, 95, 33.62, 107.02, 0.8576, 93, 14.01, -381.61, 0.1424, 2, 95, 32.96, 84.99, 0.89604, 93, 13.54, -403.36, 0.10396, 2, 95, 32.41, 62.91, 0.92765, 93, 13.15, -425.2, 0.07235, 2, 95, 31.99, 40.79, 0.95212, 93, 12.84, -447.13, 0.04788, 2, 95, 31.69, 18.63, 0.96941, 93, 12.63, -469.16, 0.03059, 2, 95, -0.76, 18.77, 0.99526, 93, -19.69, -468.81, 0.00474, 2, 95, -0.49, 40.95, 0.97983, 93, -19.5, -446.76, 0.02017, 2, 95, -0.06, 63.06, 0.95465, 93, -19.19, -424.83, 0.04535, 2, 95, 0.53, 85.13, 0.92077, 93, -18.77, -403.03, 0.07923, 2, 95, 1.25, 107.15, 0.87928, 93, -18.26, -381.32, 0.12072, 2, 95, 2.08, 129.13, 0.83126, 93, -17.67, -359.71, 0.16874, 2, 95, 3, 151.09, 0.77776, 93, -17, -338.16, 0.22224, 2, 95, 4, 173.01, 0.71985, 93, -16.29, -316.68, 0.28015, 2, 95, 5.06, 194.92, 0.65855, 93, -15.53, -295.24, 0.34145, 2, 95, 6.16, 216.81, 0.59491, 93, -14.75, -273.83, 0.40509, 2, 95, 7.29, 238.7, 0.52993, 93, -13.95, -252.44, 0.47007, 2, 95, 8.42, 260.59, 0.46461, 93, -13.14, -231.06, 0.53539, 2, 95, 9.54, 282.47, 0.39991, 93, -12.34, -209.67, 0.60009, 2, 95, 10.63, 304.37, 0.33681, 93, -11.56, -188.25, 0.66319, 2, 95, 11.67, 326.28, 0.27628, 93, -10.81, -166.8, 0.72372, 2, 95, 12.66, 348.21, 0.21926, 93, -10.11, -145.31, 0.78074, 2, 95, 13.57, 370.17, 0.16667, 93, -9.46, -123.75, 0.83333, 2, 95, 14.38, 392.16, 0.11952, 93, -8.88, -102.12, 0.88048, 2, 95, 15.09, 414.19, 0.07874, 93, -8.38, -80.41, 0.92126, 2, 95, 15.67, 436.25, 0.04529, 93, -7.96, -58.6, 0.95471, 2, 95, 16.1, 458.37, 0.02028, 93, -7.65, -36.67, 0.97972, 2, 95, 16.37, 480.54, 0.0047, 93, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}}, "lighting_03": {"lighting_01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 99, 31.51, -3.58, 0.97968, 97, 12.5, -491.28, 0.02032, 2, 99, -0.55, -3.56, 0.98313, 97, -19.54, -491.23, 0.01687, 2, 99, -32.49, -3.57, 0.97983, 97, -51.5, -491.28, 0.02017, 2, 99, -32.32, 18.63, 0.96967, 97, -51.37, -469.15, 0.03033, 2, 99, -32.02, 40.79, 0.95244, 97, -51.16, -447.12, 0.04756, 2, 99, -31.6, 62.91, 0.92796, 97, -50.86, -425.19, 0.07204, 2, 99, -31.05, 84.99, 0.8963, 97, -50.47, -403.35, 0.1037, 2, 99, -30.38, 107.03, 0.8578, 97, -49.99, -381.61, 0.1422, 2, 99, -29.61, 129.03, 0.81309, 97, -49.44, -359.95, 0.18691, 2, 99, -28.74, 151, 0.76302, 97, -48.82, -338.36, 0.23698, 2, 99, -27.8, 172.95, 0.70854, 97, -48.15, -316.83, 0.29146, 2, 99, -26.8, 194.88, 0.65065, 97, -47.44, -295.35, 0.34934, 2, 99, -25.76, 216.79, 0.59038, 97, -46.69, -273.89, 0.40962, 2, 99, -24.69, 238.69, 0.52871, 97, -45.93, -252.46, 0.47129, 2, 99, -23.62, 260.6, 0.46666, 97, -45.16, -231.03, 0.53334, 2, 99, -22.56, 282.5, 0.4052, 97, -44.41, -209.59, 0.5948, 2, 99, -21.52, 304.42, 0.34532, 97, -43.67, -188.14, 0.65468, 2, 99, -20.53, 326.35, 0.28798, 97, -42.96, -166.65, 0.71202, 2, 99, -19.6, 348.3, 0.23415, 97, -42.29, -145.11, 0.76585, 2, 99, -18.74, 370.28, 0.18476, 97, -41.68, -123.51, 0.81524, 2, 99, -17.98, 392.28, 0.14072, 97, -41.14, -101.84, 0.85928, 2, 99, -17.33, 414.33, 0.10281, 97, -40.67, -80.09, 0.89719, 2, 99, -16.79, 436.41, 0.07161, 97, -40.29, -58.24, 0.92839, 2, 99, -16.37, 458.53, 0.04744, 97, -39.99, -36.31, 0.95256, 2, 99, -16.07, 480.69, 0.03036, 97, -39.78, -14.28, 0.96964, 2, 99, -15.9, 502.89, 0.02023, 97, -39.65, 7.85, 0.97977, 2, 99, 16.16, 502.87, 0.01685, 97, -7.61, 7.8, 0.98315, 2, 99, 48.1, 502.89, 0.02016, 97, 24.35, 7.85, 0.97984, 2, 99, 47.93, 480.69, 0.03025, 97, 24.22, -14.28, 0.96975, 2, 99, 47.63, 458.53, 0.0473, 97, 24.01, -36.31, 0.9527, 2, 99, 47.22, 436.41, 0.07146, 97, 23.71, -58.25, 0.92854, 2, 99, 46.68, 414.33, 0.10268, 97, 23.33, -80.09, 0.89732, 2, 99, 46.02, 392.28, 0.14062, 97, 22.86, -101.84, 0.85938, 2, 99, 45.26, 370.28, 0.18469, 97, 22.32, -123.51, 0.81531, 2, 99, 44.4, 348.3, 0.2341, 97, 21.71, -145.11, 0.7659, 2, 99, 43.47, 326.35, 0.28795, 97, 21.04, -166.65, 0.71205, 2, 99, 42.48, 304.42, 0.3453, 97, 20.33, -188.14, 0.6547, 2, 99, 41.44, 282.5, 0.40519, 97, 19.59, -209.59, 0.59481, 2, 99, 40.38, 260.6, 0.46665, 97, 18.84, -231.03, 0.53335, 2, 99, 39.31, 238.69, 0.5287, 97, 18.07, -252.46, 0.4713, 2, 99, 38.24, 216.79, 0.59036, 97, 17.31, -273.89, 0.40964, 2, 99, 37.2, 194.88, 0.65063, 97, 16.56, -295.35, 0.34937, 2, 99, 36.2, 172.95, 0.7085, 97, 15.85, -316.83, 0.2915, 2, 99, 35.26, 151, 0.76294, 97, 15.18, -338.36, 0.23706, 2, 99, 34.39, 129.03, 0.81296, 97, 14.56, -359.95, 0.18704, 2, 99, 33.62, 107.02, 0.8576, 97, 14.01, -381.61, 0.1424, 2, 99, 32.96, 84.99, 0.89604, 97, 13.54, -403.36, 0.10396, 2, 99, 32.41, 62.91, 0.92765, 97, 13.15, -425.2, 0.07235, 2, 99, 31.99, 40.79, 0.95212, 97, 12.84, -447.13, 0.04788, 2, 99, 31.69, 18.63, 0.96941, 97, 12.63, -469.16, 0.03059, 2, 99, -0.76, 18.77, 0.99526, 97, -19.69, -468.81, 0.00474, 2, 99, -0.49, 40.95, 0.97983, 97, -19.5, -446.76, 0.02017, 2, 99, -0.06, 63.06, 0.95465, 97, -19.19, -424.83, 0.04535, 2, 99, 0.53, 85.13, 0.92077, 97, -18.77, -403.03, 0.07923, 2, 99, 1.25, 107.15, 0.87928, 97, -18.26, -381.32, 0.12072, 2, 99, 2.08, 129.13, 0.83126, 97, -17.67, -359.71, 0.16874, 2, 99, 3, 151.09, 0.77776, 97, -17, -338.16, 0.22224, 2, 99, 4, 173.01, 0.71985, 97, -16.29, -316.68, 0.28015, 2, 99, 5.06, 194.92, 0.65855, 97, -15.53, -295.24, 0.34145, 2, 99, 6.16, 216.81, 0.59491, 97, -14.75, -273.83, 0.40509, 2, 99, 7.29, 238.7, 0.52993, 97, -13.95, -252.44, 0.47007, 2, 99, 8.42, 260.59, 0.46461, 97, -13.14, -231.06, 0.53539, 2, 99, 9.54, 282.47, 0.39991, 97, -12.34, -209.67, 0.60009, 2, 99, 10.63, 304.37, 0.33681, 97, -11.56, -188.25, 0.66319, 2, 99, 11.67, 326.28, 0.27628, 97, -10.81, -166.8, 0.72372, 2, 99, 12.66, 348.21, 0.21926, 97, -10.11, -145.31, 0.78074, 2, 99, 13.57, 370.17, 0.16667, 97, -9.46, -123.75, 0.83333, 2, 99, 14.38, 392.16, 0.11952, 97, -8.88, -102.12, 0.88048, 2, 99, 15.09, 414.19, 0.07874, 97, -8.38, -80.41, 0.92126, 2, 99, 15.67, 436.25, 0.04529, 97, -7.96, -58.6, 0.95471, 2, 99, 16.1, 458.37, 0.02028, 97, -7.65, -36.67, 0.97972, 2, 99, 16.37, 480.54, 0.0047, 97, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 99, 31.51, -3.58, 0.97968, 97, 12.5, -491.28, 0.02032, 2, 99, -0.55, -3.56, 0.98313, 97, -19.54, -491.23, 0.01687, 2, 99, -32.49, -3.57, 0.97983, 97, -51.5, -491.28, 0.02017, 2, 99, -32.32, 18.63, 0.96967, 97, -51.37, -469.15, 0.03033, 2, 99, -32.02, 40.79, 0.95244, 97, -51.16, -447.12, 0.04756, 2, 99, -31.6, 62.91, 0.92796, 97, -50.86, -425.19, 0.07204, 2, 99, -31.05, 84.99, 0.8963, 97, -50.47, -403.35, 0.1037, 2, 99, -30.38, 107.03, 0.8578, 97, -49.99, -381.61, 0.1422, 2, 99, -29.61, 129.03, 0.81309, 97, -49.44, -359.95, 0.18691, 2, 99, -28.74, 151, 0.76302, 97, -48.82, -338.36, 0.23698, 2, 99, -27.8, 172.95, 0.70854, 97, -48.15, -316.83, 0.29146, 2, 99, -26.8, 194.88, 0.65065, 97, -47.44, -295.35, 0.34934, 2, 99, -25.76, 216.79, 0.59038, 97, -46.69, -273.89, 0.40962, 2, 99, -24.69, 238.69, 0.52871, 97, -45.93, -252.46, 0.47129, 2, 99, -23.62, 260.6, 0.46666, 97, -45.16, -231.03, 0.53334, 2, 99, -22.56, 282.5, 0.4052, 97, -44.41, -209.59, 0.5948, 2, 99, -21.52, 304.42, 0.34532, 97, -43.67, -188.14, 0.65468, 2, 99, -20.53, 326.35, 0.28798, 97, -42.96, -166.65, 0.71202, 2, 99, -19.6, 348.3, 0.23415, 97, -42.29, -145.11, 0.76585, 2, 99, -18.74, 370.28, 0.18476, 97, -41.68, -123.51, 0.81524, 2, 99, -17.98, 392.28, 0.14072, 97, -41.14, -101.84, 0.85928, 2, 99, -17.33, 414.33, 0.10281, 97, -40.67, -80.09, 0.89719, 2, 99, -16.79, 436.41, 0.07161, 97, -40.29, -58.24, 0.92839, 2, 99, -16.37, 458.53, 0.04744, 97, -39.99, -36.31, 0.95256, 2, 99, -16.07, 480.69, 0.03036, 97, -39.78, -14.28, 0.96964, 2, 99, -15.9, 502.89, 0.02023, 97, -39.65, 7.85, 0.97977, 2, 99, 16.16, 502.87, 0.01685, 97, -7.61, 7.8, 0.98315, 2, 99, 48.1, 502.89, 0.02016, 97, 24.35, 7.85, 0.97984, 2, 99, 47.93, 480.69, 0.03025, 97, 24.22, -14.28, 0.96975, 2, 99, 47.63, 458.53, 0.0473, 97, 24.01, -36.31, 0.9527, 2, 99, 47.22, 436.41, 0.07146, 97, 23.71, -58.25, 0.92854, 2, 99, 46.68, 414.33, 0.10268, 97, 23.33, -80.09, 0.89732, 2, 99, 46.02, 392.28, 0.14062, 97, 22.86, -101.84, 0.85938, 2, 99, 45.26, 370.28, 0.18469, 97, 22.32, -123.51, 0.81531, 2, 99, 44.4, 348.3, 0.2341, 97, 21.71, -145.11, 0.7659, 2, 99, 43.47, 326.35, 0.28795, 97, 21.04, -166.65, 0.71205, 2, 99, 42.48, 304.42, 0.3453, 97, 20.33, -188.14, 0.6547, 2, 99, 41.44, 282.5, 0.40519, 97, 19.59, -209.59, 0.59481, 2, 99, 40.38, 260.6, 0.46665, 97, 18.84, -231.03, 0.53335, 2, 99, 39.31, 238.69, 0.5287, 97, 18.07, -252.46, 0.4713, 2, 99, 38.24, 216.79, 0.59036, 97, 17.31, -273.89, 0.40964, 2, 99, 37.2, 194.88, 0.65063, 97, 16.56, -295.35, 0.34937, 2, 99, 36.2, 172.95, 0.7085, 97, 15.85, -316.83, 0.2915, 2, 99, 35.26, 151, 0.76294, 97, 15.18, -338.36, 0.23706, 2, 99, 34.39, 129.03, 0.81296, 97, 14.56, -359.95, 0.18704, 2, 99, 33.62, 107.02, 0.8576, 97, 14.01, -381.61, 0.1424, 2, 99, 32.96, 84.99, 0.89604, 97, 13.54, -403.36, 0.10396, 2, 99, 32.41, 62.91, 0.92765, 97, 13.15, -425.2, 0.07235, 2, 99, 31.99, 40.79, 0.95212, 97, 12.84, -447.13, 0.04788, 2, 99, 31.69, 18.63, 0.96941, 97, 12.63, -469.16, 0.03059, 2, 99, -0.76, 18.77, 0.99526, 97, -19.69, -468.81, 0.00474, 2, 99, -0.49, 40.95, 0.97983, 97, -19.5, -446.76, 0.02017, 2, 99, -0.06, 63.06, 0.95465, 97, -19.19, -424.83, 0.04535, 2, 99, 0.53, 85.13, 0.92077, 97, -18.77, -403.03, 0.07923, 2, 99, 1.25, 107.15, 0.87928, 97, -18.26, -381.32, 0.12072, 2, 99, 2.08, 129.13, 0.83126, 97, -17.67, -359.71, 0.16874, 2, 99, 3, 151.09, 0.77776, 97, -17, -338.16, 0.22224, 2, 99, 4, 173.01, 0.71985, 97, -16.29, -316.68, 0.28015, 2, 99, 5.06, 194.92, 0.65855, 97, -15.53, -295.24, 0.34145, 2, 99, 6.16, 216.81, 0.59491, 97, -14.75, -273.83, 0.40509, 2, 99, 7.29, 238.7, 0.52993, 97, -13.95, -252.44, 0.47007, 2, 99, 8.42, 260.59, 0.46461, 97, -13.14, -231.06, 0.53539, 2, 99, 9.54, 282.47, 0.39991, 97, -12.34, -209.67, 0.60009, 2, 99, 10.63, 304.37, 0.33681, 97, -11.56, -188.25, 0.66319, 2, 99, 11.67, 326.28, 0.27628, 97, -10.81, -166.8, 0.72372, 2, 99, 12.66, 348.21, 0.21926, 97, -10.11, -145.31, 0.78074, 2, 99, 13.57, 370.17, 0.16667, 97, -9.46, -123.75, 0.83333, 2, 99, 14.38, 392.16, 0.11952, 97, -8.88, -102.12, 0.88048, 2, 99, 15.09, 414.19, 0.07874, 97, -8.38, -80.41, 0.92126, 2, 99, 15.67, 436.25, 0.04529, 97, -7.96, -58.6, 0.95471, 2, 99, 16.1, 458.37, 0.02028, 97, -7.65, -36.67, 0.97972, 2, 99, 16.37, 480.54, 0.0047, 97, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 99, 31.51, -3.58, 0.97968, 97, 12.5, -491.28, 0.02032, 2, 99, -0.55, -3.56, 0.98313, 97, -19.54, -491.23, 0.01687, 2, 99, -32.49, -3.57, 0.97983, 97, -51.5, -491.28, 0.02017, 2, 99, -32.32, 18.63, 0.96967, 97, -51.37, -469.15, 0.03033, 2, 99, -32.02, 40.79, 0.95244, 97, -51.16, -447.12, 0.04756, 2, 99, -31.6, 62.91, 0.92796, 97, -50.86, -425.19, 0.07204, 2, 99, -31.05, 84.99, 0.8963, 97, -50.47, -403.35, 0.1037, 2, 99, -30.38, 107.03, 0.8578, 97, -49.99, -381.61, 0.1422, 2, 99, -29.61, 129.03, 0.81309, 97, -49.44, -359.95, 0.18691, 2, 99, -28.74, 151, 0.76302, 97, -48.82, -338.36, 0.23698, 2, 99, -27.8, 172.95, 0.70854, 97, -48.15, -316.83, 0.29146, 2, 99, -26.8, 194.88, 0.65065, 97, -47.44, -295.35, 0.34934, 2, 99, -25.76, 216.79, 0.59038, 97, -46.69, -273.89, 0.40962, 2, 99, -24.69, 238.69, 0.52871, 97, -45.93, -252.46, 0.47129, 2, 99, -23.62, 260.6, 0.46666, 97, -45.16, -231.03, 0.53334, 2, 99, -22.56, 282.5, 0.4052, 97, -44.41, -209.59, 0.5948, 2, 99, -21.52, 304.42, 0.34532, 97, -43.67, -188.14, 0.65468, 2, 99, -20.53, 326.35, 0.28798, 97, -42.96, -166.65, 0.71202, 2, 99, -19.6, 348.3, 0.23415, 97, -42.29, -145.11, 0.76585, 2, 99, -18.74, 370.28, 0.18476, 97, -41.68, -123.51, 0.81524, 2, 99, -17.98, 392.28, 0.14072, 97, -41.14, -101.84, 0.85928, 2, 99, -17.33, 414.33, 0.10281, 97, -40.67, -80.09, 0.89719, 2, 99, -16.79, 436.41, 0.07161, 97, -40.29, -58.24, 0.92839, 2, 99, -16.37, 458.53, 0.04744, 97, -39.99, -36.31, 0.95256, 2, 99, -16.07, 480.69, 0.03036, 97, -39.78, -14.28, 0.96964, 2, 99, -15.9, 502.89, 0.02023, 97, -39.65, 7.85, 0.97977, 2, 99, 16.16, 502.87, 0.01685, 97, -7.61, 7.8, 0.98315, 2, 99, 48.1, 502.89, 0.02016, 97, 24.35, 7.85, 0.97984, 2, 99, 47.93, 480.69, 0.03025, 97, 24.22, -14.28, 0.96975, 2, 99, 47.63, 458.53, 0.0473, 97, 24.01, -36.31, 0.9527, 2, 99, 47.22, 436.41, 0.07146, 97, 23.71, -58.25, 0.92854, 2, 99, 46.68, 414.33, 0.10268, 97, 23.33, -80.09, 0.89732, 2, 99, 46.02, 392.28, 0.14062, 97, 22.86, -101.84, 0.85938, 2, 99, 45.26, 370.28, 0.18469, 97, 22.32, -123.51, 0.81531, 2, 99, 44.4, 348.3, 0.2341, 97, 21.71, -145.11, 0.7659, 2, 99, 43.47, 326.35, 0.28795, 97, 21.04, -166.65, 0.71205, 2, 99, 42.48, 304.42, 0.3453, 97, 20.33, -188.14, 0.6547, 2, 99, 41.44, 282.5, 0.40519, 97, 19.59, -209.59, 0.59481, 2, 99, 40.38, 260.6, 0.46665, 97, 18.84, -231.03, 0.53335, 2, 99, 39.31, 238.69, 0.5287, 97, 18.07, -252.46, 0.4713, 2, 99, 38.24, 216.79, 0.59036, 97, 17.31, -273.89, 0.40964, 2, 99, 37.2, 194.88, 0.65063, 97, 16.56, -295.35, 0.34937, 2, 99, 36.2, 172.95, 0.7085, 97, 15.85, -316.83, 0.2915, 2, 99, 35.26, 151, 0.76294, 97, 15.18, -338.36, 0.23706, 2, 99, 34.39, 129.03, 0.81296, 97, 14.56, -359.95, 0.18704, 2, 99, 33.62, 107.02, 0.8576, 97, 14.01, -381.61, 0.1424, 2, 99, 32.96, 84.99, 0.89604, 97, 13.54, -403.36, 0.10396, 2, 99, 32.41, 62.91, 0.92765, 97, 13.15, -425.2, 0.07235, 2, 99, 31.99, 40.79, 0.95212, 97, 12.84, -447.13, 0.04788, 2, 99, 31.69, 18.63, 0.96941, 97, 12.63, -469.16, 0.03059, 2, 99, -0.76, 18.77, 0.99526, 97, -19.69, -468.81, 0.00474, 2, 99, -0.49, 40.95, 0.97983, 97, -19.5, -446.76, 0.02017, 2, 99, -0.06, 63.06, 0.95465, 97, -19.19, -424.83, 0.04535, 2, 99, 0.53, 85.13, 0.92077, 97, -18.77, -403.03, 0.07923, 2, 99, 1.25, 107.15, 0.87928, 97, -18.26, -381.32, 0.12072, 2, 99, 2.08, 129.13, 0.83126, 97, -17.67, -359.71, 0.16874, 2, 99, 3, 151.09, 0.77776, 97, -17, -338.16, 0.22224, 2, 99, 4, 173.01, 0.71985, 97, -16.29, -316.68, 0.28015, 2, 99, 5.06, 194.92, 0.65855, 97, -15.53, -295.24, 0.34145, 2, 99, 6.16, 216.81, 0.59491, 97, -14.75, -273.83, 0.40509, 2, 99, 7.29, 238.7, 0.52993, 97, -13.95, -252.44, 0.47007, 2, 99, 8.42, 260.59, 0.46461, 97, -13.14, -231.06, 0.53539, 2, 99, 9.54, 282.47, 0.39991, 97, -12.34, -209.67, 0.60009, 2, 99, 10.63, 304.37, 0.33681, 97, -11.56, -188.25, 0.66319, 2, 99, 11.67, 326.28, 0.27628, 97, -10.81, -166.8, 0.72372, 2, 99, 12.66, 348.21, 0.21926, 97, -10.11, -145.31, 0.78074, 2, 99, 13.57, 370.17, 0.16667, 97, -9.46, -123.75, 0.83333, 2, 99, 14.38, 392.16, 0.11952, 97, -8.88, -102.12, 0.88048, 2, 99, 15.09, 414.19, 0.07874, 97, -8.38, -80.41, 0.92126, 2, 99, 15.67, 436.25, 0.04529, 97, -7.96, -58.6, 0.95471, 2, 99, 16.1, 458.37, 0.02028, 97, -7.65, -36.67, 0.97972, 2, 99, 16.37, 480.54, 0.0047, 97, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}, "lighting_04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.95652, 0, 0.91304, 0, 0.86957, 0, 0.82609, 0, 0.78261, 0, 0.73913, 0, 0.69565, 0, 0.65217, 0, 0.6087, 0, 0.56522, 0, 0.52174, 0, 0.47826, 0, 0.43478, 0, 0.3913, 0, 0.34783, 0, 0.30435, 0, 0.26087, 0, 0.21739, 0, 0.17391, 0, 0.13043, 0, 0.08696, 0, 0.04348, 0, 0, 0.5, 0, 1, 0, 1, 0.04348, 1, 0.08696, 1, 0.13043, 1, 0.17391, 1, 0.21739, 1, 0.26087, 1, 0.30435, 1, 0.34783, 1, 0.3913, 1, 0.43478, 1, 0.47826, 1, 0.52174, 1, 0.56522, 1, 0.6087, 1, 0.65217, 1, 0.69565, 1, 0.73913, 1, 0.78261, 1, 0.82609, 1, 0.86957, 1, 0.91304, 1, 0.95652, 0.5, 0.95652, 0.5, 0.91304, 0.5, 0.86957, 0.5, 0.82609, 0.5, 0.78261, 0.5, 0.73913, 0.5, 0.69565, 0.5, 0.65217, 0.5, 0.6087, 0.5, 0.56522, 0.5, 0.52174, 0.5, 0.47826, 0.5, 0.43478, 0.5, 0.3913, 0.5, 0.34783, 0.5, 0.30435, 0.5, 0.26087, 0.5, 0.21739, 0.5, 0.17391, 0.5, 0.13043, 0.5, 0.08696, 0.5, 0.04348], "triangles": [24, 25, 26, 71, 24, 26, 23, 24, 71, 70, 23, 71, 22, 23, 70, 71, 26, 27, 13, 14, 61, 60, 61, 38, 15, 16, 63, 62, 15, 63, 61, 62, 37, 16, 17, 64, 63, 16, 64, 36, 63, 35, 19, 20, 67, 65, 18, 66, 64, 65, 34, 35, 64, 34, 69, 22, 70, 68, 21, 69, 67, 20, 68, 28, 71, 27, 29, 70, 28, 30, 69, 29, 31, 68, 30, 70, 71, 28, 69, 70, 29, 68, 69, 30, 67, 68, 31, 32, 67, 31, 66, 67, 32, 33, 66, 32, 20, 21, 68, 66, 19, 67, 65, 66, 33, 34, 65, 33, 21, 22, 69, 18, 19, 66, 17, 18, 65, 64, 17, 65, 63, 64, 35, 62, 63, 36, 37, 62, 36, 14, 15, 62, 61, 14, 62, 38, 61, 37, 2, 3, 50, 1, 2, 50, 8, 9, 56, 0, 1, 49, 50, 3, 51, 1, 50, 49, 54, 7, 55, 51, 4, 52, 50, 51, 48, 49, 50, 48, 55, 56, 43, 44, 55, 43, 45, 54, 44, 10, 11, 58, 56, 57, 42, 57, 10, 58, 11, 12, 59, 58, 11, 59, 41, 58, 40, 12, 13, 60, 59, 12, 60, 40, 59, 39, 60, 13, 61, 39, 60, 38, 59, 60, 39, 58, 59, 40, 57, 58, 41, 42, 57, 41, 43, 56, 42, 9, 10, 57, 56, 9, 57, 46, 53, 45, 47, 52, 46, 54, 55, 44, 53, 54, 45, 52, 53, 46, 51, 52, 47, 48, 51, 47, 53, 6, 54, 52, 5, 53, 55, 8, 56, 6, 7, 54, 5, 6, 53, 4, 5, 52, 7, 8, 55, 3, 4, 51], "vertices": [2, 99, 31.51, -3.58, 0.97968, 97, 12.5, -491.28, 0.02032, 2, 99, -0.55, -3.56, 0.98313, 97, -19.54, -491.23, 0.01687, 2, 99, -32.49, -3.57, 0.97983, 97, -51.5, -491.28, 0.02017, 2, 99, -32.32, 18.63, 0.96967, 97, -51.37, -469.15, 0.03033, 2, 99, -32.02, 40.79, 0.95244, 97, -51.16, -447.12, 0.04756, 2, 99, -31.6, 62.91, 0.92796, 97, -50.86, -425.19, 0.07204, 2, 99, -31.05, 84.99, 0.8963, 97, -50.47, -403.35, 0.1037, 2, 99, -30.38, 107.03, 0.8578, 97, -49.99, -381.61, 0.1422, 2, 99, -29.61, 129.03, 0.81309, 97, -49.44, -359.95, 0.18691, 2, 99, -28.74, 151, 0.76302, 97, -48.82, -338.36, 0.23698, 2, 99, -27.8, 172.95, 0.70854, 97, -48.15, -316.83, 0.29146, 2, 99, -26.8, 194.88, 0.65065, 97, -47.44, -295.35, 0.34934, 2, 99, -25.76, 216.79, 0.59038, 97, -46.69, -273.89, 0.40962, 2, 99, -24.69, 238.69, 0.52871, 97, -45.93, -252.46, 0.47129, 2, 99, -23.62, 260.6, 0.46666, 97, -45.16, -231.03, 0.53334, 2, 99, -22.56, 282.5, 0.4052, 97, -44.41, -209.59, 0.5948, 2, 99, -21.52, 304.42, 0.34532, 97, -43.67, -188.14, 0.65468, 2, 99, -20.53, 326.35, 0.28798, 97, -42.96, -166.65, 0.71202, 2, 99, -19.6, 348.3, 0.23415, 97, -42.29, -145.11, 0.76585, 2, 99, -18.74, 370.28, 0.18476, 97, -41.68, -123.51, 0.81524, 2, 99, -17.98, 392.28, 0.14072, 97, -41.14, -101.84, 0.85928, 2, 99, -17.33, 414.33, 0.10281, 97, -40.67, -80.09, 0.89719, 2, 99, -16.79, 436.41, 0.07161, 97, -40.29, -58.24, 0.92839, 2, 99, -16.37, 458.53, 0.04744, 97, -39.99, -36.31, 0.95256, 2, 99, -16.07, 480.69, 0.03036, 97, -39.78, -14.28, 0.96964, 2, 99, -15.9, 502.89, 0.02023, 97, -39.65, 7.85, 0.97977, 2, 99, 16.16, 502.87, 0.01685, 97, -7.61, 7.8, 0.98315, 2, 99, 48.1, 502.89, 0.02016, 97, 24.35, 7.85, 0.97984, 2, 99, 47.93, 480.69, 0.03025, 97, 24.22, -14.28, 0.96975, 2, 99, 47.63, 458.53, 0.0473, 97, 24.01, -36.31, 0.9527, 2, 99, 47.22, 436.41, 0.07146, 97, 23.71, -58.25, 0.92854, 2, 99, 46.68, 414.33, 0.10268, 97, 23.33, -80.09, 0.89732, 2, 99, 46.02, 392.28, 0.14062, 97, 22.86, -101.84, 0.85938, 2, 99, 45.26, 370.28, 0.18469, 97, 22.32, -123.51, 0.81531, 2, 99, 44.4, 348.3, 0.2341, 97, 21.71, -145.11, 0.7659, 2, 99, 43.47, 326.35, 0.28795, 97, 21.04, -166.65, 0.71205, 2, 99, 42.48, 304.42, 0.3453, 97, 20.33, -188.14, 0.6547, 2, 99, 41.44, 282.5, 0.40519, 97, 19.59, -209.59, 0.59481, 2, 99, 40.38, 260.6, 0.46665, 97, 18.84, -231.03, 0.53335, 2, 99, 39.31, 238.69, 0.5287, 97, 18.07, -252.46, 0.4713, 2, 99, 38.24, 216.79, 0.59036, 97, 17.31, -273.89, 0.40964, 2, 99, 37.2, 194.88, 0.65063, 97, 16.56, -295.35, 0.34937, 2, 99, 36.2, 172.95, 0.7085, 97, 15.85, -316.83, 0.2915, 2, 99, 35.26, 151, 0.76294, 97, 15.18, -338.36, 0.23706, 2, 99, 34.39, 129.03, 0.81296, 97, 14.56, -359.95, 0.18704, 2, 99, 33.62, 107.02, 0.8576, 97, 14.01, -381.61, 0.1424, 2, 99, 32.96, 84.99, 0.89604, 97, 13.54, -403.36, 0.10396, 2, 99, 32.41, 62.91, 0.92765, 97, 13.15, -425.2, 0.07235, 2, 99, 31.99, 40.79, 0.95212, 97, 12.84, -447.13, 0.04788, 2, 99, 31.69, 18.63, 0.96941, 97, 12.63, -469.16, 0.03059, 2, 99, -0.76, 18.77, 0.99526, 97, -19.69, -468.81, 0.00474, 2, 99, -0.49, 40.95, 0.97983, 97, -19.5, -446.76, 0.02017, 2, 99, -0.06, 63.06, 0.95465, 97, -19.19, -424.83, 0.04535, 2, 99, 0.53, 85.13, 0.92077, 97, -18.77, -403.03, 0.07923, 2, 99, 1.25, 107.15, 0.87928, 97, -18.26, -381.32, 0.12072, 2, 99, 2.08, 129.13, 0.83126, 97, -17.67, -359.71, 0.16874, 2, 99, 3, 151.09, 0.77776, 97, -17, -338.16, 0.22224, 2, 99, 4, 173.01, 0.71985, 97, -16.29, -316.68, 0.28015, 2, 99, 5.06, 194.92, 0.65855, 97, -15.53, -295.24, 0.34145, 2, 99, 6.16, 216.81, 0.59491, 97, -14.75, -273.83, 0.40509, 2, 99, 7.29, 238.7, 0.52993, 97, -13.95, -252.44, 0.47007, 2, 99, 8.42, 260.59, 0.46461, 97, -13.14, -231.06, 0.53539, 2, 99, 9.54, 282.47, 0.39991, 97, -12.34, -209.67, 0.60009, 2, 99, 10.63, 304.37, 0.33681, 97, -11.56, -188.25, 0.66319, 2, 99, 11.67, 326.28, 0.27628, 97, -10.81, -166.8, 0.72372, 2, 99, 12.66, 348.21, 0.21926, 97, -10.11, -145.31, 0.78074, 2, 99, 13.57, 370.17, 0.16667, 97, -9.46, -123.75, 0.83333, 2, 99, 14.38, 392.16, 0.11952, 97, -8.88, -102.12, 0.88048, 2, 99, 15.09, 414.19, 0.07874, 97, -8.38, -80.41, 0.92126, 2, 99, 15.67, 436.25, 0.04529, 97, -7.96, -58.6, 0.95471, 2, 99, 16.1, 458.37, 0.02028, 97, -7.65, -36.67, 0.97972, 2, 99, 16.37, 480.54, 0.0047, 97, -7.46, -14.62, 0.9953], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 64, "height": 512}}, "obj_01": {"obj_01": {"type": "mesh", "uvs": [0.99999, 0.31165, 0.88589, 0.50759, 0.77125, 0.61002, 0.61832, 0.62401, 0.49709, 0.86652, 0.43976, 0.86885, 0.39977, 0.96028, 0.34895, 0.99582, 0.27308, 0.716, 0.17418, 0.5129, 0.07768, 0.43973, 1e-05, 0.4021, 0.00165, 0.27402, 0.0767, 0.15261, 0.17412, 0.18205, 0.24295, 0.10133, 0.44834, 0.11234, 0.6099, 0.10322, 0.72668, 0.0813, 0.78587, 0.05389, 0.88622, 0, 0.95711, 0.12543, 0.25337, 0.31066, 0.31028, 0.44864, 0.37213, 0.59916, 0.28913, 0.26829, 0.34108, 0.38745, 0.41778, 0.55261, 0.47221, 0.75121], "triangles": [2, 18, 19, 25, 15, 16, 22, 15, 25, 14, 15, 22, 26, 25, 16, 10, 13, 14, 12, 13, 10, 11, 12, 10, 22, 25, 23, 9, 14, 22, 10, 14, 9, 3, 17, 18, 3, 16, 17, 23, 25, 26, 27, 26, 16, 24, 26, 27, 23, 26, 24, 23, 9, 22, 8, 23, 24, 8, 9, 23, 2, 19, 1, 3, 18, 2, 16, 3, 27, 28, 27, 3, 4, 28, 3, 28, 24, 27, 5, 28, 4, 5, 24, 28, 6, 24, 5, 7, 8, 24, 6, 7, 24, 1, 19, 20, 1, 20, 21, 1, 21, 0], "vertices": [1, 46, 1.98, -17.2, 1, 2, 46, -10.02, -8.16, 0.7486, 52, -4.59, 15.68, 0.2514, 2, 46, -18.23, 2.23, 0.14626, 52, 8.31, 12.68, 0.85374, 1, 52, 22.69, 4.23, 1, 1, 52, 39.44, 5.92, 1, 1, 52, 44.77, 2.64, 1, 1, 52, 50.55, 3.62, 1, 2, 52, 56.05, 1.93, 0.99749, 53, 48.49, 22.66, 0.00251, 2, 52, 56.55, -12.66, 0.25905, 53, 53.47, 8.94, 0.74095, 2, 53, 61.7, -2.23, 0.56005, 51, 59.18, 9.91, 0.43995, 2, 53, 71.09, -7.92, 0.01691, 51, 69.21, 5.44, 0.98309, 1, 51, 77.4, 2.75, 1, 1, 51, 76.51, -2.69, 1, 1, 51, 67.73, -6.82, 1, 1, 51, 57.36, -4.2, 1, 1, 51, 49.48, -6.69, 1, 1, 51, 27.33, -3.35, 1, 1, 51, 9.82, -1.49, 1, 1, 51, -2.93, -0.79, 1, 1, 51, -9.48, -1.14, 1, 4, 46, 10.64, -1.13, 0.98963, 52, -16.35, -2.7, 0.00114, 53, -18.96, -4.05, 0.00696, 51, -20.62, -2.04, 0.00226, 1, 46, 8.04, -10.19, 1, 2, 53, 51.16, -8.47, 0.15642, 51, 49.5, 2.39, 0.84358, 2, 53, 46.65, -1.17, 0.93978, 51, 44.11, 9.06, 0.06022, 2, 52, 44.75, -11.1, 0.3045, 53, 41.75, 6.79, 0.6955, 2, 53, 46.93, -9.26, 0.06142, 51, 45.4, 1.08, 0.93858, 2, 53, 42.74, -2.87, 0.7723, 51, 40.44, 6.88, 0.2277, 2, 52, 39.48, -10.11, 0.30445, 53, 36.43, 6.1, 0.69555, 1, 52, 39.06, 0.28, 1], "hull": 22, "edges": [24, 26, 40, 42, 0, 42, 12, 14, 22, 24, 26, 28, 28, 30, 0, 2, 2, 4, 4, 6, 6, 8, 22, 20, 20, 18, 18, 16, 16, 14, 28, 44, 44, 46, 46, 48, 48, 10, 10, 12, 10, 8, 30, 32, 32, 34, 38, 40, 34, 36, 36, 38], "width": 109, "height": 43}}, "obj_02": {"obj_02": {"type": "mesh", "uvs": [1, 0.27773, 0.92503, 0.48711, 0.87057, 0.60904, 0.82078, 0.74862, 0.70921, 1, 0.6193, 1, 0.4885, 0.84335, 0.38976, 0.68578, 0.30785, 0.62385, 0.21877, 0.50928, 0.11865, 0.40629, 0.02258, 0.29401, 0, 0.21665, 0.04354, 0.12137, 0.17435, 0.0571, 0.30017, 0.07345, 0.4444, 0.09107, 0.54704, 0.10362, 0.68468, 0.09699, 0.83244, 0.05937, 0.99999, 0.17484, 0.63137, 0.57264, 0.75854, 0.3361, 0.39924, 0.26932, 0.32016, 0.39054, 0.09198, 0.17186, 0.15813, 0.11121, 0.2968, 0.12429, 0.53513, 0.15337, 0.69226, 0.14933, 0.04318, 0.22256, 0.4254, 0.14331, 0.2338, 0.23822, 0.43154, 0.46005, 0.52411, 0.33388], "triangles": [26, 13, 14, 27, 14, 15, 31, 27, 15, 26, 14, 27, 16, 31, 15, 29, 18, 19, 28, 16, 17, 31, 16, 28, 25, 13, 26, 25, 30, 13, 32, 26, 27, 25, 26, 32, 23, 27, 31, 17, 18, 29, 28, 17, 29, 29, 34, 28, 22, 19, 20, 22, 20, 0, 1, 22, 0, 1, 2, 22, 3, 21, 2, 6, 21, 3, 5, 6, 3, 4, 5, 3, 22, 29, 19, 34, 29, 22, 2, 21, 22, 21, 34, 22, 33, 34, 21, 7, 33, 21, 6, 7, 21, 9, 10, 24, 8, 9, 24, 8, 24, 33, 7, 8, 33, 30, 12, 13, 32, 27, 23, 11, 12, 30, 28, 23, 31, 34, 23, 28, 24, 32, 23, 10, 25, 32, 10, 32, 24, 30, 25, 10, 11, 30, 10, 33, 23, 34, 24, 23, 33], "vertices": [1, 47, 4.42, -17.24, 1, 1, 47, -16.01, -15.75, 1, 1, 47, -28.28, -13.79, 1, 1, 47, -41.89, -12.82, 1, 1, 47, -67.16, -8.85, 1, 1, 47, -70.27, 0.25, 1, 1, 47, -61.47, 18.06, 1, 2, 49, 46.02, 18.56, 0.41067, 48, 50.39, -10.06, 0.58933, 2, 49, 51.5, 9.74, 0.96432, 48, 51.42, -20.4, 0.03568, 2, 50, 45.76, 25.14, 0.14772, 49, 55.62, -3.69, 0.85228, 2, 50, 56.59, 16.01, 0.74387, 49, 61.25, -16.68, 0.25613, 3, 50, 67.01, 6.05, 0.98493, 49, 66.13, -30.25, 0.01451, 51, 82.4, 13.94, 0.00055, 3, 50, 69.52, -0.88, 0.82524, 49, 65.29, -37.57, 0.00027, 51, 83.9, 6.72, 0.17449, 2, 50, 64.98, -9.52, 0.14444, 51, 78.19, -1.19, 0.85556, 1, 51, 63.56, -5.13, 1, 1, 51, 50.4, -1.95, 1, 1, 51, 35.3, 1.6, 1, 1, 51, 24.55, 4.12, 1, 1, 51, 9.87, 5.42, 1, 1, 47, 17.21, 6.08, 1, 1, 47, 13.18, -14.24, 1, 2, 48, 26.42, 3.98, 0.99841, 47, -33.47, 11.48, 0.00159, 2, 48, 1.27, 1.54, 0.97195, 47, -8.92, 5.5, 0.02805, 2, 50, 26.74, 3.28, 0.99081, 51, 42.14, 16.89, 0.00919, 2, 50, 35.05, 14.3, 0.25274, 49, 41.21, -8.61, 0.74726, 3, 50, 59.73, -5.05, 0.32091, 49, 54.67, -36.93, 0.00406, 51, 73.63, 3.98, 0.67503, 3, 50, 52.73, -10.6, 0.16661, 49, 45.93, -38.78, 0.09378, 51, 65.91, -0.53, 0.73961, 3, 50, 37.88, -9.62, 0.04924, 49, 33.07, -31.29, 0.12218, 51, 51.34, 2.54, 0.82858, 2, 50, 12.34, -7.35, 0.28773, 51, 26.39, 8.4, 0.71227, 4, 50, -4.46, -7.94, 0.01538, 48, -7.57, -14.42, 0.20357, 47, 4.69, 17.65, 0.00587, 51, 9.67, 10.19, 0.77518, 3, 50, 64.89, -0.41, 0.54846, 49, 61.36, -35.09, 0.00603, 51, 79.39, 7.84, 0.44551, 3, 50, 24.09, -8.1, 0.33015, 49, 21.41, -23.78, 0.02734, 51, 37.92, 6, 0.64251, 3, 50, 44.48, 0.72, 0.3066, 49, 43.59, -24.97, 0.19814, 51, 59.34, 11.85, 0.49525, 4, 50, 23.05, 20.4, 0.15894, 49, 33.18, 2.19, 0.47693, 48, 31.65, -19.09, 0.36357, 47, -30.8, 34.99, 0.00056, 5, 50, 13.3, 8.91, 0.17497, 49, 19.33, -3.75, 0.23093, 48, 16.6, -18.32, 0.3463, 47, -16.85, 29.3, 0.00519, 51, 29.64, 24.36, 0.24262], "hull": 21, "edges": [22, 20, 20, 18, 16, 14, 14, 12, 12, 10, 0, 40, 8, 10, 18, 16, 26, 28, 22, 24, 24, 26, 28, 30, 36, 38, 38, 40, 34, 36, 4, 6, 6, 8, 0, 2, 2, 4, 30, 32, 32, 34], "width": 107, "height": 90}}, "shadow": {"shadow": {"x": -37.96, "y": -7.43, "width": 256, "height": 254}}, "skirt": {"skirt": {"type": "mesh", "uvs": [0.10707, 0.02509, 0.18137, 0.03267, 0.23041, 0.03768, 0.30535, 0.04533, 0.33646, 0.07404, 0.36964, 0.1094, 0.38649, 0.14962, 0.42882, 0.16958, 0.46918, 0.19778, 0.50032, 0.24165, 0.53222, 0.31267, 0.58216, 0.36925, 0.62598, 0.42565, 0.63944, 0.4716, 0.63944, 0.50607, 0.68374, 0.53792, 0.72372, 0.5938, 0.73179, 0.64341, 0.77647, 0.67679, 0.84216, 0.76295, 0.89747, 0.82799, 0.95398, 0.89442, 1, 0.94853, 0.99426, 0.97195, 0.90176, 1, 0.79225, 1, 0.70975, 0.97506, 0.63994, 0.92592, 0.57457, 0.86385, 0.55566, 0.87003, 0.48356, 0.87462, 0.405, 0.86644, 0.31395, 0.80621, 0.26114, 0.7152, 0.24645, 0.56259, 0.2562, 0.41751, 0.24034, 0.34645, 0.20705, 0.29297, 0.17291, 0.26663, 0.17291, 0.20062, 0.13203, 0.12518, 0.05953, 0.06127, 0, 0.03507, 0.01234, 0.00599, 0.07055, 0.00419], "triangles": [27, 17, 18, 17, 15, 16, 15, 17, 14, 17, 28, 14, 13, 14, 11, 13, 11, 12, 34, 11, 14, 11, 34, 10, 10, 35, 9, 8, 9, 7, 7, 9, 35, 6, 7, 36, 6, 4, 5, 4, 6, 37, 3, 39, 2, 4, 39, 3, 39, 1, 2, 39, 40, 1, 41, 0, 40, 40, 0, 1, 42, 43, 41, 41, 44, 0, 41, 43, 44, 7, 35, 36, 6, 36, 37, 39, 4, 37, 37, 38, 39, 34, 14, 31, 34, 31, 32, 32, 33, 34, 35, 10, 34, 27, 28, 17, 29, 30, 14, 28, 29, 14, 31, 14, 30, 25, 26, 19, 26, 27, 18, 24, 21, 23, 21, 24, 20, 24, 25, 20, 20, 25, 19, 19, 26, 18, 23, 21, 22], "vertices": [1, 57, 22.9, -1.08, 1, 3, 56, 27.83, -22.71, 0.00035, 57, 12.17, -0.81, 0.62667, 2, -47.98, 35.39, 0.37298, 3, 56, 20.9, -21.25, 0.00058, 57, 5.09, -0.62, 0.38029, 2, -40.92, 34.86, 0.61913, 3, 56, 10.31, -19.02, 0.00093, 57, -5.73, -0.35, 0.00375, 2, -30.12, 34.05, 0.99532, 1, 2, -25.64, 31, 1, 1, 2, -20.87, 27.26, 1, 1, 2, -18.44, 22.99, 1, 1, 2, -12.34, 20.88, 1, 1, 2, -6.53, 17.89, 1, 1, 2, -2.05, 13.24, 1, 1, 2, 2.55, 5.71, 1, 1, 2, 9.74, -0.29, 1, 1, 2, 16.05, -6.27, 1, 1, 2, 17.98, -11.14, 1, 1, 2, 17.98, -14.79, 1, 1, 2, 24.36, -18.17, 1, 1, 2, 30.12, -24.09, 1, 1, 2, 31.28, -29.35, 1, 1, 2, 37.72, -32.89, 1, 1, 60, 10.87, 1.99, 1, 2, 60, 21.4, 2.32, 1, 59, 35.77, 22.33, 0, 2, 60, 32.16, 2.66, 1, 59, 45.02, 27.84, 0, 2, 60, 40.92, 2.94, 1, 59, 52.54, 32.32, 0, 2, 60, 41.99, 0.55, 0.99966, 59, 54.64, 30.75, 0.00034, 2, 60, 34.22, -10.66, 0.83688, 59, 53.27, 17.17, 0.16312, 2, 60, 22.63, -21.36, 0.10937, 59, 48.3, 2.21, 0.89063, 2, 59, 42.05, -8.24, 0.93925, 58, 53.52, 20.23, 0.06075, 2, 59, 33.95, -16.14, 0.5934, 58, 49.27, 9.74, 0.4066, 3, 59, 24.74, -23, 0.03412, 58, 43.59, -0.24, 0.95952, 55, 25.4, 44.31, 0.00635, 3, 59, 24.5, -25.79, 0.00082, 58, 44.49, -2.9, 0.96806, 55, 27.8, 42.86, 0.03112, 3, 59, 21.7, -35.81, 0.00043, 58, 45.94, -13.19, 0.80837, 55, 35.51, 35.89, 0.19119, 2, 58, 46.13, -24.53, 0.62814, 55, 42.93, 27.31, 0.37186, 2, 58, 40.99, -38.18, 0.30896, 55, 47.74, 13.54, 0.69104, 2, 58, 32.09, -46.65, 0.04363, 55, 46.35, 1.34, 0.95637, 2, 55, 36.46, -11.64, 0.99548, 56, 25.96, 34.2, 0.00452, 2, 55, 24.63, -21.57, 0.53949, 56, 22.54, 19.15, 0.46051, 2, 55, 20.95, -28.52, 0.20496, 56, 23.81, 11.38, 0.79504, 2, 55, 20.36, -35.92, 0.02546, 56, 27.81, 5.13, 0.97454, 3, 55, 21.88, -41.37, 0.00012, 56, 32.31, 1.71, 0.99753, 57, 12.16, 24.02, 0.00235, 2, 56, 31.39, -5.23, 0.74503, 57, 12.51, 17.04, 0.25497, 2, 56, 36.17, -13.93, 0.14726, 57, 18.78, 9.34, 0.85274, 1, 57, 29.54, 3.09, 1, 1, 57, 38.24, 0.74, 1, 1, 57, 36.62, -2.43, 1, 1, 57, 28.26, -3.03, 1], "hull": 45, "edges": [86, 88, 88, 0, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 46, 44, 46, 48, 56, 54, 54, 52, 48, 50, 52, 50, 62, 64, 64, 66, 66, 68, 68, 70, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 82, 84, 70, 72, 72, 74, 58, 56, 0, 2, 2, 4, 4, 6, 38, 40, 40, 42, 42, 44, 58, 60, 60, 62], "width": 144, "height": 106}}, "skirt_02": {"skirt_02": {"type": "mesh", "uvs": [0.03595, 0.25084, 0.06109, 0.25083, 0.09511, 0.26661, 0.13422, 0.26796, 0.19105, 0.25747, 0.22468, 0.25949, 0.27918, 0.28074, 0.421, 0.38329, 0.55979, 0.49809, 0.56259, 0.54006, 0.52456, 0.593, 0.5256, 0.63406, 0.54965, 0.69606, 0.59792, 0.76479, 0.65289, 0.80222, 0.71741, 0.88875, 0.62241, 0.94034, 0.54253, 0.95355, 0.4653, 0.95799, 0.38797, 0.96156, 0.30061, 0.93208, 0.24065, 0.87845, 0.18914, 0.85761, 0.13674, 0.81365, 0.10342, 0.73577, 0.07765, 0.62163, 0.05963, 0.51274, 0.01729, 0.46983, 0.00506, 0.41356, 0.00087, 0.36273, 1e-05, 0.28225, 0.01126, 0.25861, 0.22741, 0.3343, 0.29352, 0.39679, 0.37848, 0.4717, 0.48612, 0.5532], "triangles": [18, 19, 12, 13, 17, 18, 17, 13, 16, 13, 18, 12, 19, 11, 12, 16, 14, 15, 16, 13, 14, 11, 35, 10, 35, 11, 21, 21, 34, 35, 10, 35, 9, 35, 8, 9, 8, 35, 7, 35, 34, 7, 19, 20, 11, 11, 20, 21, 34, 21, 24, 34, 24, 33, 33, 24, 32, 22, 24, 21, 22, 23, 24, 4, 24, 25, 4, 25, 26, 34, 33, 7, 26, 27, 2, 1, 27, 28, 0, 28, 29, 32, 6, 33, 33, 6, 7, 32, 5, 6, 5, 32, 4, 4, 32, 24, 26, 3, 4, 26, 2, 3, 2, 27, 1, 1, 28, 0, 30, 31, 0, 0, 29, 30], "vertices": [2, 57, 31.93, -2.66, 1, 61, 37.69, -27.74, 0, 1, 57, 26.7, -2.92, 1, 1, 57, 19.51, -0.72, 1, 1, 57, 11.37, -0.9, 1, 1, 57, -0.35, -3.18, 1, 2, 57, -7.35, -3.2, 0.74143, 61, -0.25, -17.56, 0.25857, 4, 57, -18.84, -0.32, 0.23428, 61, -10.52, -11.66, 0.6951, 62, -16.49, -26.62, 0.06568, 58, -39.74, -33.8, 0.00494, 5, 61, -35.53, 11.15, 0.07926, 62, -25.69, 5.96, 0.41989, 63, -30.67, 5.23, 0.01666, 58, -25.94, -2.88, 0.48411, 60, -54.48, 21.66, 9e-05, 3, 62, -33.04, 39.51, 0.00212, 58, -10.1, 27.59, 0.91395, 60, -20.65, 27.56, 0.08393, 2, 58, -3.39, 28.8, 0.89977, 60, -15.61, 22.96, 0.10023, 2, 58, 5.89, 21.72, 0.78386, 60, -15.61, 11.29, 0.21614, 2, 58, 12.49, 22.55, 0.53701, 60, -10.94, 6.55, 0.46299, 2, 58, 22.02, 28.47, 0.02706, 60, -0.46, 2.56, 0.97294, 1, 60, 14.47, 1.19, 1, 1, 60, 26.99, 4.48, 1, 1, 60, 46.36, 3.28, 1, 2, 58, 60.02, 47.21, 0.07511, 60, 37.5, -16.26, 0.92489, 3, 63, 55.94, 46.14, 0.01753, 58, 63.7, 30.86, 0.30789, 60, 26.74, -29.1, 0.67458, 3, 63, 59.43, 30.44, 0.16743, 58, 65.9, 14.94, 0.50987, 60, 15.42, -40.52, 0.3227, 3, 63, 62.79, 14.7, 0.60604, 58, 67.97, -1.03, 0.32131, 60, 3.99, -51.85, 0.07265, 2, 62, 54.76, 51.33, 0.01018, 63, 61.23, -4.03, 0.98982, 2, 62, 57.48, 36.37, 0.23808, 63, 54.84, -17.81, 0.76192, 2, 62, 62.69, 26.43, 0.52568, 63, 53.37, -28.95, 0.47432, 2, 62, 65.4, 13.69, 0.80119, 63, 48.25, -40.92, 0.19881, 2, 62, 61.41, -0.14, 0.99842, 63, 37.02, -49.93, 0.00158, 2, 61, 42.73, 32.75, 0.19626, 62, 52.17, -17.03, 0.80374, 2, 61, 42.42, 14.71, 0.75366, 62, 42.39, -32.18, 0.24634, 2, 61, 49.44, 5.96, 0.97561, 62, 43.73, -43.32, 0.02439, 2, 57, 37.04, 23.98, 0.07541, 61, 49.87, -3.49, 0.92459, 2, 57, 38.32, 15.8, 0.4012, 61, 48.87, -11.71, 0.5988, 2, 57, 39.14, 2.79, 0.94082, 61, 46.12, -24.46, 0.05918, 2, 57, 36.99, -1.15, 0.99547, 61, 42.98, -27.66, 0.00453, 2, 57, -8.52, 8.88, 0.23227, 61, 1.92, -5.62, 0.76773, 3, 61, -9.2, 7.33, 0.50748, 62, -5.35, -11.19, 0.4766, 58, -21.3, -29.08, 0.01592, 4, 61, -23.7, 23.12, 0.01837, 62, -9.32, 9.88, 0.4471, 63, -15.03, -1, 0.19959, 58, -10.86, -10.36, 0.33494, 3, 62, -15.88, 35.03, 0.00225, 58, 0.21, 13.16, 0.90568, 60, -25.86, 10.61, 0.09207], "hull": 32, "edges": [58, 60, 54, 52, 52, 50, 50, 48, 48, 46, 42, 40, 40, 38, 38, 36, 34, 32, 34, 36, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 10, 8, 60, 62, 2, 4, 8, 6, 6, 4, 12, 10, 46, 44, 44, 42, 12, 14, 14, 16, 2, 0, 0, 62, 54, 56, 56, 58], "width": 208, "height": 162}}}}], "animations": {"character_01_bule": {"bones": {"bone": {"translate": [{"curve": [0.233, 0, 0.467, -11.25, 0.233, 0, 0.467, -3.44]}, {"time": 0.7, "x": -11.25, "y": -3.44, "curve": [1.022, -11.25, 1.344, 0, 1.022, -3.44, 1.344, 0]}, {"time": 1.6667}]}, "bone20": {"rotate": [{"value": -11.15, "curve": [0.122, -11.15, 0.244, 0]}, {"time": 0.3667, "curve": [0.522, 0, 0.678, -12.14]}, {"time": 0.8333, "value": -12.14, "curve": [0.967, -12.14, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -11.15]}, {"time": 1.6667, "value": -11.15}]}, "bone21": {"rotate": [{"value": -7.19, "curve": [0.068, -5.05, 0.178, 0]}, {"time": 0.2667, "curve": [0.4, 0, 0.533, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.811, -12.14, 0.956, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -12.14]}, {"time": 1.5333, "value": -12.14, "curve": [1.578, -12.14, 1.64, -8.2]}, {"time": 1.6667, "value": -7.19}]}, "bone19": {"rotate": [{"value": -6.31, "curve": [0.053, -8.88, 0.133, -12.14]}, {"time": 0.2, "value": -12.14, "curve": [0.344, -12.14, 0.489, 0]}, {"time": 0.6333, "curve": [0.767, 0, 0.9, -12.14]}, {"time": 1.0333, "value": -12.14, "curve": [1.178, -12.14, 1.322, 0]}, {"time": 1.4667, "curve": [1.533, 0, 1.612, -3.85]}, {"time": 1.6667, "value": -6.31}]}, "bone22": {"rotate": [{"value": -6.94, "curve": [0.072, -4.08, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.8, -12.14, 0.933, 0]}, {"time": 1.0667, "curve": [1.211, 0, 1.356, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.617, -9.24]}, {"time": 1.6667, "value": -6.94}]}, "bone23": {"rotate": [{"value": -6.44, "curve": [0.054, -3.25, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.789, -12.14, 0.911, 0]}, {"time": 1.0333, "curve": [1.189, 0, 1.344, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.637, -8.8]}, {"time": 1.6667, "value": -6.44}]}, "bone27": {"rotate": [{"value": -9.1, "curve": [0.048, -5.6, 0.111, 0]}, {"time": 0.1667, "curve": [0.311, 0, 0.456, -22.76]}, {"time": 0.6, "value": -22.76, "curve": [0.733, -22.76, 0.867, 0]}, {"time": 1, "curve": [1.144, 0, 1.289, -22.76]}, {"time": 1.4333, "value": -22.76, "curve": [1.511, -22.76, 1.62, -12.74]}, {"time": 1.6667, "value": -9.1}]}, "bone26": {"rotate": [{"value": -19.12, "curve": [0.069, -10.86, 0.244, 0]}, {"time": 0.3667, "curve": [0.5, 0, 0.633, -22.76]}, {"time": 0.7667, "value": -22.76, "curve": [0.911, -22.76, 1.056, 0]}, {"time": 1.2, "curve": [1.333, 0, 1.467, -22.76]}, {"time": 1.6, "value": -22.76, "curve": [1.622, -22.76, 1.649, -21.21]}, {"time": 1.6667, "value": -19.12}]}, "bone25": {"rotate": [{"value": -22.3, "curve": [0.133, -22.3, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -22.3]}, {"time": 0.8333, "value": -22.3, "curve": [0.967, -22.3, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -22.3]}, {"time": 1.6667, "value": -22.3}]}, "skirt_02": {"rotate": [{"value": -18.66, "curve": [0.311, -18.66, 0.622, 0]}, {"time": 0.9333, "curve": [1.178, 0, 1.422, -18.66]}, {"time": 1.6667, "value": -18.66}]}, "target": {"translate": [{"x": 2.44, "y": 0.47, "curve": [0.079, 1.04, 0.156, 0, 0.079, 0.2, 0.156, 0]}, {"time": 0.2333, "curve": [0.5, 0, 0.767, 13.45, 0.5, 0, 0.767, 2.59]}, {"time": 1.0333, "x": 13.45, "y": 2.59, "curve": [1.245, 13.45, 1.457, 6.3, 1.245, 2.59, 1.457, 1.21]}, {"time": 1.6667, "x": 2.44, "y": 0.47}]}, "hair_023": {"rotate": [{"value": -14.83, "curve": [0.203, -19.31, 0.4, -31.88]}, {"time": 0.6, "value": -31.88, "curve": [0.767, -31.88, 0.938, -26.93]}, {"time": 1.1, "value": -20.42, "curve": [1.289, -12.84, 1.45, -10.8]}, {"time": 1.6667, "value": -14.83}], "translate": [{"x": -6.54, "y": -3.02, "curve": [0.2, -6.54, 0.4, -14.42, 0.2, -3.02, 0.4, -3.13]}, {"time": 0.6, "x": -14.42, "y": -3.13, "curve": [0.778, -14.42, 0.963, -13.64, 0.778, -3.13, 0.956, -0.61]}, {"time": 1.1333, "x": -11.75, "y": -0.61, "curve": [1.301, -9.89, 1.489, -6.54, 1.311, -0.61, 1.489, -3.02]}, {"time": 1.6667, "x": -6.54, "y": -3.02}]}, "target3": {"translate": [{"x": 2.78, "y": 3.39, "curve": [0.101, 1.47, 0.199, 0.38, 0.101, 5.73, 0.199, 7.68]}, {"time": 0.3, "x": 0.38, "y": 7.68, "curve": [0.589, 0.38, 0.878, 7.68, 0.589, 7.68, 0.878, -5.38]}, {"time": 1.1667, "x": 7.68, "y": -5.38, "curve": [1.346, 7.68, 1.508, 4.91, 1.346, -5.38, 1.508, -0.42]}, {"time": 1.6667, "x": 2.78, "y": 3.39}]}, "bone10": {"rotate": [{"value": -1.32}, {"time": 0.4667}, {"time": 1.2, "value": -2.65}, {"time": 1.6667, "value": -1.32}], "translate": [{"x": -1.27, "y": -2.89}, {"time": 0.4667}, {"time": 1.2, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.27, "y": -2.89}]}, "bone29": {"rotate": [{"value": -1.23}, {"time": 0.4333}, {"time": 1.1667, "value": -2.65}, {"time": 1.6667, "value": -1.23}], "translate": [{"x": -1.18, "y": -2.69}, {"time": 0.4333}, {"time": 1.1667, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.18, "y": -2.69}]}, "bone9": {"rotate": [{"value": -1.08}, {"time": 0.4333, "value": -2.65}, {"time": 1.3667}, {"time": 1.6667, "value": -1.08}], "translate": [{"x": -1.04, "y": -2.37}, {"time": 0.4333, "x": -2.53, "y": -5.78}, {"time": 1.3667}, {"time": 1.6667, "x": -1.04, "y": -2.37}]}, "bone16": {"rotate": [{"value": -6.51, "curve": [0.127, -3.56, 0.289, 0]}, {"time": 0.4333, "curve": [0.667, 0, 0.9, -14.51]}, {"time": 1.1333, "value": -14.51, "curve": [1.311, -14.51, 1.506, -10.34]}, {"time": 1.6667, "value": -6.51}]}, "bone18": {"rotate": [{"value": -10.51, "curve": [0.16, -6.55, 0.467, 0]}, {"time": 0.7, "curve": [0.933, 0, 1.167, -14.51]}, {"time": 1.4, "value": -14.51, "curve": [1.489, -14.51, 1.589, -11.92]}, {"time": 1.6667, "value": -10.51}]}, "bone17": {"rotate": [{"value": -11.01, "curve": [0.233, -7.37, 0.489, 0]}, {"time": 0.7333, "curve": [0.967, 0, 1.2, -14.51]}, {"time": 1.4333, "value": -14.51, "curve": [1.511, -14.51, 1.587, -12.61]}, {"time": 1.6667, "value": -11.01}]}, "bone12": {"rotate": [{"curve": [0.233, 0, 0.467, -14.51]}, {"time": 0.7, "value": -14.51, "curve": [1.022, -14.51, 1.344, 0]}, {"time": 1.6667}]}, "bone13": {"rotate": [{"value": -4.5, "curve": [0.094, -1.9, 0.2, 0]}, {"time": 0.3, "curve": [0.533, 0, 0.767, -14.51]}, {"time": 1, "value": -14.51, "curve": [1.222, -14.51, 1.444, -11.35]}, {"time": 1.6667, "value": -4.5}]}, "bone14": {"rotate": [{"value": -8.01, "curve": [0.186, -2.46, 0.356, 0]}, {"time": 0.5333, "curve": [0.767, 0, 1, -14.51]}, {"time": 1.2333, "value": -14.51, "curve": [1.378, -14.51, 1.512, -13.22]}, {"time": 1.6667, "value": -8.01}]}, "hair_05": {"rotate": [{"curve": [0.14, 0, 0.26, -5.67]}, {"time": 0.4, "value": -5.67, "curve": [0.54, -5.67, 0.719, 0]}, {"time": 0.8667, "curve": [1.007, 0, 1.127, -5.67]}, {"time": 1.2667, "value": -5.67, "curve": [1.407, -5.67, 1.532, 0]}, {"time": 1.6667}]}, "hair_06": {"rotate": [{"value": -3.94, "curve": [0.066, -1.64, 0.144, 0]}, {"time": 0.2, "curve": [0.34, 0, 0.46, -9.86]}, {"time": 0.6, "value": -9.86, "curve": [0.684, -9.86, 0.815, -5.63]}, {"time": 0.8667, "value": -3.94, "curve": [0.919, -2.25, 0.944, 0]}, {"time": 1, "curve": [1.14, 0, 1.26, -9.86]}, {"time": 1.4, "value": -9.86, "curve": [1.484, -9.86, 1.596, -6.37]}, {"time": 1.6667, "value": -3.94}]}, "hair_07": {"rotate": [{"value": -9.07, "curve": [0.129, -9.07, 0.271, 0]}, {"time": 0.4, "curve": [0.54, 0, 0.66, -9.79]}, {"time": 0.8, "value": -9.86, "curve": [0.973, -9.94, 1.071, 0]}, {"time": 1.2, "curve": [1.34, 0, 1.51, -9.07]}, {"time": 1.6667, "value": -9.07}]}, "hair_010": {"rotate": [{"value": -6.83}, {"time": 0.3, "curve": [0.444, -3.04, 0.589, -9.11]}, {"time": 0.7333, "value": -9.11, "curve": [0.867, -9.11, 1, 0]}, {"time": 1.1333, "curve": [1.278, 0, 1.422, -9.11]}, {"time": 1.5667, "value": -9.11, "curve": [1.6, -9.11, 1.633, -6.83]}, {"time": 1.6667, "value": -6.83}]}, "hair_011": {"rotate": [{"value": -9.11, "curve": [0.1, -9.11, 0.2, -4.46]}, {"time": 0.3, "value": -2.19, "curve": [0.333, -1.43, 0.367, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -9.11]}, {"time": 0.8333, "value": -9.11, "curve": [0.933, -9.11, 1.033, -4.46]}, {"time": 1.1333, "value": -2.19, "curve": [1.167, -1.43, 1.2, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -9.11]}, {"time": 1.6667, "value": -9.11}]}, "hair_012": {"rotate": [{"value": 1.66}, {"time": 0.1, "value": 2.16, "curve": [0.167, 1.79, 0.233, 1.4]}, {"time": 0.3, "value": 1.04, "curve": [0.367, 0.68, 0.433, 0]}, {"time": 0.5, "curve": [0.644, 0, 0.789, 2.16]}, {"time": 0.9333, "value": 2.16, "curve": [1, 2.16, 1.067, 1.4]}, {"time": 1.1333, "value": 1.04, "curve": [1.2, 0.68, 1.267, 0]}, {"time": 1.3333, "curve": [1.444, 0, 1.556, 1.66]}, {"time": 1.6667, "value": 1.66}]}, "hair_013": {"rotate": [{"value": 10.45}, {"time": 0.2, "value": 19.4, "curve": [0.233, 17.59, 0.267, 15.59]}, {"time": 0.3, "value": 13.97, "curve": [0.4, 9.12, 0.5, 0]}, {"time": 0.6, "curve": [0.744, 0, 0.889, 19.4]}, {"time": 1.0333, "value": 19.4, "curve": [1.067, 19.4, 1.1, 15.59]}, {"time": 1.1333, "value": 13.97, "curve": [1.233, 9.12, 1.333, 0]}, {"time": 1.4333, "curve": [1.511, 0, 1.589, 10.45]}, {"time": 1.6667, "value": 10.45}]}, "hair_014": {"rotate": [{"value": -3.89, "curve": [0.056, -3.89, 0.111, 0]}, {"time": 0.1667, "curve": [0.3, 0, 0.433, -9.71]}, {"time": 0.5667, "value": -9.71, "curve": [0.656, -9.71, 0.744, -5.88]}, {"time": 0.8333, "value": -3.89, "curve": [0.889, -2.64, 0.944, 0]}, {"time": 1, "curve": [1.133, 0, 1.267, -9.71]}, {"time": 1.4, "value": -9.71, "curve": [1.489, -9.71, 1.578, -3.89]}, {"time": 1.6667, "value": -3.89}]}, "hair_015": {"rotate": [{"value": -6.99, "curve": [0.1, -6.99, 0.2, 0]}, {"time": 0.3, "curve": [0.433, 0, 0.567, -9.71]}, {"time": 0.7, "value": -9.71, "curve": [0.744, -9.71, 0.789, -7.99]}, {"time": 0.8333, "value": -6.99, "curve": [0.933, -4.75, 1.033, 0]}, {"time": 1.1333, "curve": [1.267, 0, 1.4, -9.71]}, {"time": 1.5333, "value": -9.71, "curve": [1.578, -9.71, 1.622, -6.99]}, {"time": 1.6667, "value": -6.99}]}, "hair_016": {"rotate": [{"curve": [0.144, 0, 0.289, -9.71]}, {"time": 0.4333, "value": -9.71, "curve": [0.567, -9.71, 0.7, 0]}, {"time": 0.8333, "curve": [0.978, 0, 1.122, -9.71]}, {"time": 1.2667, "value": -9.71, "curve": [1.4, -9.71, 1.533, 0]}, {"time": 1.6667}]}, "hair_02": {"rotate": [{"value": -1.49}, {"time": 0.1667}, {"time": 0.6333, "value": -3.73}, {"time": 0.8333, "value": -1.49}, {"time": 1}, {"time": 1.4667, "value": -3.73}, {"time": 1.6667, "value": -1.49}]}, "hair_03": {"rotate": [{"value": -2.84}, {"time": 0.3333}, {"time": 0.7667, "value": -3.73}, {"time": 0.8333, "value": -2.84}, {"time": 1.1667}, {"time": 1.6, "value": -3.73}, {"time": 1.6667, "value": -2.84}]}, "hair_018": {"rotate": [{"curve": [0.133, 0, 0.267, -6.59]}, {"time": 0.4, "value": -6.59, "curve": [0.544, -6.59, 0.689, 0]}, {"time": 0.8333, "curve": [0.967, 0, 1.1, -6.59]}, {"time": 1.2333, "value": -6.59, "curve": [1.378, -6.59, 1.522, 0]}, {"time": 1.6667}]}, "hair_019": {"rotate": [{"value": -2.11, "curve": [0.044, -2.11, 0.089, 0]}, {"time": 0.1333, "curve": [0.267, 0, 0.4, -6.59]}, {"time": 0.5333, "value": -6.59, "curve": [0.633, -6.59, 0.733, -3.63]}, {"time": 0.8333, "value": -2.11, "curve": [0.878, -1.43, 0.922, 0]}, {"time": 0.9667, "curve": [1.1, 0, 1.233, -6.59]}, {"time": 1.3667, "value": -6.59, "curve": [1.467, -6.59, 1.567, -2.11]}, {"time": 1.6667, "value": -2.11}]}, "hair_020": {"rotate": [{"value": -4.48, "curve": [0.089, -4.48, 0.178, 0]}, {"time": 0.2667, "curve": [0.411, 0, 0.556, -6.59]}, {"time": 0.7, "value": -6.59, "curve": [0.744, -6.59, 0.789, -5.21]}, {"time": 0.8333, "value": -4.48, "curve": [0.922, -3.02, 1.011, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -6.59]}, {"time": 1.5333, "value": -6.59, "curve": [1.578, -6.59, 1.622, -4.48]}, {"time": 1.6667, "value": -4.48}]}, "hair_024": {"rotate": [{"value": -6.59, "curve": [0.133, -6.59, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -6.59]}, {"time": 0.8333, "value": -6.59, "curve": [0.967, -6.59, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -6.59]}, {"time": 1.6667, "value": -6.59}]}, "hair_04": {"rotate": [{"curve": [0.089, 1.26, 0.178, 5.76]}, {"time": 0.2667, "value": 5.76, "curve": [0.367, 5.76, 0.467, -2.67]}, {"time": 0.5667, "value": -2.67, "curve": [0.656, -2.67, 0.744, -1.41]}, {"time": 0.8333, "curve": [0.922, 1.41, 1.011, 5.76]}, {"time": 1.1, "value": 5.76, "curve": [1.2, 5.76, 1.3, -2.67]}, {"time": 1.4, "value": -2.67, "curve": [1.489, -2.67, 1.591, -1.11]}, {"time": 1.6667}]}, "hair_08": {"rotate": [{"value": -1, "curve": [0.043, -0.26, 0.067, 0.13]}, {"time": 0.1, "value": 0.74, "curve": [0.189, 2.38, 0.278, 5.76]}, {"time": 0.3667, "value": 5.76, "curve": [0.467, 5.76, 0.567, -2.67]}, {"time": 0.6667, "value": -2.67, "curve": [0.722, -2.67, 0.778, -2.04]}, {"time": 0.8333, "value": -1.48, "curve": [0.867, -1.15, 0.9, -0.61]}, {"time": 0.9333, "curve": [1.022, 1.64, 1.111, 5.76]}, {"time": 1.2, "value": 5.76, "curve": [1.3, 5.76, 1.4, -2.67]}, {"time": 1.5, "value": -2.67, "curve": [1.556, -2.67, 1.609, -1.78]}, {"time": 1.6667, "value": -1}]}, "hair_09": {"rotate": [{"value": -2.51, "curve": [0.089, -2.51, 0.178, -1.47]}, {"time": 0.2667, "curve": [0.344, 1.29, 0.422, 5.76]}, {"time": 0.5, "value": 5.76, "curve": [0.611, 5.76, 0.722, -2.51]}, {"time": 0.8333, "value": -2.51, "curve": [0.922, -2.51, 1.019, -1.89]}, {"time": 1.1, "value": -0.25, "curve": [1.178, 1.32, 1.256, 5.76]}, {"time": 1.3333, "value": 5.76, "curve": [1.444, 5.76, 1.556, -2.51]}, {"time": 1.6667, "value": -2.51}]}, "hair_026": {"rotate": [{"value": -0.45, "curve": [0.037, -1.2, 0.067, -2.67]}, {"time": 0.1, "value": -2.67, "curve": [0.189, -2.67, 0.278, -1.5]}, {"time": 0.3667, "curve": [0.444, 1.31, 0.522, 5.76]}, {"time": 0.6, "value": 5.76, "curve": [0.678, 5.76, 0.778, 1.48]}, {"time": 0.8333, "value": -0.75, "curve": [0.863, -1.93, 0.9, -2.67]}, {"time": 0.9333, "value": -2.67, "curve": [1.022, -2.67, 1.128, -1.8]}, {"time": 1.2, "curve": [1.27, 1.76, 1.356, 5.76]}, {"time": 1.4333, "value": 5.76, "curve": [1.511, 5.76, 1.639, 1.26]}, {"time": 1.6667, "value": -0.45}]}, "hair_027": {"rotate": [{"value": 0.43, "curve": [0.031, -0.42, 0.089, -2.67]}, {"time": 0.1333, "value": -2.67, "curve": [0.222, -2.67, 0.311, -1.5]}, {"time": 0.4, "curve": [0.478, 1.31, 0.556, 5.76]}, {"time": 0.6333, "value": 5.76, "curve": [0.7, 5.76, 0.784, 2.33]}, {"time": 0.8333, "value": 0.43, "curve": [0.868, -0.91, 0.922, -2.67]}, {"time": 0.9667, "value": -2.67, "curve": [1.056, -2.67, 1.153, -1.67]}, {"time": 1.2333, "curve": [1.309, 1.57, 1.389, 5.76]}, {"time": 1.4667, "value": 5.76, "curve": [1.533, 5.76, 1.629, 2.33]}, {"time": 1.6667, "value": 0.43}]}, "hair_028": {"rotate": [{"value": 2.65, "curve": [0.067, 2.65, 0.133, -2.67]}, {"time": 0.2, "value": -2.67, "curve": [0.289, -2.67, 0.378, -1.41]}, {"time": 0.4667, "curve": [0.556, 1.41, 0.644, 5.76]}, {"time": 0.7333, "value": 5.76, "curve": [0.767, 5.76, 0.8, 3.59]}, {"time": 0.8333, "value": 2.65, "curve": [0.9, 0.78, 0.967, -2.67]}, {"time": 1.0333, "value": -2.67, "curve": [1.122, -2.67, 1.211, -1.41]}, {"time": 1.3, "curve": [1.389, 1.41, 1.478, 5.76]}, {"time": 1.5667, "value": 5.76, "curve": [1.6, 5.76, 1.633, 2.65]}, {"time": 1.6667, "value": 2.65}]}, "head3": {"rotate": [{}, {"time": 0.8333, "value": 12.84}, {"time": 1.6667}]}, "head4": {"rotate": [{"value": 3.08}, {"time": 0.2}, {"time": 1.0333, "value": 12.84}, {"time": 1.6667, "value": 3.08}]}, "head": {"rotate": [{"curve": [0.121, -1.17, 0.289, -2.98]}, {"time": 0.4333, "value": -2.98, "curve": [0.722, -2.98, 1.012, 2.16]}, {"time": 1.3, "value": 2.32, "curve": [1.49, 2.42, 1.573, 1.06]}, {"time": 1.6667}]}, "vfx": {"translate": [{"curve": [0.233, 0, 0.467, 3.62, 0.233, 0, 0.467, 8.86]}, {"time": 0.7, "x": 3.62, "y": 8.86, "curve": [1.022, 3.62, 1.344, 0, 1.022, 8.86, 1.344, 0]}, {"time": 1.6667}]}}}, "character_01_pink": {"slots": {"glow (1)2": {"attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "glow (1)"}, {"time": 1.6333, "name": null}]}, "glow (1)3": {"attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "glow (1)"}, {"time": 1.6333, "name": null}]}, "glow (1)4": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 1.0667, "name": null}]}, "glow (1)5": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 1.0667, "name": null}]}, "glow (1)10": {"attachment": [{"time": 0.7667, "name": "glow (1)"}, {"time": 1.4333, "name": null}]}, "glow (1)11": {"attachment": [{"time": 0.7667, "name": "glow (1)"}, {"time": 1.4333, "name": null}]}, "glow (1)12": {"attachment": [{"time": 1.4667, "name": "glow (1)"}]}, "glow (1)13": {"attachment": [{"time": 1.4667, "name": "glow (1)"}]}, "glow (1)14": {"rgba": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffff03"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 1.3, "color": "ffffff03"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.2333, "name": null}, {"time": 1.3, "name": "glow (1)"}, {"time": 1.5333, "name": null}]}, "glow (1)15": {"rgba": [{"color": "00d6ff00"}, {"time": 0.0667, "color": "00d6ff9b"}, {"time": 0.2333, "color": "00d6ff00"}, {"time": 1.3, "color": "00d6ff9b"}, {"time": 1.5333, "color": "00d6ff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.2333, "name": null}, {"time": 1.3, "name": "glow (1)"}, {"time": 1.5333, "name": null}]}, "lighting_01": {"attachment": [{"time": 0.0667, "name": "lighting_01"}, {"time": 0.1, "name": "lighting_02"}, {"time": 0.1667, "name": "lighting_03"}, {"time": 0.2333, "name": "lighting_04"}, {"time": 0.2667, "name": "lighting_01"}, {"time": 0.3333, "name": "lighting_02"}, {"time": 0.4, "name": "lighting_03"}, {"time": 0.4333, "name": "lighting_04"}, {"time": 0.5, "name": "lighting_01"}, {"time": 0.5667, "name": "lighting_02"}, {"time": 0.6, "name": "lighting_03"}, {"time": 0.6667, "name": "lighting_04"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "lighting_01"}, {"time": 1.5333, "name": "lighting_02"}, {"time": 1.6, "name": "lighting_03"}, {"time": 1.6333, "name": "lighting_04"}]}, "lighting_02": {"attachment": [{"time": 0.2333, "name": "lighting_01"}, {"time": 0.2667, "name": "lighting_02"}, {"time": 0.3333, "name": "lighting_03"}, {"time": 0.4, "name": "lighting_04"}, {"time": 0.4333, "name": "lighting_01"}, {"time": 0.5, "name": "lighting_02"}, {"time": 0.5667, "name": "lighting_03"}, {"time": 0.6, "name": "lighting_04"}, {"time": 0.6667, "name": "lighting_01"}, {"time": 0.7333, "name": "lighting_02"}, {"time": 0.7667, "name": "lighting_03"}, {"time": 0.8333, "name": "lighting_01"}, {"time": 0.9, "name": "lighting_02"}, {"time": 0.9333, "name": "lighting_03"}, {"time": 1, "name": "lighting_04"}, {"time": 1.0667, "name": null}]}, "lighting_03": {"attachment": [{"time": 0.7667, "name": "lighting_01"}, {"time": 0.8333, "name": "lighting_02"}, {"time": 0.9, "name": "lighting_03"}, {"time": 0.9333, "name": "lighting_04"}, {"time": 1, "name": "lighting_01"}, {"time": 1.0667, "name": "lighting_02"}, {"time": 1.1, "name": "lighting_03"}, {"time": 1.1667, "name": "lighting_04"}, {"time": 1.2333, "name": "lighting_01"}, {"time": 1.2667, "name": "lighting_02"}, {"time": 1.3333, "name": "lighting_03"}, {"time": 1.4, "name": "lighting_04"}, {"time": 1.4333, "name": null}]}}, "bones": {"VFX2": {"rotate": [{}, {"time": 0.1667, "value": -12.99}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.4333}, {"time": 1.6333, "value": -12.99}], "scale": [{}, {"time": 0.1667, "y": 0.7}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.4333}, {"time": 1.6333, "y": 0.7}]}, "VFX3": {"rotate": [{}, {"time": 0.4333, "value": 17.98}, {"time": 1.4333}], "translate": [{}, {"time": 0.2333, "x": -29.61}, {"time": 0.6667, "x": -55.07, "y": 9.19}, {"time": 1.4333}], "scale": [{}, {"time": 0.4333, "x": 0.951, "y": 1.135}, {"time": 0.6667, "x": 0.922, "y": 0.922}, {"time": 1.4333}]}, "VFX5": {"rotate": [{"time": 0.1667}, {"time": 0.3333, "value": -12.99}, {"time": 1}], "translate": [{"time": 0.1667}, {"time": 1, "x": 7.49, "y": 2.88}], "scale": [{"time": 0.1667}, {"time": 0.3333, "y": 0.7}, {"time": 1}]}, "VFX6": {"rotate": [{"time": 0.1667}, {"time": 0.6, "value": 17.98}, {"time": 1, "value": 40.65}], "translate": [{"time": 0.1667, "x": 9.7, "y": 51.76}, {"time": 1, "x": 30.25, "y": 67.27}], "scale": [{"time": 0.1667}, {"time": 0.6, "x": 0.951, "y": 1.135}, {"time": 1, "x": 0.922, "y": 1.029}]}, "VFX4": {"rotate": [{"value": -39.43}], "translate": [{"x": -219.72, "y": -73.95}]}, "VFX8": {"rotate": [{"time": 0.7333}, {"time": 0.9, "value": -12.99}, {"time": 1.4}], "translate": [{"time": 0.7333, "x": 25.47, "y": -5.39}, {"time": 1.4, "x": 3.98, "y": 5.12}], "scale": [{"time": 0.7333}, {"time": 0.9, "y": 0.7}, {"time": 1.4}]}, "VFX9": {"scale": [{"x": 0.951, "y": 1.135}]}, "VFX7": {"rotate": [{"value": -21.51}], "translate": [{"x": -70.29, "y": 24.6}]}, "VFX": {"rotate": [{"time": 1.4333, "value": -180.22}], "translate": [{"time": 1.4333, "x": -163.49, "y": 100.41}], "scale": [{"time": 1.4333, "x": 0.446, "y": 0.446}]}, "glow6": {"scale": [{"time": 1.4667, "x": 1.84, "y": 1.84}]}, "glow": {"scale": [{"time": 1.4667, "x": 1.84, "y": 1.84}]}, "glow7": {"rotate": [{"time": 0.0667, "value": 14.58}], "translate": [{"time": 0.0667, "x": -52.34, "y": -43.5}], "scale": [{"time": 0.0667, "x": 25.935, "y": 25.51}]}, "bone": {"translate": [{"curve": [0.233, 0, 0.467, -11.25, 0.233, 0, 0.467, -3.44]}, {"time": 0.7, "x": -11.25, "y": -3.44, "curve": [1.022, -11.25, 1.344, 0, 1.022, -3.44, 1.344, 0]}, {"time": 1.6667}]}, "bone20": {"rotate": [{"value": -11.15, "curve": [0.122, -11.15, 0.244, 0]}, {"time": 0.3667, "curve": [0.522, 0, 0.678, -12.14]}, {"time": 0.8333, "value": -12.14, "curve": [0.967, -12.14, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -11.15]}, {"time": 1.6667, "value": -11.15}]}, "bone21": {"rotate": [{"value": -7.19, "curve": [0.068, -5.05, 0.178, 0]}, {"time": 0.2667, "curve": [0.4, 0, 0.533, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.811, -12.14, 0.956, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -12.14]}, {"time": 1.5333, "value": -12.14, "curve": [1.578, -12.14, 1.64, -8.2]}, {"time": 1.6667, "value": -7.19}]}, "bone19": {"rotate": [{"value": -6.31, "curve": [0.053, -8.88, 0.133, -12.14]}, {"time": 0.2, "value": -12.14, "curve": [0.344, -12.14, 0.489, 0]}, {"time": 0.6333, "curve": [0.767, 0, 0.9, -12.14]}, {"time": 1.0333, "value": -12.14, "curve": [1.178, -12.14, 1.322, 0]}, {"time": 1.4667, "curve": [1.533, 0, 1.612, -3.85]}, {"time": 1.6667, "value": -6.31}]}, "bone22": {"rotate": [{"value": -6.94, "curve": [0.072, -4.08, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.8, -12.14, 0.933, 0]}, {"time": 1.0667, "curve": [1.211, 0, 1.356, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.617, -9.24]}, {"time": 1.6667, "value": -6.94}]}, "bone23": {"rotate": [{"value": -6.44, "curve": [0.054, -3.25, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.789, -12.14, 0.911, 0]}, {"time": 1.0333, "curve": [1.189, 0, 1.344, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.637, -8.8]}, {"time": 1.6667, "value": -6.44}]}, "bone27": {"rotate": [{"value": -9.1, "curve": [0.048, -5.6, 0.111, 0]}, {"time": 0.1667, "curve": [0.311, 0, 0.456, -22.76]}, {"time": 0.6, "value": -22.76, "curve": [0.733, -22.76, 0.867, 0]}, {"time": 1, "curve": [1.144, 0, 1.289, -22.76]}, {"time": 1.4333, "value": -22.76, "curve": [1.511, -22.76, 1.62, -12.74]}, {"time": 1.6667, "value": -9.1}]}, "bone26": {"rotate": [{"value": -19.12, "curve": [0.069, -10.86, 0.244, 0]}, {"time": 0.3667, "curve": [0.5, 0, 0.633, -22.76]}, {"time": 0.7667, "value": -22.76, "curve": [0.911, -22.76, 1.056, 0]}, {"time": 1.2, "curve": [1.333, 0, 1.467, -22.76]}, {"time": 1.6, "value": -22.76, "curve": [1.622, -22.76, 1.649, -21.21]}, {"time": 1.6667, "value": -19.12}]}, "bone25": {"rotate": [{"value": -22.3, "curve": [0.133, -22.3, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -22.3]}, {"time": 0.8333, "value": -22.3, "curve": [0.967, -22.3, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -22.3]}, {"time": 1.6667, "value": -22.3}]}, "skirt_02": {"rotate": [{"value": -18.66, "curve": [0.311, -18.66, 0.622, 0]}, {"time": 0.9333, "curve": [1.178, 0, 1.422, -18.66]}, {"time": 1.6667, "value": -18.66}]}, "target": {"translate": [{"x": 2.44, "y": 0.47, "curve": [0.079, 1.04, 0.156, 0, 0.079, 0.2, 0.156, 0]}, {"time": 0.2333, "curve": [0.5, 0, 0.767, 13.45, 0.5, 0, 0.767, 2.59]}, {"time": 1.0333, "x": 13.45, "y": 2.59, "curve": [1.245, 13.45, 1.457, 6.3, 1.245, 2.59, 1.457, 1.21]}, {"time": 1.6667, "x": 2.44, "y": 0.47}]}, "hair_023": {"rotate": [{"value": -14.83, "curve": [0.203, -19.31, 0.4, -31.88]}, {"time": 0.6, "value": -31.88, "curve": [0.767, -31.88, 0.938, -26.93]}, {"time": 1.1, "value": -20.42, "curve": [1.289, -12.84, 1.45, -10.8]}, {"time": 1.6667, "value": -14.83}], "translate": [{"x": -6.54, "y": -3.02, "curve": [0.2, -6.54, 0.4, -14.42, 0.2, -3.02, 0.4, -3.13]}, {"time": 0.6, "x": -14.42, "y": -3.13, "curve": [0.778, -14.42, 0.963, -13.64, 0.778, -3.13, 0.956, -0.61]}, {"time": 1.1333, "x": -11.75, "y": -0.61, "curve": [1.301, -9.89, 1.489, -6.54, 1.311, -0.61, 1.489, -3.02]}, {"time": 1.6667, "x": -6.54, "y": -3.02}]}, "target3": {"translate": [{"x": 2.78, "y": 3.39, "curve": [0.101, 1.47, 0.199, 0.38, 0.101, 5.73, 0.199, 7.68]}, {"time": 0.3, "x": 0.38, "y": 7.68, "curve": [0.589, 0.38, 0.878, 7.68, 0.589, 7.68, 0.878, -5.38]}, {"time": 1.1667, "x": 7.68, "y": -5.38, "curve": [1.346, 7.68, 1.508, 4.91, 1.346, -5.38, 1.508, -0.42]}, {"time": 1.6667, "x": 2.78, "y": 3.39}]}, "bone10": {"rotate": [{"value": -1.32}, {"time": 0.4667}, {"time": 1.2, "value": -2.65}, {"time": 1.6667, "value": -1.32}], "translate": [{"x": -1.27, "y": -2.89}, {"time": 0.4667}, {"time": 1.2, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.27, "y": -2.89}]}, "bone29": {"rotate": [{"value": -1.23}, {"time": 0.4333}, {"time": 1.1667, "value": -2.65}, {"time": 1.6667, "value": -1.23}], "translate": [{"x": -1.18, "y": -2.69}, {"time": 0.4333}, {"time": 1.1667, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.18, "y": -2.69}]}, "bone9": {"rotate": [{"value": -1.08}, {"time": 0.4333, "value": -2.65}, {"time": 1.3667}, {"time": 1.6667, "value": -1.08}], "translate": [{"x": -1.04, "y": -2.37}, {"time": 0.4333, "x": -2.53, "y": -5.78}, {"time": 1.3667}, {"time": 1.6667, "x": -1.04, "y": -2.37}]}, "bone16": {"rotate": [{"value": -6.51, "curve": [0.127, -3.56, 0.289, 0]}, {"time": 0.4333, "curve": [0.667, 0, 0.9, -14.51]}, {"time": 1.1333, "value": -14.51, "curve": [1.311, -14.51, 1.506, -10.34]}, {"time": 1.6667, "value": -6.51}]}, "bone18": {"rotate": [{"value": -10.51, "curve": [0.16, -6.55, 0.467, 0]}, {"time": 0.7, "curve": [0.933, 0, 1.167, -14.51]}, {"time": 1.4, "value": -14.51, "curve": [1.489, -14.51, 1.589, -11.92]}, {"time": 1.6667, "value": -10.51}]}, "bone17": {"rotate": [{"value": -11.01, "curve": [0.233, -7.37, 0.489, 0]}, {"time": 0.7333, "curve": [0.967, 0, 1.2, -14.51]}, {"time": 1.4333, "value": -14.51, "curve": [1.511, -14.51, 1.587, -12.61]}, {"time": 1.6667, "value": -11.01}]}, "bone12": {"rotate": [{"curve": [0.233, 0, 0.467, -14.51]}, {"time": 0.7, "value": -14.51, "curve": [1.022, -14.51, 1.344, 0]}, {"time": 1.6667}]}, "bone13": {"rotate": [{"value": -4.5, "curve": [0.094, -1.9, 0.2, 0]}, {"time": 0.3, "curve": [0.533, 0, 0.767, -14.51]}, {"time": 1, "value": -14.51, "curve": [1.222, -14.51, 1.444, -11.35]}, {"time": 1.6667, "value": -4.5}]}, "bone14": {"rotate": [{"value": -8.01, "curve": [0.186, -2.46, 0.356, 0]}, {"time": 0.5333, "curve": [0.767, 0, 1, -14.51]}, {"time": 1.2333, "value": -14.51, "curve": [1.378, -14.51, 1.512, -13.22]}, {"time": 1.6667, "value": -8.01}]}, "hair_05": {"rotate": [{"curve": [0.14, 0, 0.26, -5.67]}, {"time": 0.4, "value": -5.67, "curve": [0.54, -5.67, 0.719, 0]}, {"time": 0.8667, "curve": [1.007, 0, 1.127, -5.67]}, {"time": 1.2667, "value": -5.67, "curve": [1.407, -5.67, 1.532, 0]}, {"time": 1.6667}]}, "hair_06": {"rotate": [{"value": -3.94, "curve": [0.066, -1.64, 0.144, 0]}, {"time": 0.2, "curve": [0.34, 0, 0.46, -9.86]}, {"time": 0.6, "value": -9.86, "curve": [0.684, -9.86, 0.815, -5.63]}, {"time": 0.8667, "value": -3.94, "curve": [0.919, -2.25, 0.944, 0]}, {"time": 1, "curve": [1.14, 0, 1.26, -9.86]}, {"time": 1.4, "value": -9.86, "curve": [1.484, -9.86, 1.596, -6.37]}, {"time": 1.6667, "value": -3.94}]}, "hair_07": {"rotate": [{"value": -9.07, "curve": [0.129, -9.07, 0.271, 0]}, {"time": 0.4, "curve": [0.54, 0, 0.66, -9.79]}, {"time": 0.8, "value": -9.86, "curve": [0.973, -9.94, 1.071, 0]}, {"time": 1.2, "curve": [1.34, 0, 1.51, -9.07]}, {"time": 1.6667, "value": -9.07}]}, "hair_010": {"rotate": [{"value": -6.83}, {"time": 0.3, "curve": [0.444, -3.04, 0.589, -9.11]}, {"time": 0.7333, "value": -9.11, "curve": [0.867, -9.11, 1, 0]}, {"time": 1.1333, "curve": [1.278, 0, 1.422, -9.11]}, {"time": 1.5667, "value": -9.11, "curve": [1.6, -9.11, 1.633, -6.83]}, {"time": 1.6667, "value": -6.83}]}, "hair_011": {"rotate": [{"value": -9.11, "curve": [0.1, -9.11, 0.2, -4.46]}, {"time": 0.3, "value": -2.19, "curve": [0.333, -1.43, 0.367, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -9.11]}, {"time": 0.8333, "value": -9.11, "curve": [0.933, -9.11, 1.033, -4.46]}, {"time": 1.1333, "value": -2.19, "curve": [1.167, -1.43, 1.2, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -9.11]}, {"time": 1.6667, "value": -9.11}]}, "hair_012": {"rotate": [{"value": 1.66}, {"time": 0.1, "value": 2.16, "curve": [0.167, 1.79, 0.233, 1.4]}, {"time": 0.3, "value": 1.04, "curve": [0.367, 0.68, 0.433, 0]}, {"time": 0.5, "curve": [0.644, 0, 0.789, 2.16]}, {"time": 0.9333, "value": 2.16, "curve": [1, 2.16, 1.067, 1.4]}, {"time": 1.1333, "value": 1.04, "curve": [1.2, 0.68, 1.267, 0]}, {"time": 1.3333, "curve": [1.444, 0, 1.556, 1.66]}, {"time": 1.6667, "value": 1.66}]}, "hair_013": {"rotate": [{"value": 10.45}, {"time": 0.2, "value": 19.4, "curve": [0.233, 17.59, 0.267, 15.59]}, {"time": 0.3, "value": 13.97, "curve": [0.4, 9.12, 0.5, 0]}, {"time": 0.6, "curve": [0.744, 0, 0.889, 19.4]}, {"time": 1.0333, "value": 19.4, "curve": [1.067, 19.4, 1.1, 15.59]}, {"time": 1.1333, "value": 13.97, "curve": [1.233, 9.12, 1.333, 0]}, {"time": 1.4333, "curve": [1.511, 0, 1.589, 10.45]}, {"time": 1.6667, "value": 10.45}]}, "hair_014": {"rotate": [{"value": -3.89, "curve": [0.056, -3.89, 0.111, 0]}, {"time": 0.1667, "curve": [0.3, 0, 0.433, -9.71]}, {"time": 0.5667, "value": -9.71, "curve": [0.656, -9.71, 0.744, -5.88]}, {"time": 0.8333, "value": -3.89, "curve": [0.889, -2.64, 0.944, 0]}, {"time": 1, "curve": [1.133, 0, 1.267, -9.71]}, {"time": 1.4, "value": -9.71, "curve": [1.489, -9.71, 1.578, -3.89]}, {"time": 1.6667, "value": -3.89}]}, "hair_015": {"rotate": [{"value": -6.99, "curve": [0.1, -6.99, 0.2, 0]}, {"time": 0.3, "curve": [0.433, 0, 0.567, -9.71]}, {"time": 0.7, "value": -9.71, "curve": [0.744, -9.71, 0.789, -7.99]}, {"time": 0.8333, "value": -6.99, "curve": [0.933, -4.75, 1.033, 0]}, {"time": 1.1333, "curve": [1.267, 0, 1.4, -9.71]}, {"time": 1.5333, "value": -9.71, "curve": [1.578, -9.71, 1.622, -6.99]}, {"time": 1.6667, "value": -6.99}]}, "hair_016": {"rotate": [{"curve": [0.144, 0, 0.289, -9.71]}, {"time": 0.4333, "value": -9.71, "curve": [0.567, -9.71, 0.7, 0]}, {"time": 0.8333, "curve": [0.978, 0, 1.122, -9.71]}, {"time": 1.2667, "value": -9.71, "curve": [1.4, -9.71, 1.533, 0]}, {"time": 1.6667}]}, "hair_02": {"rotate": [{"value": -1.49}, {"time": 0.1667}, {"time": 0.6333, "value": -3.73}, {"time": 0.8333, "value": -1.49}, {"time": 1}, {"time": 1.4667, "value": -3.73}, {"time": 1.6667, "value": -1.49}]}, "hair_03": {"rotate": [{"value": -2.84}, {"time": 0.3333}, {"time": 0.7667, "value": -3.73}, {"time": 0.8333, "value": -2.84}, {"time": 1.1667}, {"time": 1.6, "value": -3.73}, {"time": 1.6667, "value": -2.84}]}, "hair_018": {"rotate": [{"curve": [0.133, 0, 0.267, -6.59]}, {"time": 0.4, "value": -6.59, "curve": [0.544, -6.59, 0.689, 0]}, {"time": 0.8333, "curve": [0.967, 0, 1.1, -6.59]}, {"time": 1.2333, "value": -6.59, "curve": [1.378, -6.59, 1.522, 0]}, {"time": 1.6667}]}, "hair_019": {"rotate": [{"value": -2.11, "curve": [0.044, -2.11, 0.089, 0]}, {"time": 0.1333, "curve": [0.267, 0, 0.4, -6.59]}, {"time": 0.5333, "value": -6.59, "curve": [0.633, -6.59, 0.733, -3.63]}, {"time": 0.8333, "value": -2.11, "curve": [0.878, -1.43, 0.922, 0]}, {"time": 0.9667, "curve": [1.1, 0, 1.233, -6.59]}, {"time": 1.3667, "value": -6.59, "curve": [1.467, -6.59, 1.567, -2.11]}, {"time": 1.6667, "value": -2.11}]}, "hair_020": {"rotate": [{"value": -4.48, "curve": [0.089, -4.48, 0.178, 0]}, {"time": 0.2667, "curve": [0.411, 0, 0.556, -6.59]}, {"time": 0.7, "value": -6.59, "curve": [0.744, -6.59, 0.789, -5.21]}, {"time": 0.8333, "value": -4.48, "curve": [0.922, -3.02, 1.011, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -6.59]}, {"time": 1.5333, "value": -6.59, "curve": [1.578, -6.59, 1.622, -4.48]}, {"time": 1.6667, "value": -4.48}]}, "hair_024": {"rotate": [{"value": -6.59, "curve": [0.133, -6.59, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -6.59]}, {"time": 0.8333, "value": -6.59, "curve": [0.967, -6.59, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -6.59]}, {"time": 1.6667, "value": -6.59}]}, "hair_04": {"rotate": [{"curve": [0.089, 1.26, 0.178, 5.76]}, {"time": 0.2667, "value": 5.76, "curve": [0.367, 5.76, 0.467, -2.67]}, {"time": 0.5667, "value": -2.67, "curve": [0.656, -2.67, 0.744, -1.41]}, {"time": 0.8333, "curve": [0.922, 1.41, 1.011, 5.76]}, {"time": 1.1, "value": 5.76, "curve": [1.2, 5.76, 1.3, -2.67]}, {"time": 1.4, "value": -2.67, "curve": [1.489, -2.67, 1.591, -1.11]}, {"time": 1.6667}]}, "hair_08": {"rotate": [{"value": -1, "curve": [0.043, -0.26, 0.067, 0.13]}, {"time": 0.1, "value": 0.74, "curve": [0.189, 2.38, 0.278, 5.76]}, {"time": 0.3667, "value": 5.76, "curve": [0.467, 5.76, 0.567, -2.67]}, {"time": 0.6667, "value": -2.67, "curve": [0.722, -2.67, 0.778, -2.04]}, {"time": 0.8333, "value": -1.48, "curve": [0.867, -1.15, 0.9, -0.61]}, {"time": 0.9333, "curve": [1.022, 1.64, 1.111, 5.76]}, {"time": 1.2, "value": 5.76, "curve": [1.3, 5.76, 1.4, -2.67]}, {"time": 1.5, "value": -2.67, "curve": [1.556, -2.67, 1.609, -1.78]}, {"time": 1.6667, "value": -1}]}, "hair_09": {"rotate": [{"value": -2.51, "curve": [0.089, -2.51, 0.178, -1.47]}, {"time": 0.2667, "curve": [0.344, 1.29, 0.422, 5.76]}, {"time": 0.5, "value": 5.76, "curve": [0.611, 5.76, 0.722, -2.51]}, {"time": 0.8333, "value": -2.51, "curve": [0.922, -2.51, 1.019, -1.89]}, {"time": 1.1, "value": -0.25, "curve": [1.178, 1.32, 1.256, 5.76]}, {"time": 1.3333, "value": 5.76, "curve": [1.444, 5.76, 1.556, -2.51]}, {"time": 1.6667, "value": -2.51}]}, "hair_026": {"rotate": [{"value": -0.45, "curve": [0.037, -1.2, 0.067, -2.67]}, {"time": 0.1, "value": -2.67, "curve": [0.189, -2.67, 0.278, -1.5]}, {"time": 0.3667, "curve": [0.444, 1.31, 0.522, 5.76]}, {"time": 0.6, "value": 5.76, "curve": [0.678, 5.76, 0.778, 1.48]}, {"time": 0.8333, "value": -0.75, "curve": [0.863, -1.93, 0.9, -2.67]}, {"time": 0.9333, "value": -2.67, "curve": [1.022, -2.67, 1.128, -1.8]}, {"time": 1.2, "curve": [1.27, 1.76, 1.356, 5.76]}, {"time": 1.4333, "value": 5.76, "curve": [1.511, 5.76, 1.639, 1.26]}, {"time": 1.6667, "value": -0.45}]}, "hair_027": {"rotate": [{"value": 0.43, "curve": [0.031, -0.42, 0.089, -2.67]}, {"time": 0.1333, "value": -2.67, "curve": [0.222, -2.67, 0.311, -1.5]}, {"time": 0.4, "curve": [0.478, 1.31, 0.556, 5.76]}, {"time": 0.6333, "value": 5.76, "curve": [0.7, 5.76, 0.784, 2.33]}, {"time": 0.8333, "value": 0.43, "curve": [0.868, -0.91, 0.922, -2.67]}, {"time": 0.9667, "value": -2.67, "curve": [1.056, -2.67, 1.153, -1.67]}, {"time": 1.2333, "curve": [1.309, 1.57, 1.389, 5.76]}, {"time": 1.4667, "value": 5.76, "curve": [1.533, 5.76, 1.629, 2.33]}, {"time": 1.6667, "value": 0.43}]}, "hair_028": {"rotate": [{"value": 2.65, "curve": [0.067, 2.65, 0.133, -2.67]}, {"time": 0.2, "value": -2.67, "curve": [0.289, -2.67, 0.378, -1.41]}, {"time": 0.4667, "curve": [0.556, 1.41, 0.644, 5.76]}, {"time": 0.7333, "value": 5.76, "curve": [0.767, 5.76, 0.8, 3.59]}, {"time": 0.8333, "value": 2.65, "curve": [0.9, 0.78, 0.967, -2.67]}, {"time": 1.0333, "value": -2.67, "curve": [1.122, -2.67, 1.211, -1.41]}, {"time": 1.3, "curve": [1.389, 1.41, 1.478, 5.76]}, {"time": 1.5667, "value": 5.76, "curve": [1.6, 5.76, 1.633, 2.65]}, {"time": 1.6667, "value": 2.65}]}, "head3": {"rotate": [{}, {"time": 0.8333, "value": 12.84}, {"time": 1.6667}]}, "head4": {"rotate": [{"value": 3.08}, {"time": 0.2}, {"time": 1.0333, "value": 12.84}, {"time": 1.6667, "value": 3.08}]}, "head": {"rotate": [{"curve": [0.121, -1.17, 0.289, -2.98]}, {"time": 0.4333, "value": -2.98, "curve": [0.722, -2.98, 1.012, 2.16]}, {"time": 1.3, "value": 2.32, "curve": [1.49, 2.42, 1.573, 1.06]}, {"time": 1.6667}]}, "vfx": {"translate": [{"curve": [0.233, 0, 0.467, 3.62, 0.233, 0, 0.467, 8.86]}, {"time": 0.7, "x": 3.62, "y": 8.86, "curve": [1.022, 3.62, 1.344, 0, 1.022, 8.86, 1.344, 0]}, {"time": 1.6667}]}}}, "character_01_yellow": {"slots": {"glow (1)2": {"attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "glow (1)"}, {"time": 1.6333, "name": null}]}, "glow (1)3": {"attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "glow (1)"}, {"time": 1.6333, "name": null}]}, "glow (1)4": {"attachment": [{"time": 0.5333, "name": "glow (1)"}, {"time": 1.1, "name": null}]}, "glow (1)5": {"attachment": [{"time": 0.5333, "name": "glow (1)"}, {"time": 1.1, "name": null}]}, "glow (1)10": {"attachment": [{"time": 0.7667, "name": "glow (1)"}, {"time": 1.4333, "name": null}]}, "glow (1)11": {"attachment": [{"time": 0.7667, "name": "glow (1)"}, {"time": 1.4333, "name": null}]}, "glow (1)12": {"attachment": [{"time": 1.4667, "name": "glow (1)"}]}, "glow (1)13": {"attachment": [{"time": 1.4667, "name": "glow (1)"}]}, "glow (1)14": {"rgba": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffff03"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 1.3, "color": "ffffff03"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.2333, "name": null}, {"time": 1.3, "name": "glow (1)"}, {"time": 1.5333, "name": null}]}, "glow (1)15": {"rgba": [{"color": "00d6ff00"}, {"time": 0.0667, "color": "00d6ff9b"}, {"time": 0.2333, "color": "00d6ff00"}, {"time": 1.3, "color": "00d6ff9b"}, {"time": 1.5333, "color": "00d6ff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.2333, "name": null}, {"time": 1.3, "name": "glow (1)"}, {"time": 1.5333, "name": null}]}, "lighting_01": {"attachment": [{"time": 0.0667, "name": "lighting_01"}, {"time": 0.1, "name": "lighting_02"}, {"time": 0.1667, "name": "lighting_03"}, {"time": 0.2333, "name": "lighting_04"}, {"time": 0.2667, "name": "lighting_01"}, {"time": 0.3333, "name": "lighting_02"}, {"time": 0.4, "name": "lighting_03"}, {"time": 0.4333, "name": "lighting_04"}, {"time": 0.5, "name": "lighting_01"}, {"time": 0.5667, "name": "lighting_02"}, {"time": 0.6, "name": "lighting_03"}, {"time": 0.6667, "name": "lighting_04"}, {"time": 0.7333, "name": null}, {"time": 1.4667, "name": "lighting_01"}, {"time": 1.5333, "name": "lighting_02"}, {"time": 1.6, "name": "lighting_03"}, {"time": 1.6333, "name": "lighting_04"}]}, "lighting_02": {"attachment": [{"time": 0.5333, "name": "lighting_02"}, {"time": 0.6, "name": "lighting_03"}, {"time": 0.6333, "name": "lighting_04"}, {"time": 0.6667, "name": "lighting_01"}, {"time": 0.7333, "name": "lighting_03"}, {"time": 0.7667, "name": "lighting_04"}, {"time": 0.8333, "name": "lighting_01"}, {"time": 0.8667, "name": "lighting_02"}, {"time": 0.9, "name": "lighting_03"}, {"time": 0.9333, "name": "lighting_01"}, {"time": 1, "name": "lighting_02"}, {"time": 1.0333, "name": "lighting_03"}, {"time": 1.0667, "name": "lighting_04"}, {"time": 1.1, "name": null}]}, "lighting_03": {"attachment": [{"time": 0.7667, "name": "lighting_01"}, {"time": 0.8333, "name": "lighting_02"}, {"time": 0.9, "name": "lighting_03"}, {"time": 0.9333, "name": "lighting_04"}, {"time": 1, "name": "lighting_01"}, {"time": 1.0667, "name": "lighting_02"}, {"time": 1.1, "name": "lighting_03"}, {"time": 1.1667, "name": "lighting_04"}, {"time": 1.2333, "name": "lighting_01"}, {"time": 1.2667, "name": "lighting_02"}, {"time": 1.3333, "name": "lighting_03"}, {"time": 1.4, "name": "lighting_04"}, {"time": 1.4333, "name": null}]}, "shadow": {"rgba": [{"color": "fc000000", "curve": [0.078, 0.99, 0.189, 0.96, 0.078, 0, 0.189, 0.01, 0.078, 0, 0.189, 0.74, 0.078, 0, 0.189, 0.43]}, {"time": 0.2667, "color": "f502bc6e", "curve": [0.367, 0.96, 0.522, 0.99, 0.367, 0.01, 0.522, 0, 0.367, 0.74, 0.522, 0.61, 0.367, 0.43, 0.522, 0.43]}, {"time": 0.6, "color": "fc009b6e", "curve": [0.7, 0.99, 0.333, 0.99, 0.7, 0, 0.333, 0, 0.7, 0.61, 0.333, 0.2, 0.7, 0.43, 0.333, 0.14]}, {"time": 1.4333, "color": "fc000000"}]}}, "bones": {"VFX2": {"rotate": [{}, {"time": 0.1667, "value": -12.99}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.4333}, {"time": 1.6333, "value": -12.99}], "scale": [{}, {"time": 0.1667, "y": 0.7}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.4333}, {"time": 1.6333, "y": 0.7}]}, "VFX3": {"rotate": [{}, {"time": 0.4333, "value": 17.98}, {"time": 1.4333}], "translate": [{}, {"time": 0.2333, "x": -29.61}, {"time": 0.6667, "x": -55.07, "y": 9.19}, {"time": 1.4333}], "scale": [{}, {"time": 0.4333, "x": 0.951, "y": 1.135}, {"time": 0.6667, "x": 0.922, "y": 0.922}, {"time": 1.4333}]}, "VFX5": {"rotate": [{"time": 0.4667}, {"time": 0.6, "value": -12.99}, {"time": 1.0667}], "translate": [{"time": 0.4667}, {"time": 1.0667, "x": 7.49, "y": 2.88}], "scale": [{"time": 0.4667}, {"time": 0.6, "y": 0.7}, {"time": 1.0667}]}, "VFX6": {"rotate": [{"time": 0.1667}, {"time": 0.6, "value": 17.98}, {"time": 1, "value": 40.65}], "translate": [{"time": 0.1667, "x": 9.7, "y": 51.76}, {"time": 1, "x": 30.25, "y": 67.27}], "scale": [{"time": 0.1667}, {"time": 0.6, "x": 0.951, "y": 1.135}, {"time": 1, "x": 0.922, "y": 1.029}]}, "VFX4": {"rotate": [{"value": -39.43}], "translate": [{"x": -219.72, "y": -73.95}]}, "VFX8": {"rotate": [{"time": 0.7333}, {"time": 0.9, "value": -12.99}, {"time": 1.4}], "translate": [{"time": 0.7333, "x": 25.47, "y": -5.39}, {"time": 1.4, "x": 3.98, "y": 5.12}], "scale": [{"time": 0.7333}, {"time": 0.9, "y": 0.7}, {"time": 1.4}]}, "VFX9": {"scale": [{"x": 0.951, "y": 1.135}]}, "VFX7": {"rotate": [{"value": -21.51}], "translate": [{"x": -70.29, "y": 24.6}]}, "VFX": {"rotate": [{"time": 1.4333, "value": -180.22}], "translate": [{"time": 1.4333, "x": -163.49, "y": 100.41}], "scale": [{"time": 1.4333, "x": 0.446, "y": 0.446}]}, "glow6": {"scale": [{"time": 1.4667, "x": 1.84, "y": 1.84}]}, "glow": {"scale": [{"time": 1.4667, "x": 1.84, "y": 1.84}]}, "glow7": {"rotate": [{"time": 0.0667, "value": 14.58}], "translate": [{"time": 0.0667, "x": -52.34, "y": -43.5}], "scale": [{"time": 0.0667, "x": 25.935, "y": 25.51}]}, "bone": {"translate": [{"curve": [0.233, 0, 0.467, -11.25, 0.233, 0, 0.467, -3.44]}, {"time": 0.7, "x": -11.25, "y": -3.44, "curve": [1.022, -11.25, 1.344, 0, 1.022, -3.44, 1.344, 0]}, {"time": 1.6667}]}, "bone20": {"rotate": [{"value": -11.15, "curve": [0.122, -11.15, 0.244, 0]}, {"time": 0.3667, "curve": [0.522, 0, 0.678, -12.14]}, {"time": 0.8333, "value": -12.14, "curve": [0.967, -12.14, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -11.15]}, {"time": 1.6667, "value": -11.15}]}, "bone21": {"rotate": [{"value": -7.19, "curve": [0.068, -5.05, 0.178, 0]}, {"time": 0.2667, "curve": [0.4, 0, 0.533, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.811, -12.14, 0.956, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -12.14]}, {"time": 1.5333, "value": -12.14, "curve": [1.578, -12.14, 1.64, -8.2]}, {"time": 1.6667, "value": -7.19}]}, "bone19": {"rotate": [{"value": -6.31, "curve": [0.053, -8.88, 0.133, -12.14]}, {"time": 0.2, "value": -12.14, "curve": [0.344, -12.14, 0.489, 0]}, {"time": 0.6333, "curve": [0.767, 0, 0.9, -12.14]}, {"time": 1.0333, "value": -12.14, "curve": [1.178, -12.14, 1.322, 0]}, {"time": 1.4667, "curve": [1.533, 0, 1.612, -3.85]}, {"time": 1.6667, "value": -6.31}]}, "bone22": {"rotate": [{"value": -6.94, "curve": [0.072, -4.08, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.8, -12.14, 0.933, 0]}, {"time": 1.0667, "curve": [1.211, 0, 1.356, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.617, -9.24]}, {"time": 1.6667, "value": -6.94}]}, "bone23": {"rotate": [{"value": -6.44, "curve": [0.054, -3.25, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -12.14]}, {"time": 0.6667, "value": -12.14, "curve": [0.789, -12.14, 0.911, 0]}, {"time": 1.0333, "curve": [1.189, 0, 1.344, -12.14]}, {"time": 1.5, "value": -12.14, "curve": [1.556, -12.14, 1.637, -8.8]}, {"time": 1.6667, "value": -6.44}]}, "bone27": {"rotate": [{"value": -9.1, "curve": [0.048, -5.6, 0.111, 0]}, {"time": 0.1667, "curve": [0.311, 0, 0.456, -22.76]}, {"time": 0.6, "value": -22.76, "curve": [0.733, -22.76, 0.867, 0]}, {"time": 1, "curve": [1.144, 0, 1.289, -22.76]}, {"time": 1.4333, "value": -22.76, "curve": [1.511, -22.76, 1.62, -12.74]}, {"time": 1.6667, "value": -9.1}]}, "bone26": {"rotate": [{"value": -19.12, "curve": [0.069, -10.86, 0.244, 0]}, {"time": 0.3667, "curve": [0.5, 0, 0.633, -22.76]}, {"time": 0.7667, "value": -22.76, "curve": [0.911, -22.76, 1.056, 0]}, {"time": 1.2, "curve": [1.333, 0, 1.467, -22.76]}, {"time": 1.6, "value": -22.76, "curve": [1.622, -22.76, 1.649, -21.21]}, {"time": 1.6667, "value": -19.12}]}, "bone25": {"rotate": [{"value": -22.3, "curve": [0.133, -22.3, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -22.3]}, {"time": 0.8333, "value": -22.3, "curve": [0.967, -22.3, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -22.3]}, {"time": 1.6667, "value": -22.3}]}, "skirt_02": {"rotate": [{"value": -18.66, "curve": [0.311, -18.66, 0.622, 0]}, {"time": 0.9333, "curve": [1.178, 0, 1.422, -18.66]}, {"time": 1.6667, "value": -18.66}]}, "target": {"translate": [{"x": 2.44, "y": 0.47, "curve": [0.079, 1.04, 0.156, 0, 0.079, 0.2, 0.156, 0]}, {"time": 0.2333, "curve": [0.5, 0, 0.767, 13.45, 0.5, 0, 0.767, 2.59]}, {"time": 1.0333, "x": 13.45, "y": 2.59, "curve": [1.245, 13.45, 1.457, 6.3, 1.245, 2.59, 1.457, 1.21]}, {"time": 1.6667, "x": 2.44, "y": 0.47}]}, "hair_023": {"rotate": [{"value": -14.83, "curve": [0.203, -19.31, 0.4, -31.88]}, {"time": 0.6, "value": -31.88, "curve": [0.767, -31.88, 0.938, -26.93]}, {"time": 1.1, "value": -20.42, "curve": [1.289, -12.84, 1.45, -10.8]}, {"time": 1.6667, "value": -14.83}], "translate": [{"x": -6.54, "y": -3.02, "curve": [0.2, -6.54, 0.4, -14.42, 0.2, -3.02, 0.4, -3.13]}, {"time": 0.6, "x": -14.42, "y": -3.13, "curve": [0.778, -14.42, 0.963, -13.64, 0.778, -3.13, 0.956, -0.61]}, {"time": 1.1333, "x": -11.75, "y": -0.61, "curve": [1.301, -9.89, 1.489, -6.54, 1.311, -0.61, 1.489, -3.02]}, {"time": 1.6667, "x": -6.54, "y": -3.02}]}, "target3": {"translate": [{"x": 2.78, "y": 3.39, "curve": [0.101, 1.47, 0.199, 0.38, 0.101, 5.73, 0.199, 7.68]}, {"time": 0.3, "x": 0.38, "y": 7.68, "curve": [0.589, 0.38, 0.878, 7.68, 0.589, 7.68, 0.878, -5.38]}, {"time": 1.1667, "x": 7.68, "y": -5.38, "curve": [1.346, 7.68, 1.508, 4.91, 1.346, -5.38, 1.508, -0.42]}, {"time": 1.6667, "x": 2.78, "y": 3.39}]}, "bone10": {"rotate": [{"value": -1.32}, {"time": 0.4667}, {"time": 1.2, "value": -2.65}, {"time": 1.6667, "value": -1.32}], "translate": [{"x": -1.27, "y": -2.89}, {"time": 0.4667}, {"time": 1.2, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.27, "y": -2.89}]}, "bone29": {"rotate": [{"value": -1.23}, {"time": 0.4333}, {"time": 1.1667, "value": -2.65}, {"time": 1.6667, "value": -1.23}], "translate": [{"x": -1.18, "y": -2.69}, {"time": 0.4333}, {"time": 1.1667, "x": -2.53, "y": -5.78}, {"time": 1.6667, "x": -1.18, "y": -2.69}]}, "bone9": {"rotate": [{"value": -1.08}, {"time": 0.4333, "value": -2.65}, {"time": 1.3667}, {"time": 1.6667, "value": -1.08}], "translate": [{"x": -1.04, "y": -2.37}, {"time": 0.4333, "x": -2.53, "y": -5.78}, {"time": 1.3667}, {"time": 1.6667, "x": -1.04, "y": -2.37}]}, "bone16": {"rotate": [{"value": -6.51, "curve": [0.127, -3.56, 0.289, 0]}, {"time": 0.4333, "curve": [0.667, 0, 0.9, -14.51]}, {"time": 1.1333, "value": -14.51, "curve": [1.311, -14.51, 1.506, -10.34]}, {"time": 1.6667, "value": -6.51}]}, "bone18": {"rotate": [{"value": -10.51, "curve": [0.16, -6.55, 0.467, 0]}, {"time": 0.7, "curve": [0.933, 0, 1.167, -14.51]}, {"time": 1.4, "value": -14.51, "curve": [1.489, -14.51, 1.589, -11.92]}, {"time": 1.6667, "value": -10.51}]}, "bone17": {"rotate": [{"value": -11.01, "curve": [0.233, -7.37, 0.489, 0]}, {"time": 0.7333, "curve": [0.967, 0, 1.2, -14.51]}, {"time": 1.4333, "value": -14.51, "curve": [1.511, -14.51, 1.587, -12.61]}, {"time": 1.6667, "value": -11.01}]}, "bone12": {"rotate": [{"curve": [0.233, 0, 0.467, -14.51]}, {"time": 0.7, "value": -14.51, "curve": [1.022, -14.51, 1.344, 0]}, {"time": 1.6667}]}, "bone13": {"rotate": [{"value": -4.5, "curve": [0.094, -1.9, 0.2, 0]}, {"time": 0.3, "curve": [0.533, 0, 0.767, -14.51]}, {"time": 1, "value": -14.51, "curve": [1.222, -14.51, 1.444, -11.35]}, {"time": 1.6667, "value": -4.5}]}, "bone14": {"rotate": [{"value": -8.01, "curve": [0.186, -2.46, 0.356, 0]}, {"time": 0.5333, "curve": [0.767, 0, 1, -14.51]}, {"time": 1.2333, "value": -14.51, "curve": [1.378, -14.51, 1.512, -13.22]}, {"time": 1.6667, "value": -8.01}]}, "hair_05": {"rotate": [{"curve": [0.14, 0, 0.26, -5.67]}, {"time": 0.4, "value": -5.67, "curve": [0.54, -5.67, 0.719, 0]}, {"time": 0.8667, "curve": [1.007, 0, 1.127, -5.67]}, {"time": 1.2667, "value": -5.67, "curve": [1.407, -5.67, 1.532, 0]}, {"time": 1.6667}]}, "hair_06": {"rotate": [{"value": -3.94, "curve": [0.066, -1.64, 0.144, 0]}, {"time": 0.2, "curve": [0.34, 0, 0.46, -9.86]}, {"time": 0.6, "value": -9.86, "curve": [0.684, -9.86, 0.815, -5.63]}, {"time": 0.8667, "value": -3.94, "curve": [0.919, -2.25, 0.944, 0]}, {"time": 1, "curve": [1.14, 0, 1.26, -9.86]}, {"time": 1.4, "value": -9.86, "curve": [1.484, -9.86, 1.596, -6.37]}, {"time": 1.6667, "value": -3.94}]}, "hair_07": {"rotate": [{"value": -9.07, "curve": [0.129, -9.07, 0.271, 0]}, {"time": 0.4, "curve": [0.54, 0, 0.66, -9.79]}, {"time": 0.8, "value": -9.86, "curve": [0.973, -9.94, 1.071, 0]}, {"time": 1.2, "curve": [1.34, 0, 1.51, -9.07]}, {"time": 1.6667, "value": -9.07}]}, "hair_010": {"rotate": [{"value": -6.83}, {"time": 0.3, "curve": [0.444, -3.04, 0.589, -9.11]}, {"time": 0.7333, "value": -9.11, "curve": [0.867, -9.11, 1, 0]}, {"time": 1.1333, "curve": [1.278, 0, 1.422, -9.11]}, {"time": 1.5667, "value": -9.11, "curve": [1.6, -9.11, 1.633, -6.83]}, {"time": 1.6667, "value": -6.83}]}, "hair_011": {"rotate": [{"value": -9.11, "curve": [0.1, -9.11, 0.2, -4.46]}, {"time": 0.3, "value": -2.19, "curve": [0.333, -1.43, 0.367, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -9.11]}, {"time": 0.8333, "value": -9.11, "curve": [0.933, -9.11, 1.033, -4.46]}, {"time": 1.1333, "value": -2.19, "curve": [1.167, -1.43, 1.2, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -9.11]}, {"time": 1.6667, "value": -9.11}]}, "hair_012": {"rotate": [{"value": 1.66}, {"time": 0.1, "value": 2.16, "curve": [0.167, 1.79, 0.233, 1.4]}, {"time": 0.3, "value": 1.04, "curve": [0.367, 0.68, 0.433, 0]}, {"time": 0.5, "curve": [0.644, 0, 0.789, 2.16]}, {"time": 0.9333, "value": 2.16, "curve": [1, 2.16, 1.067, 1.4]}, {"time": 1.1333, "value": 1.04, "curve": [1.2, 0.68, 1.267, 0]}, {"time": 1.3333, "curve": [1.444, 0, 1.556, 1.66]}, {"time": 1.6667, "value": 1.66}]}, "hair_013": {"rotate": [{"value": 10.45}, {"time": 0.2, "value": 19.4, "curve": [0.233, 17.59, 0.267, 15.59]}, {"time": 0.3, "value": 13.97, "curve": [0.4, 9.12, 0.5, 0]}, {"time": 0.6, "curve": [0.744, 0, 0.889, 19.4]}, {"time": 1.0333, "value": 19.4, "curve": [1.067, 19.4, 1.1, 15.59]}, {"time": 1.1333, "value": 13.97, "curve": [1.233, 9.12, 1.333, 0]}, {"time": 1.4333, "curve": [1.511, 0, 1.589, 10.45]}, {"time": 1.6667, "value": 10.45}]}, "hair_014": {"rotate": [{"value": -3.89, "curve": [0.056, -3.89, 0.111, 0]}, {"time": 0.1667, "curve": [0.3, 0, 0.433, -9.71]}, {"time": 0.5667, "value": -9.71, "curve": [0.656, -9.71, 0.744, -5.88]}, {"time": 0.8333, "value": -3.89, "curve": [0.889, -2.64, 0.944, 0]}, {"time": 1, "curve": [1.133, 0, 1.267, -9.71]}, {"time": 1.4, "value": -9.71, "curve": [1.489, -9.71, 1.578, -3.89]}, {"time": 1.6667, "value": -3.89}]}, "hair_015": {"rotate": [{"value": -6.99, "curve": [0.1, -6.99, 0.2, 0]}, {"time": 0.3, "curve": [0.433, 0, 0.567, -9.71]}, {"time": 0.7, "value": -9.71, "curve": [0.744, -9.71, 0.789, -7.99]}, {"time": 0.8333, "value": -6.99, "curve": [0.933, -4.75, 1.033, 0]}, {"time": 1.1333, "curve": [1.267, 0, 1.4, -9.71]}, {"time": 1.5333, "value": -9.71, "curve": [1.578, -9.71, 1.622, -6.99]}, {"time": 1.6667, "value": -6.99}]}, "hair_016": {"rotate": [{"curve": [0.144, 0, 0.289, -9.71]}, {"time": 0.4333, "value": -9.71, "curve": [0.567, -9.71, 0.7, 0]}, {"time": 0.8333, "curve": [0.978, 0, 1.122, -9.71]}, {"time": 1.2667, "value": -9.71, "curve": [1.4, -9.71, 1.533, 0]}, {"time": 1.6667}]}, "hair_02": {"rotate": [{"value": -1.49}, {"time": 0.1667}, {"time": 0.6333, "value": -3.73}, {"time": 0.8333, "value": -1.49}, {"time": 1}, {"time": 1.4667, "value": -3.73}, {"time": 1.6667, "value": -1.49}]}, "hair_03": {"rotate": [{"value": -2.84}, {"time": 0.3333}, {"time": 0.7667, "value": -3.73}, {"time": 0.8333, "value": -2.84}, {"time": 1.1667}, {"time": 1.6, "value": -3.73}, {"time": 1.6667, "value": -2.84}]}, "hair_018": {"rotate": [{"curve": [0.133, 0, 0.267, -6.59]}, {"time": 0.4, "value": -6.59, "curve": [0.544, -6.59, 0.689, 0]}, {"time": 0.8333, "curve": [0.967, 0, 1.1, -6.59]}, {"time": 1.2333, "value": -6.59, "curve": [1.378, -6.59, 1.522, 0]}, {"time": 1.6667}]}, "hair_019": {"rotate": [{"value": -2.11, "curve": [0.044, -2.11, 0.089, 0]}, {"time": 0.1333, "curve": [0.267, 0, 0.4, -6.59]}, {"time": 0.5333, "value": -6.59, "curve": [0.633, -6.59, 0.733, -3.63]}, {"time": 0.8333, "value": -2.11, "curve": [0.878, -1.43, 0.922, 0]}, {"time": 0.9667, "curve": [1.1, 0, 1.233, -6.59]}, {"time": 1.3667, "value": -6.59, "curve": [1.467, -6.59, 1.567, -2.11]}, {"time": 1.6667, "value": -2.11}]}, "hair_020": {"rotate": [{"value": -4.48, "curve": [0.089, -4.48, 0.178, 0]}, {"time": 0.2667, "curve": [0.411, 0, 0.556, -6.59]}, {"time": 0.7, "value": -6.59, "curve": [0.744, -6.59, 0.789, -5.21]}, {"time": 0.8333, "value": -4.48, "curve": [0.922, -3.02, 1.011, 0]}, {"time": 1.1, "curve": [1.244, 0, 1.389, -6.59]}, {"time": 1.5333, "value": -6.59, "curve": [1.578, -6.59, 1.622, -4.48]}, {"time": 1.6667, "value": -4.48}]}, "hair_024": {"rotate": [{"value": -6.59, "curve": [0.133, -6.59, 0.267, 0]}, {"time": 0.4, "curve": [0.544, 0, 0.689, -6.59]}, {"time": 0.8333, "value": -6.59, "curve": [0.967, -6.59, 1.1, 0]}, {"time": 1.2333, "curve": [1.378, 0, 1.522, -6.59]}, {"time": 1.6667, "value": -6.59}]}, "hair_04": {"rotate": [{"curve": [0.089, 1.26, 0.178, 5.76]}, {"time": 0.2667, "value": 5.76, "curve": [0.367, 5.76, 0.467, -2.67]}, {"time": 0.5667, "value": -2.67, "curve": [0.656, -2.67, 0.744, -1.41]}, {"time": 0.8333, "curve": [0.922, 1.41, 1.011, 5.76]}, {"time": 1.1, "value": 5.76, "curve": [1.2, 5.76, 1.3, -2.67]}, {"time": 1.4, "value": -2.67, "curve": [1.489, -2.67, 1.591, -1.11]}, {"time": 1.6667}]}, "hair_08": {"rotate": [{"value": -1, "curve": [0.043, -0.26, 0.067, 0.13]}, {"time": 0.1, "value": 0.74, "curve": [0.189, 2.38, 0.278, 5.76]}, {"time": 0.3667, "value": 5.76, "curve": [0.467, 5.76, 0.567, -2.67]}, {"time": 0.6667, "value": -2.67, "curve": [0.722, -2.67, 0.778, -2.04]}, {"time": 0.8333, "value": -1.48, "curve": [0.867, -1.15, 0.9, -0.61]}, {"time": 0.9333, "curve": [1.022, 1.64, 1.111, 5.76]}, {"time": 1.2, "value": 5.76, "curve": [1.3, 5.76, 1.4, -2.67]}, {"time": 1.5, "value": -2.67, "curve": [1.556, -2.67, 1.609, -1.78]}, {"time": 1.6667, "value": -1}]}, "hair_09": {"rotate": [{"value": -2.51, "curve": [0.089, -2.51, 0.178, -1.47]}, {"time": 0.2667, "curve": [0.344, 1.29, 0.422, 5.76]}, {"time": 0.5, "value": 5.76, "curve": [0.611, 5.76, 0.722, -2.51]}, {"time": 0.8333, "value": -2.51, "curve": [0.922, -2.51, 1.019, -1.89]}, {"time": 1.1, "value": -0.25, "curve": [1.178, 1.32, 1.256, 5.76]}, {"time": 1.3333, "value": 5.76, "curve": [1.444, 5.76, 1.556, -2.51]}, {"time": 1.6667, "value": -2.51}]}, "hair_026": {"rotate": [{"value": -0.45, "curve": [0.037, -1.2, 0.067, -2.67]}, {"time": 0.1, "value": -2.67, "curve": [0.189, -2.67, 0.278, -1.5]}, {"time": 0.3667, "curve": [0.444, 1.31, 0.522, 5.76]}, {"time": 0.6, "value": 5.76, "curve": [0.678, 5.76, 0.778, 1.48]}, {"time": 0.8333, "value": -0.75, "curve": [0.863, -1.93, 0.9, -2.67]}, {"time": 0.9333, "value": -2.67, "curve": [1.022, -2.67, 1.128, -1.8]}, {"time": 1.2, "curve": [1.27, 1.76, 1.356, 5.76]}, {"time": 1.4333, "value": 5.76, "curve": [1.511, 5.76, 1.639, 1.26]}, {"time": 1.6667, "value": -0.45}]}, "hair_027": {"rotate": [{"value": 0.43, "curve": [0.031, -0.42, 0.089, -2.67]}, {"time": 0.1333, "value": -2.67, "curve": [0.222, -2.67, 0.311, -1.5]}, {"time": 0.4, "curve": [0.478, 1.31, 0.556, 5.76]}, {"time": 0.6333, "value": 5.76, "curve": [0.7, 5.76, 0.784, 2.33]}, {"time": 0.8333, "value": 0.43, "curve": [0.868, -0.91, 0.922, -2.67]}, {"time": 0.9667, "value": -2.67, "curve": [1.056, -2.67, 1.153, -1.67]}, {"time": 1.2333, "curve": [1.309, 1.57, 1.389, 5.76]}, {"time": 1.4667, "value": 5.76, "curve": [1.533, 5.76, 1.629, 2.33]}, {"time": 1.6667, "value": 0.43}]}, "hair_028": {"rotate": [{"value": 2.65, "curve": [0.067, 2.65, 0.133, -2.67]}, {"time": 0.2, "value": -2.67, "curve": [0.289, -2.67, 0.378, -1.41]}, {"time": 0.4667, "curve": [0.556, 1.41, 0.644, 5.76]}, {"time": 0.7333, "value": 5.76, "curve": [0.767, 5.76, 0.8, 3.59]}, {"time": 0.8333, "value": 2.65, "curve": [0.9, 0.78, 0.967, -2.67]}, {"time": 1.0333, "value": -2.67, "curve": [1.122, -2.67, 1.211, -1.41]}, {"time": 1.3, "curve": [1.389, 1.41, 1.478, 5.76]}, {"time": 1.5667, "value": 5.76, "curve": [1.6, 5.76, 1.633, 2.65]}, {"time": 1.6667, "value": 2.65}]}, "head3": {"rotate": [{}, {"time": 0.8333, "value": 12.84}, {"time": 1.6667}]}, "head4": {"rotate": [{"value": 3.08}, {"time": 0.2}, {"time": 1.0333, "value": 12.84}, {"time": 1.6667, "value": 3.08}]}, "head": {"rotate": [{"curve": [0.121, -1.17, 0.289, -2.98]}, {"time": 0.4333, "value": -2.98, "curve": [0.722, -2.98, 1.012, 2.16]}, {"time": 1.3, "value": 2.32, "curve": [1.49, 2.42, 1.573, 1.06]}, {"time": 1.6667}]}, "vfx": {"translate": [{"curve": [0.233, 0, 0.467, 3.62, 0.233, 0, 0.467, 8.86]}, {"time": 0.7, "x": 3.62, "y": 8.86, "curve": [1.022, 3.62, 1.344, 0, 1.022, 8.86, 1.344, 0]}, {"time": 1.6667}]}, "shadow": {"translate": [{"curve": [0.289, 0, 1.144, 20.53, 0.289, 0, 1.144, -5.13]}, {"time": 1.4333, "x": 20.53, "y": -5.13}], "scale": [{"curve": [0.178, 1, -0.522, 1.11, 0.178, 1, -0.522, 1.11]}, {"time": 1.4333, "x": 1.11, "y": 1.11}]}}}}}