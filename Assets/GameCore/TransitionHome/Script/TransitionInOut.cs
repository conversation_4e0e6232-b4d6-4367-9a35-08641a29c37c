using System;
using System.Collections;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class TransitionInOut : Singleton<TransitionInOut> {

    #region Fields

    public ParticleSystem TransitionOutVFX;
    public ParticleSystem TransitionInVFX;

    public static bool isReady => RemoteConfig.instance.UITransition_IsTransitionHomeAndGameplay;

    [Space] [Header("Block UI")] 
    public Image blockUI;

    private WaitForSeconds _wait;
    #endregion

    protected override void Awake() {
        base.Awake(); 
        _wait = YieldPool.GetWaitForSeconds(1f);
    }

    #region Methods

    public void TransitionOut(Vector3 position, Action callback = null) {
        if (isReady) {
            if (TransitionOutVFX) {
                TransitionOutVFX.transform.position = position;
                TransitionOutVFX.Play();
            }

            SoundManager.PlaySFX_PopupOpen();
            StartCoroutine(IEDisableUIInteractive(callback));
        }
        else {
            callback?.Invoke();
        }
    }

    public void TransitionIn(Vector3 position) {
        if (isReady) {
            if (TransitionInVFX) {
                TransitionInVFX.transform.position = position;
                TransitionInVFX.Play();
            }
            SoundManager.PlaySFX_PopupClose();   
        }
    }

    public void StopTransitionOut() {
        if (TransitionOutVFX) {
            TransitionOutVFX.Stop();
        }
    }

    IEnumerator IEDisableUIInteractive(Action callback = null) {
        if (blockUI) {
            blockUI.enabled = true;
            yield return _wait;
            blockUI.enabled = false;
            callback?.Invoke();
        }
        else {
            yield return _wait;
            callback?.Invoke();
        }
    }
    
    #endregion
    
    public static void Active() {
        if (isInstanced) {
            return;
        }
        
        var resource = Resources.Load<GameObject>("Transition VFX");
        if (resource != null) {
            Instantiate(resource);
        }
    }
}
