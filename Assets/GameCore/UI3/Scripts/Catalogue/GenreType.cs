public static class GenreType { //230112
    public const string ACOUSTIC   = "ACOUSTIC";
    public const string CHILL      = "CHILL";
    public const string INDIE      = "INDIE";
    public const string BALLAD     = "BALLAD";
    public const string BLUES      = "BLUES";
    public const string C_POP      = "C-POP";
    public const string CLASSICAL  = "CLASSICAL";
    public const string COUNTRY    = "COUNTRY";
    public const string EDM        = "EDM";
    public const string HOUSE      = "HOUSE";
    public const string DANCE      = "DANCE";
    public const string ELECTRONIC = "ELECTRONIC";
    public const string HIPHOP     = "HIPHOP";
    public const string J_POP      = "J-POP";
    public const string JAZZ       = "JAZZ";
    public const string K_POP      = "K-POP";
    public const string LATIN      = "LATIN";
    public const string POP        = "POP";
    public const string RNB        = "RNB";
    public const string SOUL       = "SOUL";
    public const string ROCK       = "ROCK";
    public const string FOLK       = "FOLK";
    public const string OTHERS     = "OTHERS";
    public const  string POPULAR    = "POPULAR";
    public const string XMAS       = "XMAS";

    public static string GetLocalizedValueOfGenre(string genreName) {
        string keyGenre = "GENRE_" + genreName.ToUpper();
        keyGenre = keyGenre.Replace(' ', '_').Replace('&', 'N') //
            .Replace("CHILL-OUT", "CHILL") //
            .Replace("HIP-HOP_RAP", "HIP-HOP-RAP");
        
        string localizedValue;
        if (LocalizationManager.instance.ContainsKey(keyGenre)) {
            localizedValue = LocalizationManager.instance.GetLocalizedValue(keyGenre);
        } else {
            localizedValue = genreName.Replace("_", " ");
            CustomException.Fire("[Localization]", "Cannot get localized value => " + keyGenre);
        }

        return localizedValue;
    }
}