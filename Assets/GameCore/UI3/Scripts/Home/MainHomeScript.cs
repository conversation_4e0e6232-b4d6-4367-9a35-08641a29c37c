using System;
using UnityEngine;

namespace UI3 {
    public class MainHomeScript : TabScript {
        #region Fields

       //public TabAllScript tabAllScript;

        #endregion

        #region Unity Method

        private void OnEnable() {
            //if (GroundMusic.instance && GroundMusic.instance.IsCanResume()) {
            //    GroundMusic.instance.ResumeMusic();
            //}
        
            PlayerPrefs.SetString(PlayerPrefsKey.LastTabHome,"HOME");
        }
    
        #endregion

        /// <summary>
        /// ScrollToTop
        /// </summary>
        /// <param name="onDone"></param>
        public void ScrollToTop(Action onDone) {
            //tabAllScript.ScrollToTop(onDone);
        }

        public void PressBackKey() {
            Util.ToggleExitPopup();
        }
    }
}