using System.Runtime.CompilerServices;
using UnityEngine;

/// <summary>
/// Singleton MonoBehaviour
/// Update:2020-04-21 by hungtx
/// Update:2023-02-10 by trungvt
/// </summary>
/// <typeparam name="T"></typeparam>
public abstract class Singleton<T> : MonoBehaviour where T : Singleton<T> {
    //static
    private static T    s_instance;
    public static  bool isInstanced; //checking bool is faster than checking null

    //public
    public bool isDontDestroy;

    /// <summary>
    /// The static reference to the instance
    /// </summary>
    public static T instance {
        get {
            if (isInstanced) {
                return s_instance;
            }

            if (s_instance != null) {
                isInstanced = true;
            }

            return s_instance;
        }
    }

    #region Unity method

    protected virtual void Awake() {
        // Make instance in Awake to make reference performance uniformly.
        if (s_instance == null) {
            s_instance = (T) this;
            isInstanced = true;
        }
        // If there is an instance already in the same scene, destroy this script.
        else if (s_instance != this) {
            Debug.LogWarning("Singleton " + typeof(T) + " is already exists.");
            Destroy(gameObject);
        }

        if (isDontDestroy) {
            SetDontDestroyOnLoad();
        }
    }

    protected virtual void OnDestroy() {
        if (s_instance == this) {
            s_instance = null;
            isInstanced = false;
        }
    }

    #endregion

    /// <summary>
    /// IsSingleton
    /// </summary>
    /// <returns></returns>
    protected bool IsSingleton() {
        return s_instance == this;
    }

    private void SetDontDestroyOnLoad() {
        if (IsRootObject()) {
            DontDestroyOnLoad(gameObject);
        } else {
            Logger.CanLog(() => Debug.LogWarning("Cannot set DontDestroyOnLoad on non root gameobject: \n" +
                                                 " name => " + gameObject.name + " ~ type => " + typeof(T)));
        }
    }

    private bool IsRootObject() {
        if (transform == transform.root) {
            return true;
        }

        return false;
    }
}