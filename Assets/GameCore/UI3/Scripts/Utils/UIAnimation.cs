using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class UIAnimation : MonoBehaviour {
    #region Fields

    //public
    [FormerlySerializedAs("isAutoPlayAnimation")]
    [Header("Config for static UI")]
    [SerializeField]
    private bool isStaticUI = true;

    [SerializeField]
    private float timeDelayStartUIAnimation = 0;

    [Header("Animation for list UI")]
    [SerializeField]
    private UIAnimConfig parentUIList;

    [Header("Animation for individually UIs")]
    [SerializeField]
    private List<UIAnimConfig> animationUIs;

    //private
    private LayoutGroup layoutGroup;
    RectTransform rectCanvas;
    float widthCanvas;
    float heightCanvas;
    private bool isInitComplete;

    private float heightScreen;

    #endregion

    #region Unity Method

    public void Start() {
        if (!RemoteConfig.instance.isEnableAnimationUI3) {
            return;
        }

        if (isStaticUI) {
            StartCoroutine(DoStartUIAnimation(true));
        }
    }

    private void OnEnable() {
        if (isStaticUI) {
            StartCoroutine(DoStartUIAnimation(false));
        }
    }

    #endregion

    #region Method

    private void InitUIAnimation() {
        if (rectCanvas == null) { //Must Call From Start !!
            Canvas canvas = GetComponentInParent<Canvas>();
            rectCanvas = canvas.GetComponent<RectTransform>();
            Vector2 sizeDelta = rectCanvas.sizeDelta;
            widthCanvas = sizeDelta.x;
            heightCanvas = sizeDelta.y;
        }

        if (parentUIList != null && parentUIList.rectTransform != null && parentUIList.rectTransform.childCount > 0) {
            animationUIs = new List<UIAnimConfig>();

            List<Transform> listTransforms = parentUIList.rectTransform.GetChilds();
            for (int index = 0; index < listTransforms.Count; index++) {
                UIAnimConfig uIAnim = new UIAnimConfig {
                    rectTransform = listTransforms[index] as RectTransform,
                    directionStart = parentUIList.directionStart,
                    easeType = parentUIList.easeType,
                    time = parentUIList.time,
                    timeDelay = parentUIList.timeDelay * index
                };
                animationUIs.Add(uIAnim);
            }

            layoutGroup = parentUIList.rectTransform.GetComponent<LayoutGroup>();
            if (layoutGroup) {
                LayoutRebuilder.ForceRebuildLayoutImmediate(parentUIList.rectTransform);
            }
        }

        isInitComplete = true;
    }

    public IEnumerator DoStartUIAnimation(bool isInit) {
        if (!isStaticUI) { //Dynamic list
            yield return null; //Importance
        }

        if (!RemoteConfig.instance.isEnableAnimationUI3) {
            yield break;
        }

        if (isInit) {
            InitUIAnimation();
        }

        if (!isInitComplete) {
            yield break;
        }

        if (animationUIs != null && animationUIs.Count != 0) {
            SceneFader.instance.BlockUserInput(true);
            if (layoutGroup) {
                layoutGroup.enabled = false;
            }

            UpdateStartPosition();
            StartCoroutine(StartUIAnimation());
        }
    }

    private void UpdateStartPosition() {
        foreach (UIAnimConfig uIElement in animationUIs) {
            UpdateStartPosition(uIElement);
        }
    }

    private void UpdateStartPosition(UIAnimConfig uIElement) {
        if (!uIElement.rectTransform.gameObject.activeSelf) {
            return;
        }

        uIElement.localPosition = uIElement.rectTransform.localPosition;

        float heightScr = GetHeightScreen();
        if (-heightScr < uIElement.rectTransform.position.y && uIElement.rectTransform.position.y < heightScr) {
            RectTransform rectTransform = uIElement.rectTransform;
            switch (uIElement.directionStart) {
                case Direction.None:
                    break;
                case Direction.Left:
                    rectTransform.SetLocalX(uIElement.localPosition.x - widthCanvas);
                    break;
                case Direction.Right:
                    rectTransform.SetLocalX(uIElement.localPosition.x + widthCanvas);
                    break;
                case Direction.Top:
                    rectTransform.SetLocalY(uIElement.localPosition.y + heightCanvas);
                    break;
                case Direction.Bottom:
                    rectTransform.SetLocalY(uIElement.localPosition.y - heightCanvas);
                    break;
                default:
                    Debug.LogError("[MoveOutsideScreen] TODO ...");
                    break;
            }
        }
    }

    private float GetHeightScreen() {
        if (heightScreen <= 0) {
            heightScreen = ScreenUtils.GetHeight();
        }

        return heightScreen;
    }

    private IEnumerator StartUIAnimation() {
        yield return new WaitForSeconds(timeDelayStartUIAnimation);
        int index = 0;
        for (index = 0; index < animationUIs.Count; index++) {
            UIAnimConfig animConfig = animationUIs[index];
            if (animConfig.rectTransform.gameObject.activeSelf) {
                if (animConfig.localPosition != animConfig.rectTransform.localPosition) {
                    animConfig.rectTransform.DOLocalMove(animConfig.localPosition, animConfig.time)
                        .SetDelay(animConfig.timeDelay);
                } else {
                    if (index > 0) {
                        break;
                    }
                }
            }
        }

        int indexEnd = Mathf.Clamp(index - 1, 0, animationUIs.Count - 1);
        UIAnimConfig animConfigEnd = animationUIs[indexEnd];
        float timeAnimationDone = animConfigEnd.timeDelay + animConfigEnd.time;
        yield return new WaitForSeconds(timeAnimationDone);

        if (layoutGroup) {
            layoutGroup.enabled = true;
        }

        SceneFader.instance.BlockUserInput(false);
    }

    #endregion

    public void SetParentUIList(RectTransform rectTransform) {
        parentUIList.rectTransform = rectTransform;
    }
}