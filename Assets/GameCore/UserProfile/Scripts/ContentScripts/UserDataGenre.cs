using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.U2D;
using UnityEngine.UI;
using System;
using System.Diagnostics;
using System.Text;
using Inwave;
using TileHop.Cores.Pooling;
using Debug = UnityEngine.Debug;

public class UserDataGenre : MonoBehaviour {
	[SerializeField] private StandardScrollerAdapter scrollerAdapter;
	[SerializeField] private OptimizedCellView prefCellView;
	[SerializeField] private SpriteAtlas atlasGenres;
	[SerializeField] private Button btnSeeAll;

	private List<string> _listGenres = new List<string>();
	private List<IData> _datas;
	
	private void Awake() {
		scrollerAdapter.Init(prefCellView.transform as RectTransform);
		btnSeeAll.onClick.AddListener(BtnSeeAllOnClick);
	}

	private void OnEnable() {
		scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
	}

	private void OnDisable() {
		scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
	}

	private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
		if (_datas.Count <= item.ItemIndex) return;
		if (item.cellView is UserDataGenreItem view) {
			string genre = _listGenres[item.ItemIndex];
			view.SetImage(GetSpriteByName(genre));
			view.SetTitle(genre);
			view.isPreviewing = true;
			view.SetSelected(true);

			//view.ordering = item.ItemIndex + 1;

			view.onButtonClick = () => { SoundManager.PlayGameButton(); };
		}
	}

	private IEnumerator SetData() {
		while (!scrollerAdapter.IsInitialized) {
			yield return YieldPool.GetWaitForEndOfFrame();
		}

		yield return YieldPool.GetWaitForEndOfFrame();
		scrollerAdapter.SetItems(_datas);
	}
	
	public void LoadGenres(List<string> favoriteGenres) {
		_listGenres.Clear();

		//load genres
		if (!favoriteGenres.IsNullOrEmpty()) {
			if (favoriteGenres.Count > 0) {
				foreach (string genre in favoriteGenres.Where(genre =>
					         !string.IsNullOrEmpty(genre) && !_listGenres.Contains(genre))) {
					_listGenres.Add(genre);
				}
			}
		}

		_datas = new List<IData>();
		//int column = (Mathf.Min(10, _listGenres.Count) + 1); // chỉ lấy tối đa 10
		for (int i = 0; i < _listGenres.Count; i++) {
			_datas.Add(new HeaderData());
		}

		Configuration.instance.StartCoroutine(SetData());
	}

	private Sprite GetSpriteByName(string genre) {
		Sprite sprite = atlasGenres.GetSprite(genre);

		if (sprite == null) {
			sprite = atlasGenres.GetSprite(GenreType.OTHERS);
			Logger.LogWarning(
				$"[GetSpriteByName] Cannot get sprite {genre.ToColor(Color.green)} in atlas {atlasGenres.name}");
		}

		return sprite;
	}

	private void BtnSeeAllOnClick() {
		SoundManager.PlayGameButton();
		UserProfilePage.instance.ShowAllGenres();
	}
}