using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;
using System;
using TileHop.Cores.Pooling;

public class UserDataRecentSong : MonoBehaviour {
    #region Fields

    
    [SerializeField] private StandardScrollerAdapter scrollerAdapter;
    [SerializeField] private OptimizedCellView       prefCellView;
    [SerializeField] private Button                  btnSeeAll;

    [Space]
    [SerializeField] private GameObject objNoneOfSong;

    [SerializeField] private Button btnPlayNow;

        private List<string> _listRecentSongsPath = new List<string>();
    private List<Song>   _listRecentSongs     = new List<Song>();
    private List<IData>  _datas;
    
    public List<Song> ListRecentSongs => _listRecentSongs;
    
    #endregion

    #region Unity Method

    protected void Awake() {
        scrollerAdapter.Init(prefCellView.transform as RectTransform);
    }

    private void Start() {
        btnSeeAll.onClick.AddListener(BtnSeeAllOnClick);
        btnPlayNow.onClick.AddListener(BtnPlayNowOnClick);
    }

    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }

    #endregion

    #region OptimizedScroller Handlers

    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count < item.ItemIndex)
            return;

        if (item.cellView is SongItemSmall view) {
            Song song = (Song) _datas[item.ItemIndex];
            view.ordering = item.ItemIndex + 1;
            view.SetSong(song,LOCATION_NAME.user_profile.ToString() );
            view.song_play_type = SONG_PLAY_TYPE.discover_recent_songs;
        }
    }

    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        scrollerAdapter.SetItems(_datas);
    }

    #endregion

    private void BtnSeeAllOnClick() {
        SoundManager.PlayGameButton();
        UserProfilePage.instance.ShowAllRecentSongs();
    }
    

    private void BtnPlayNowOnClick() {
        SoundManager.PlayGameButton();
        Song songToPlay = SongManager.instance.GetSongToDoMission();
        Util.GoToGamePlay(songToPlay, location: LOCATION_NAME.user_profile.ToString(), isSongClick: false);
    }

    private List<string> GetSongRecent(int total, Dictionary<string, Song> songList) {
        List<string> clampedSongs = new List<string>();
        
        List<string> recentSongs = CoreData.GetPlayerData().GetRecentSongs();
        if (recentSongs == null) {
            return clampedSongs;
        }

        _listRecentSongs.Clear();
        foreach (string recentSong in recentSongs) {
            if (songList.ContainsKey(recentSong)) {
                _listRecentSongs.Add(songList[recentSong]);
            }else if (SongManager.instance.userLocalSongs.ContainsKey(recentSong)) {
                _listRecentSongs.Add(SongManager.instance.userLocalSongs[recentSong]);
            }   
            
            clampedSongs.Add(recentSong);
            if (clampedSongs.Count >= total) {
                break;
            }
        }
        return clampedSongs;
    }

    public void ShowRecentSongs() {
        _listRecentSongsPath.Clear();
        Dictionary<string, Song> songList = SongManager.instance.GetByTag("ALL"); //Get all song list

        //get data recent songs
        if (songList != null && songList.Count > 0) {
            List<string> recentSongs = GetSongRecent(10, songList); //MY RECENT
            if (recentSongs.Count > 0) {
                _listRecentSongsPath.AddRange(recentSongs);
            }
        }

        if (_listRecentSongs.Count == 0) {
            objNoneOfSong.SetActive(true);
            btnSeeAll.gameObject.SetActive(false);
        } else {
            objNoneOfSong.SetActive(false);
            _datas = new List<IData>();
            _datas.AddRange(_listRecentSongs.Take(Mathf.Min(9, _listRecentSongs.Count)));
            Configuration.instance.StartCoroutine(SetData());
            btnSeeAll.gameObject.SetActive(_listRecentSongsPath.Count >= 4);
        }
    }
}