using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class BoardItemV2 : BoardItem {
    [SerializeField] private Image rankImage;
    [SerializeField] private Image border;
    private BoardManagerV2 _boardManagerV2 => BoardManager.instance as BoardManagerV2;

    public override void UpdateVisualRank(UserEntry entry, string boardId) {
        UpdateRankInTop(true);
        
        switch (entry.GetMyRank(boardId)) {
            case 1:
                rankBg.sprite = boardId == CONFIG_STRING.DBKey_Total
                    ? BoardManager.instance.total_rank1
                    : BoardManager.instance.song_rank1;
                break;
            case 2:
                rankBg.sprite = boardId == CONFIG_STRING.DBKey_Total
                    ? BoardManager.instance.total_rank2
                    : BoardManager.instance.song_rank2;
                break;
            case 3:
                rankBg.sprite = boardId == CONFIG_STRING.DBKey_Total
                    ? BoardManager.instance.total_rank3
                    : BoardManager.instance.song_rank3;
                break;
            default:
                rankBg.sprite = BoardManager.instance.rankBgDefault;
                rankText.text = GetRankText(entry.GetMyRank(boardId));
                UpdateRankInTop(false);
                break;
        }
    }

    public void UpdateRankInTop(bool isInTop) {
        rankImage.gameObject.SetActive(isInTop);
        rankText.gameObject.SetActive(!isInTop);
    }

    protected override void SetupItemVisual(UserEntry entry) {
        if (CoreUser.instance.user != null && entry.auId == CoreUser.instance.user.auId) {
            SetUpMeItemVisual();
        }
        else {
            border.rectTransform.sizeDelta = _boardManagerV2.sizeBorderDefault;
            border.sprite = _boardManagerV2.borderDefault;
            bg.sprite = _boardManagerV2.bgDefault;
            nameText.color = _boardManagerV2.ColorDefault;
            scoreText.color = _boardManagerV2.ColorDefaultScore;
        }
    }

    public void SetUpMeItemVisual() {
        border.rectTransform.sizeDelta = _boardManagerV2.sizeBorderMe;
        border.sprite = _boardManagerV2.borderMe;
        bg.sprite = _boardManagerV2.bgMe;
        nameText.color = _boardManagerV2.ColorMe;
        scoreText.color = _boardManagerV2.ColorMe;
    }

    public bool IsMe() {
        return CoreUser.instance.user != null && _entry.auId == CoreUser.instance.user.auId;
    }
}