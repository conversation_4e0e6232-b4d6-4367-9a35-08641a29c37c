using UnityEngine;
using UnityEngine.UI;

public class ItemSongPreview : OptimizedCellView {
    // public                   Song  song;
    [SerializeField] private Image  imgIconSong;

    private static Sprite _questionMarkIcon;
    private        string _artistName;

    public void SetArtist(string artistName) {
        _artistName = artistName;
        SetSprite();
    }

    private async void SetSprite() {
        var (sprite, status) = await SongCards.instance.GetIconArtistWithStatus(_artistName);

        if (imgIconSong != null) {
            if (sprite == null || status == false) {
                if (_questionMarkIcon == null) {
                    _questionMarkIcon = Resources.Load<Sprite>("Sprites/question_mark_item");
                }
                
                if(_questionMarkIcon != null) {
                    imgIconSong.sprite = _questionMarkIcon; // use question mark icon
                }
            } else {
                imgIconSong.sprite = sprite;
            }
        }
    }

    public override void SetData(IData data) { }
}
