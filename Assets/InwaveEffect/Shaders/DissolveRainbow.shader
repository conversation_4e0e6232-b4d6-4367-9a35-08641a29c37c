// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/DissolveRainbow"
{
	Properties
	{
		_TextureSample0("Texture Sample 0", 2D) = "white" {}
		_EdgeSize("Edge Size", Range( 0 , 1)) = 0.02875557
		_Alpha("Alpha", Float) = 0.45
		_EdgeOffset("Edge Offset", Float) = -0.99
		[HDR]_EdgeColor("Edge Color", Color) = (4,2.02353,0,1)
		_Emission("Emission", Color) = (0,0,0,1)
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
		[HideInInspector] __dirty( "", Int ) = 1
	}

	SubShader
	{
		Tags{ "RenderType" = "Transparent"  "Queue" = "Overlay+0" "IgnoreProjector" = "True" "IsEmissive" = "true"  }
		Cull Off
		ZWrite On
		ZTest LEqual
		Blend SrcAlpha OneMinusSrcAlpha
		CGPROGRAM
		#pragma target 3.0
		#pragma surface surf Lambert keepalpha noshadow 
		struct Input
		{
			float2 uv_texcoord;
			float3 worldNormal;
		};

		uniform sampler2D _TextureSample0;
		uniform float4 _TextureSample0_ST;
		uniform float4 _Emission;
		uniform float4 _EdgeColor;
		uniform float _Alpha;
		uniform float _EdgeOffset;
		uniform float _EdgeSize;

		void surf( Input i , inout SurfaceOutput o )
		{
			float2 uv_TextureSample0 = i.uv_texcoord * _TextureSample0_ST.xy + _TextureSample0_ST.zw;
			float4 tex2DNode1 = tex2D( _TextureSample0, uv_TextureSample0 );
			float3 ase_worldNormal = i.worldNormal;
			float3 ase_vertexNormal = mul( unity_WorldToObject, float4( ase_worldNormal, 0 ) );
			ase_vertexNormal = normalize( ase_vertexNormal );
			float temp_output_49_0 = ( ( 1.0 - _Alpha ) + _EdgeOffset );
			float smoothstepResult20 = smoothstep( ase_vertexNormal.z , temp_output_49_0 , ( temp_output_49_0 + _EdgeSize ));
			o.Emission = ( ( tex2DNode1 + _Emission ) + ( _EdgeColor * ( 1.0 - smoothstepResult20 ) ) ).rgb;
			float smoothstepResult25 = smoothstep( -0.04 , 0.16 , ( ase_vertexNormal.z + _Alpha ));
			o.Alpha = ( tex2DNode1.a * smoothstepResult25 );
		}

		ENDCG
	}
	CustomEditor "ASEMaterialInspector"
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.SimpleAddOpNode;34;-955.5805,51.68531;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.BreakToComponentsNode;29;-1401.437,25.39672;Inherit;True;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.RangedFloatNode;42;-832.7134,542.8834;Inherit;False;Constant;_Float0;Float 0;8;0;Create;True;0;0;0;False;0;False;0.44;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SmoothstepOpNode;20;-716.2518,340.726;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;36;-997.7037,319.7542;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;46;-1443.644,413.3405;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;49;-1216.494,421.3295;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;27;-1606.36,270.1915;Inherit;False;Property;_Alpha;Alpha;7;0;Create;True;0;0;0;False;0;False;0.45;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;47;-1434.559,501.0454;Inherit;False;Property;_EdgeOffset;Edge Offset;8;0;Create;True;0;0;0;False;0;False;-0.99;-0.99;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;17;-266.1975,-458.3296;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;54;-46.34678,-155.7076;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;51;-327.2168,-167.0461;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SmoothstepOpNode;25;-617.4753,38.18936;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;-0.04;False;2;FLOAT;0.16;False;1;FLOAT;0
Node;AmplifyShaderEditor.NormalVertexDataNode;66;-1769.673,27.18033;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;65;-253.5946,32.99322;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;26;-1463.782,-162.9886;Inherit;False;Property;_EdgeSize;Edge Size;6;0;Create;True;0;0;0;False;0;False;0.02875557;1;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;53;-575.7781,-383.3322;Inherit;False;Property;_Emission;Emission;11;0;Create;True;0;0;0;False;0;False;0,0,0,1;0,0.6032205,0.7975745,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;1;-683.8847,-180.0701;Inherit;True;Property;_TextureSample0;Texture Sample 0;5;0;Create;True;0;0;0;False;0;False;-1;c83c80d883c4fcf4fb552f29152560af;7bbfda7a6e9fcdb4b9557d5f3d9d1f7e;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.OneMinusNode;50;-445.4633,263.6311;Inherit;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;11;-581.7358,-599.8694;Inherit;False;Property;_EdgeColor;Edge Color;10;1;[HDR];Create;True;0;0;0;False;0;False;4,2.02353,0,1;0,3.025275,4,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StandardSurfaceOutputNode;70;221.0803,-168.7003;Float;False;True;-1;2;ASEMaterialInspector;0;0;Lambert;InwaveEffect/DissolveRainbow;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;False;False;False;False;False;False;Off;1;False;;3;False;;False;0;False;;0;False;;False;0;Custom;0.5;True;False;0;False;Transparent;;Overlay;All;12;all;True;True;True;True;0;False;;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;2;15;10;25;False;0.5;False;2;5;False;;10;False;;0;0;False;;0;False;;0;False;;0;False;;0;False;0;0,0,0,0;VertexOffset;True;False;Cylindrical;False;True;Relative;0;;-1;-1;-1;-1;0;False;0;0;False;;-1;0;False;;0;0;0;False;0.1;False;;0;False;;False;15;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT;0;False;9;FLOAT;0;False;10;FLOAT;0;False;13;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;12;FLOAT3;0,0,0;False;14;FLOAT4;0,0,0,0;False;15;FLOAT3;0,0,0;False;0
WireConnection;34;0;29;2
WireConnection;34;1;27;0
WireConnection;29;0;66;0
WireConnection;20;0;36;0
WireConnection;20;1;29;2
WireConnection;20;2;49;0
WireConnection;36;0;49;0
WireConnection;36;1;26;0
WireConnection;46;0;27;0
WireConnection;49;0;46;0
WireConnection;49;1;47;0
WireConnection;17;0;11;0
WireConnection;17;1;50;0
WireConnection;54;0;51;0
WireConnection;54;1;17;0
WireConnection;51;0;1;0
WireConnection;51;1;53;0
WireConnection;25;0;34;0
WireConnection;65;0;1;4
WireConnection;65;1;25;0
WireConnection;50;0;20;0
WireConnection;70;2;54;0
WireConnection;70;9;65;0
ASEEND*/
//CHKSM=42B74E79A37F1EF2695DCAB6E7D718167E826B03