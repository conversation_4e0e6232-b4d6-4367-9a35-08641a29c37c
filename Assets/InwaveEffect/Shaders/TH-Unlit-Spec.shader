// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/TH-Unlit-Spec"
{
	Properties
	{
		[HDR]_ColorMark("Color Mark", Color) = (1,1,1,1)
		[HDR]_ColorTile1("Color Tile 1", Color) = (1,1,1,1)
		_ColorTile2("Color Tile 2", Color) = (1,1,1,1)
		_MainTexture("Main Texture", 2D) = "white" {}
		_OpacityTexture("Opacity Texture", 2D) = "white" {}
		_Cubemap("Cubemap", CUBE) = "white" {}
		_Opacity("Opacity", Range( 0 , 1)) = 1
		[Toggle(_RIMPLIGHT_ON)] _RimpLight("Rimp Light", Float) = 1
		_RimpColor("Rimp Color", Color) = (0.004716992,0.7491221,1,0)
		_RimpPower("Rimp Power", Float) = 1
		_RimpScale("Rimp Scale", Float) = 2
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
		[HideInInspector] __dirty( "", Int ) = 1
	}

	SubShader
	{
		Tags{ "RenderType" = "Custom"  "Queue" = "Transparent+0" "IgnoreProjector" = "True" "IsEmissive" = "true"  }
		Cull Off
		Blend SrcAlpha OneMinusSrcAlpha
		
		CGPROGRAM
		#pragma target 3.0
		#pragma shader_feature_local _RIMPLIGHT_ON
		#pragma surface surf Unlit keepalpha noshadow 
		struct Input
		{
			float2 uv_texcoord;
			float3 worldPos;
			float3 worldNormal;
			float3 worldRefl;
			INTERNAL_DATA
		};

		uniform float4 _ColorMark;
		uniform sampler2D _MainTexture;
		uniform float4 _MainTexture_ST;
		uniform float4 _ColorTile1;
		uniform float4 _ColorTile2;
		uniform float4 _RimpColor;
		uniform float _RimpPower;
		uniform float _RimpScale;
		uniform samplerCUBE _Cubemap;
		uniform sampler2D _OpacityTexture;
		uniform float4 _OpacityTexture_ST;
		uniform float _Opacity;

		inline half4 LightingUnlit( SurfaceOutput s, half3 lightDir, half atten )
		{
			return half4 ( 0, 0, 0, s.Alpha );
		}

		void surf( Input i , inout SurfaceOutput o )
		{
			float2 uv_MainTexture = i.uv_texcoord * _MainTexture_ST.xy + _MainTexture_ST.zw;
			float4 break30 = tex2D( _MainTexture, uv_MainTexture );
			float4 temp_cast_0 = (0.0).xxxx;
			float3 ase_worldPos = i.worldPos;
			float3 ase_worldViewDir = normalize( UnityWorldSpaceViewDir( ase_worldPos ) );
			float3 ase_worldNormal = i.worldNormal;
			float fresnelNdotV84 = dot( ase_worldNormal, ase_worldViewDir );
			float fresnelNode84 = ( 0.0 + _RimpPower * pow( max( 1.0 - fresnelNdotV84 , 0.0001 ), _RimpScale ) );
			#ifdef _RIMPLIGHT_ON
				float4 staticSwitch90 = ( _RimpColor * fresnelNode84 );
			#else
				float4 staticSwitch90 = temp_cast_0;
			#endif
			float3 ase_worldReflection = i.worldRefl;
			o.Emission = ( ( ( ( _ColorMark * break30.r ) + ( _ColorTile1 * break30.g ) + ( _ColorTile2 * break30.b ) ) + staticSwitch90 ) + ( ( 1.0 - 0.5 ) * texCUBE( _Cubemap, ase_worldReflection ) ) ).rgb;
			float2 uv_OpacityTexture = i.uv_texcoord * _OpacityTexture_ST.xy + _OpacityTexture_ST.zw;
			o.Alpha = ( tex2D( _OpacityTexture, uv_OpacityTexture ).a * _Opacity );
		}

		ENDCG
	}
	CustomEditor "ASEMaterialInspector"
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.SamplerNode;22;-1783.748,-878.4249;Inherit;True;Property;_MainTexture;Main Texture;3;0;Create;True;0;0;0;False;0;False;-1;None;e0f6e59d0a4fd224bad8149d1ceb03bf;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.BreakToComponentsNode;30;-1487.727,-866.6819;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;37;-1042.911,-870.7535;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;31;-1063.909,-1109.357;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;39;-1046.248,-647.9782;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;38;-1489.88,-698.6554;Inherit;False;Property;_ColorTile1;Color Tile 1;1;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.2688679,0.7102035,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;32;-1519.717,-1105.694;Inherit;False;Property;_ColorMark;Color Mark;0;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.5188679,0.5188679,0.5188679,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;86;-859.4446,-266.9221;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;42;-713.1899,-763.643;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;87;-1166.245,-377.4228;Inherit;False;Property;_RimpColor;Rimp Color;10;0;Create;True;0;0;0;False;0;False;0.004716992,0.7491221,1,0;0.3529412,0.957868,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;89;-1436.645,-39.12267;Inherit;False;Property;_RimpPower;Rimp Power;12;0;Create;True;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;92;-1423.645,40.1773;Inherit;False;Property;_RimpScale;Rimp Scale;13;0;Create;True;0;0;0;False;0;False;2;2;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.FresnelNode;84;-1183.578,-138.8869;Inherit;False;Standard;TangentNormal;ViewDir;False;True;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;5;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;91;-776.2455,-393.0228;Inherit;False;Constant;_Float0;Float 0;9;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;29;-1798.567,-587.1912;Inherit;True;Property;_OpacityTexture;Opacity Texture;5;0;Create;True;0;0;0;False;0;False;-1;None;3c2316bd473a184469022600eab9dd49;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;83;-1366.584,-503.3187;Inherit;False;Property;_ColorTile2;Color Tile 2;2;0;Create;True;0;0;0;False;0;False;1,1,1,1;0.6,0.3345942,0.9716981,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;108;-1790.257,-375.4737;Inherit;False;Property;_Opacity;Opacity;7;0;Create;True;0;0;0;False;0;False;1;1;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;90;-639.0836,-310.7841;Inherit;False;Property;_RimpLight;Rimp Light;9;0;Create;True;0;0;0;False;0;False;0;1;1;True;;Toggle;2;Key0;Key1;Create;True;True;All;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;85;-388.6379,-489.5337;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;120;-144.2927,21.0238;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;109;-1498.303,-447.5699;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GrabScreenPosition;107;-1715.966,-250.4017;Inherit;False;0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WorldReflectionVector;111;-1112.484,403.4922;Inherit;False;False;1;0;FLOAT3;0,0,0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.OneMinusNode;112;-648.4852,291.4923;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;114;-856.4852,403.4922;Inherit;True;Property;_Cubemap;Cubemap;6;0;Create;True;0;0;0;False;0;False;-1;None;937d2c8637f646741837892225c32d55;True;0;False;white;Auto;False;Object;-1;Auto;Cube;8;0;SAMPLERCUBE;0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;1;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;115;-520.4852,451.4922;Inherit;True;Property;_Metallic;Metallic;8;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;1;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;116;-456.4851,291.4923;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;118;-861.6858,285.7924;Float;False;Constant;_Float1;Float 0;-1;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.StandardSurfaceOutputNode;70;0,0;Float;False;True;-1;2;ASEMaterialInspector;0;0;Unlit;InwaveEffect/TH-Unlit-Spec;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;False;False;False;False;False;False;Off;0;False;;0;False;;False;0;False;;0;False;;False;5;Custom;0.5;True;False;0;True;Custom;;Transparent;All;12;all;True;True;True;True;0;False;;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;2;15;10;25;False;0.5;False;2;5;False;;10;False;;0;5;False;;10;False;;0;False;;4;False;;0;False;0;0,0,0,0;VertexOffset;True;False;Cylindrical;False;True;Relative;0;;11;-1;-1;-1;0;False;0;0;False;;-1;0;False;;0;0;0;False;0.1;False;;0;False;;False;15;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT;0;False;9;FLOAT;0;False;10;FLOAT;0;False;13;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;12;FLOAT3;0,0,0;False;14;FLOAT4;0,0,0,0;False;15;FLOAT3;0,0,0;False;0
Node;AmplifyShaderEditor.SamplerNode;110;-1448.484,163.4924;Inherit;True;Property;_Normals;Normals;4;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;True;bump;Auto;True;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;1;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
WireConnection;30;0;22;0
WireConnection;37;0;38;0
WireConnection;37;1;30;1
WireConnection;31;0;32;0
WireConnection;31;1;30;0
WireConnection;39;0;83;0
WireConnection;39;1;30;2
WireConnection;86;0;87;0
WireConnection;86;1;84;0
WireConnection;42;0;31;0
WireConnection;42;1;37;0
WireConnection;42;2;39;0
WireConnection;84;2;89;0
WireConnection;84;3;92;0
WireConnection;90;1;91;0
WireConnection;90;0;86;0
WireConnection;85;0;42;0
WireConnection;85;1;90;0
WireConnection;120;0;85;0
WireConnection;120;1;116;0
WireConnection;109;0;29;4
WireConnection;109;1;108;0
WireConnection;112;0;118;0
WireConnection;114;1;111;0
WireConnection;116;0;112;0
WireConnection;116;1;114;0
WireConnection;70;2;120;0
WireConnection;70;9;109;0
ASEEND*/
//CHKSM=E7AF3445F2DEDECE68EDF4886F4B06FF24DFE2AE