using System.Security.Cryptography;
using System.Text;
using UnityEngine;

public class SecuredPlayerPrefs : MonoBehaviour {
    private static readonly StringBuilder _stringBuilder = new();
    
    private const string privateKey = "inwave-secured-pref-d98f23hsdu9e";

    public static string[] keys = {
        "uihfdgshdg934dhg",
        "983hg0qeghrq398h",
        "u8fdn9qr9qtgh3ed",
        "jq324e89sghe9rfg"
    };
    
    public static string Md5(string strToEncrypt) {
        UTF8Encoding ue = new UTF8Encoding();
        byte[] bytes = ue.GetBytes(strToEncrypt);
 
        MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
        byte[] hashBytes = md5.ComputeHash(bytes);
 
        _stringBuilder.Clear();
 
        for (int i = 0; i < hashBytes.Length; i++) {
            _stringBuilder.Append(System.Convert.ToString(hashBytes[i], 16).PadLeft(2, '0'));
        }
 
        return _stringBuilder.ToString().PadLeft(32, '0');
    }
    
    /// <summary>
    /// Lưu lại chuỗi Hash MD5 để có thể kiểm tra tính đúng đắn của dữ liệu lưu trong PlayerPref
    /// </summary>
    /// <param name="key"></param>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public static void SaveEncryption(string key, string type, string value) {
        int keyIndex = (int)Mathf.Floor(Random.value * keys.Length);
        string secretKey = keys[keyIndex];
        string check = Md5(Util.BuildString('_', type, privateKey, secretKey, value));
        PlayerPrefs.SetString(EncryptionKey(key), check);
        PlayerPrefs.SetInt(UsedKey(key), keyIndex);
    }
    
    /// <summary>
    /// Kiểm tra giá trị trong PlayerPref đã bị sửa đổi từ bên ngoài hay chưa
    /// </summary>
    /// <param name="key"></param>
    /// <param name="type"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public static bool CheckEncryption(string key, string type, string value) {
        int keyIndex = PlayerPrefs.GetInt(UsedKey(key));
        string secretKey = keys[keyIndex];
        string check = Md5(Util.BuildString('_', type, privateKey, secretKey, value));
        if(!HasEncryptionKey(key)) return false;
        string storedCheck = PlayerPrefs.GetString(EncryptionKey(key));
        return storedCheck == check;
    }

    #region PlayerPref Methods

    /// <summary>
    /// Lưu data kiểu Int vào PlayerPref và lưu data mã hóa
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    public static void SetInt(string key, int value) {
        PlayerPrefs.SetInt(key, value);
        SaveEncryption(key, "int", value.ToString());
    }
    
    /// <summary>
    /// Lưu data kiểu Float vào PlayerPref và lưu data mã hóa
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    public static void SetFloat(string key, float value) {
        PlayerPrefs.SetFloat(key, value);
        SaveEncryption(key, "float", Mathf.Floor(value*1000).ToString());
    }
    
    /// <summary>
    /// Lưu data kiểu String vào PlayerPref và lưu data mã hóa
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    public static void SetString(string key, string value) {
        PlayerPrefs.SetString(key, value);
        SaveEncryption(key, "string", value);
    }

    /// <summary>
    /// Lấy data đã mã hóa từ PlayerPref, nếu giá trị trong PlayerPref đã bị sửa từ bên ngoài
    /// thì trả về giá trị default thay vì trả về giá trị đã bị hacker sửa.
    /// </summary>
    /// <param name="key"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static int GetInt(string key, int defaultValue = 0) {
        int value = PlayerPrefs.GetInt(key);
        if(!CheckEncryption(key, "int", value.ToString())) return defaultValue;
        return value;
    }
    
    public static float GetFloat(string key, float defaultValue = 0f) {
        float value = PlayerPrefs.GetFloat(key);
        if(!CheckEncryption(key, "float", Mathf.Floor(value*1000).ToString())) return defaultValue;
        return value;
    }
    
    public static string GetString(string key, string defaultValue = "") {
        string value = PlayerPrefs.GetString(key);
        if(!CheckEncryption(key, "string", value)) return defaultValue;
        return value;
    }
    
    public static bool HasKey(string key) {
        return PlayerPrefs.HasKey(key);
    }

    /// <summary>
    /// Kiểm tra một PlayerPref key đã được mã hóa hay chưa
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static bool HasEncryptionKey(string key) {
        return PlayerPrefs.HasKey(EncryptionKey(key));
    }
    
    public static void DeleteKey(string key) {
        PlayerPrefs.DeleteKey(key);
        PlayerPrefs.DeleteKey(EncryptionKey(key));
        PlayerPrefs.DeleteKey(UsedKey(key));
    }

    /// <summary>
    /// Lấy PlayerPref key mã hóa tương ứng
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    private static string EncryptionKey(string key) {
        return Util.BuildString('_', key, "hashed");
    }

    /// <summary>
    /// PlayerPref-key cho biết index của secured-key được sử dụng để mã hóa
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    private static string UsedKey(string key) {
        return Util.BuildString('_', key, "key");
    }

    #endregion
}
