using UnityEditor;
using UnityEngine;

public class ClosePopup : Editor {
    [MenuItem("Trungvt/ClosePopup")]
    static void StartClosePopup() {
        EditorWindow focusedWindow = EditorWindow.focusedWindow;
        if (focusedWindow == null) {
            return;
        }

        if (!focusedWindow.docked) {
            Debug.Log("ClosePopup: " + focusedWindow.titleContent.text);
            focusedWindow.Close();
        }
    }
}