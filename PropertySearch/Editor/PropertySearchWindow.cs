// Decompiled with JetBrains decompiler
// Type: GameSavvy.PropertySearch.PropertySearchWindow
// Assembly: PropertySearch, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: D9E32750-8AA0-42CC-BD69-6EC0DB6C7435
// Assembly location: D:\Projects\InWave\BeatJumper\Assets\Trungvt\Editor\GameSavvy\PropertySearch\PropertySearch.dll

using System;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace GameSavvy.PropertySearch {
    [ExecuteInEditMode]
    public class PropertySearchWindow : EditorWindow {
        private PropertyObjects _FilteredObjects;
        private Vector2 _ScrollPosition;
        private string _SearchString="";
        private string _LastSearchString;
        private string _PropertyNameFilter="";
        private string _ComponentNameFilter="";
        private string _GameObjectNameFilter="";
        private int _FrameIndex;
        private bool _ExpandedAll;
        public bool ExpandedGOs;
        public bool ExpandedComps;

        [MenuItem("Tools/Property Search/Show Window %&#p")]
        public static void OpenWindow() {
            PropertySearchWindow window = EditorWindow.GetWindow<PropertySearchWindow>();
            ((EditorWindow) window).titleContent.text = ("Property Search");
            ((EditorWindow) window).Show();
        }

        private void OnEnable() {
            this._FilteredObjects = new PropertyObjects(this);
            Selection.selectionChanged = (Action) Delegate.Combine((Delegate) Selection.selectionChanged,
                (Delegate) new Action(this.UpdateContext));
        }

        private void OnDisable() {
            Selection.selectionChanged = (Action) Delegate.Remove((Delegate) Selection.selectionChanged,
                (Delegate) new Action(this.UpdateContext));
        }

        [ExecuteAlways]
        private void OnGUI() {
            GUILayout.BeginVertical(Array.Empty<GUILayoutOption>());
            GUILayout.Space(8f);
            this.DrawCollapseExpandButtons();
            GUILayout.Space(4f);
            if (this.ReadInputString() && this.UpdateFilterStrings())
                this.UpdateContext();
            this.DisplayContext();
            GUILayout.EndVertical();
        }

        private void Update() {
            this._FrameIndex = (this._FrameIndex + 1) % 10;
            if (this._FrameIndex != 0)
                return;
            this.Repaint();
        }

        private void DrawCollapseExpandButtons() {
            this._ExpandedAll = this.ExpandedComps && this.ExpandedGOs;
            GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
            if (GUILayout.Button((this._ExpandedAll ? "><" : "<>") + " All", Array.Empty<GUILayoutOption>()))
                this.ExpandCollapseAll();
            if (GUILayout.Button((this.ExpandedGOs ? "><" : "<>") + " GameObjects", Array.Empty<GUILayoutOption>()))
                this.ExpandCollapseGameObjects();
            if (GUILayout.Button((this.ExpandedComps ? "><" : "<>") + " Components", Array.Empty<GUILayoutOption>()))
                this.ExpandCollapseComponents();
            GUILayout.EndHorizontal();
            this.ExpandedComps = true;
            this.ExpandedGOs = true;
        }

        private void ExpandCollapseComponents() {
            this.ExpandedComps = !this.ExpandedComps;
            foreach (ObjectComponents objectComponents in this._FilteredObjects.Objs) {
                foreach (ComponentProperties comp in objectComponents.Comps)
                    comp.ExpandCollapse(this.ExpandedComps);
            }
        }

        private void ExpandCollapseGameObjects() {
            this.ExpandedGOs = !this.ExpandedGOs;
            foreach (ObjectComponents objectComponents in this._FilteredObjects.Objs)
                objectComponents.ExpandCollapse(this.ExpandedGOs);
        }

        private void ExpandCollapseAll() {
            this.ExpandedGOs = !this._ExpandedAll;
            this.ExpandedComps = !this._ExpandedAll;
            foreach (ObjectComponents objectComponents in this._FilteredObjects.Objs) {
                objectComponents.ExpandCollapse(this.ExpandedGOs);
                foreach (ComponentProperties comp in objectComponents.Comps)
                    comp.ExpandCollapse(this.ExpandedComps);
            }
        }

        private bool ReadInputString() {
            GUI.SetNextControlName("PropertySearchText");
            this.SearchStringTextField();
            this.PrintFilterStrings();
            if (this._LastSearchString == this._SearchString)
                return false;
            this._LastSearchString = this._SearchString;
            return true;
        }

        private void SearchStringTextField() {
            GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
            this._SearchString = GUILayout.TextField(this._SearchString, Array.Empty<GUILayoutOption>()).ToLower()
                .Replace(" ", "");
            if (GUILayout.Button("X", new GUILayoutOption[2] {
                GUILayout.MaxWidth(22f),
                GUILayout.MaxHeight(16f)
            }))
                this._SearchString = "";
            GUILayout.EndHorizontal();
        }

        private void PrintFilterStrings() {
            string str1 = this._GameObjectNameFilter.Length > 0 ? this._GameObjectNameFilter : "*";
            string str2 = this._ComponentNameFilter.Length > 0 ? this._ComponentNameFilter : "*";
            string str3 = this._PropertyNameFilter.Replace("t:", "");
            string str4 = str3.Length > 0 ? str3 : "*";
            string str5 = this._PropertyNameFilter.Contains("t:") ? "<" + str4 + ">" : "[" + str4 + "]";
            GUILayout.Label("[" + str1 + "].[" + str2 + "]." + str5, Array.Empty<GUILayoutOption>());
            GUILayout.Space(2f);
        }

        private bool UpdateFilterStrings() {
            this._GameObjectNameFilter = "";
            this._ComponentNameFilter = "";
            this._PropertyNameFilter = "";
            string[] strArray = this._SearchString.Split('.');
            int length = strArray.Length;
            if (length == 1)
                this._PropertyNameFilter = strArray[0].Clean().Trim();
            else if (length == 2) {
                this._ComponentNameFilter = strArray[0].Clean().Trim();
                this._PropertyNameFilter = strArray[1].Clean().Trim();
            } else {
                if (length < 3)
                    return false;
                this._GameObjectNameFilter = strArray[0].Clean().Trim();
                this._ComponentNameFilter = strArray[1].Clean().Trim();
                this._PropertyNameFilter = strArray[2].Clean().Trim();
            }

            return true;
        }

        private void PrintContext() {
            foreach (ObjectComponents objectComponents in this._FilteredObjects.Objs) {
                string str = ((Object) objectComponents.GObj).name + "\n";
                foreach (ComponentProperties comp in objectComponents.Comps) {
                    str = str + " - " + ((object) comp.Comp).GetType().Name + "\n";
                    foreach (string prop in comp.Props)
                        str = str + "    * " + prop;
                }

                Debug.Log((object) str);
            }
        }

        private void DisplayContext() {
            this._ScrollPosition =
                EditorGUILayout.BeginScrollView(this._ScrollPosition, Array.Empty<GUILayoutOption>());
            try {
                foreach (ObjectComponents objectComponents in this._FilteredObjects.Objs) {
                    GUILayout.BeginVertical(new GUIStyle("Box"), Array.Empty<GUILayoutOption>());
                    objectComponents.DrawGameObjectWithComponents();
                    GUILayout.EndVertical();
                    GUILayout.Space(4f);
                }
            } catch (ArgumentException ex) {
                this.UpdateContext();
            } finally {
                EditorGUILayout.EndScrollView();
            }
        }

        private void UpdateContext() {
            this._FilteredObjects = new PropertyObjects(this);
            this._FilteredObjects.FilterObjs(Selection.objects, this._GameObjectNameFilter, this._ComponentNameFilter,
                this._PropertyNameFilter);
            this._FilteredObjects.RemoveEmptyObjs();
        }
    }
}